import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import * as Highcharts from 'highcharts';
import { CommonService } from '../../../common.service';
import HC_exporting from 'highcharts/modules/exporting';
import { TranslateModule } from '@ngx-translate/core';
import HC_exportData from 'highcharts/modules/export-data';
import HC_accessibility from 'highcharts/modules/accessibility';
import { Options } from 'highcharts';
import { HighchartsChartModule } from 'highcharts-angular';
import { OutsideClickDirective } from 'src/app/scad-insights/core/directives/outsideClick.directive';
import { SubSink } from "subsink";
import { MatTooltipModule } from '@angular/material/tooltip';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { geoMapKeys } from '../../../geospatial.contants';
import { Store } from '@ngrx/store';
import { setEmailUpdate, setNotificationUpdate, unsubscribeNotificationUpdate } from 'src/app/scad-insights/store/notification/notification.action';
import { selectGetMappedData } from 'src/app/scad-insights/store/notification/notification.selector';
import { distinctUntilChanged } from 'rxjs';
import { IfpAlertBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-alert-box/ifp-alert-box.component';
import { EmptyChartComponent } from '../empty-chart/empty-chart.component';
import { AlertBoxService } from 'src/app/scad-insights/core/services/alert-box.service';
import { CensusServiceService } from 'src/app/scad-insights/geospatial-revamp/census-service.service';
import { round } from 'lodash';
import { IfpTagComponent } from "../../../../../ifp-analytics/atom/ifp-tag/ifp-tag.component";
import { IfpBackButtonComponent } from "../../../../../ifp-widgets/atoms/ifp-back-button/ifp-back-button.component";

HC_exporting(Highcharts);
HC_exportData(Highcharts);
HC_accessibility(Highcharts);

interface MultiSelect {
  SELECT_CODE: number;
  SELECT_EN: string;
  SELECT_AR: string;
}

interface Security {
  id: number;
  label: string;
  name: string;
}

interface IndicatorItem {
  title: string;
  indicator_value: string;
  domain_id: number;
  card_position: string | null;
  light_icon: string;
  dark_icon: string;
  security: Security;
}


@Component({
  selector: 'ifp-insight-chart',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    HighchartsChartModule,
    HighchartsChartModule,
    OutsideClickDirective,
    MatTooltipModule,
    IfpAlertBoxComponent,
    EmptyChartComponent,
    TranslateModule,
    IfpTagComponent,
    IfpBackButtonComponent
  ],
  templateUrl: './insight-chart.component.html',
  styleUrl: './insight-chart.component.scss'
})
export class InsightChartComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges, AfterViewChecked {

  @ViewChild('outsideRef') outsideRef!: ElementRef;
  @ViewChild('alertBox') alertBox!: ElementRef;
  @ViewChild('card') card!: ElementRef;

  @Output() filterChange = new EventEmitter();
  @Output() regionChangeFromChart = new EventEmitter();
  @Output() triggerResetFilter = new EventEmitter();

  @Input() chartStats: any = [];
  @Input() indicatorsList: any = [];
  @Input() indicatorsListValues: any = {};
  @Input() totalPopulation: number = 0;
  @Input() eodPopulation: number = 0;
  @Input() totalBuildings: number = 0;
  @Input() totalNonCitizinPopulation: number = 0;
  @Input() totalPopulation15years: number = 0;
  @Input() totalUnits: number = 0;
  @Input() totalJobSeekersEmirati: number = 0;
  @Input() chartType: string = 'bar';
  @Input() isCustomize: boolean = false;
  @Input() height: number = 200;
  @Input() width: number = 200;
  @Input() domain_id: number = 1;
  @Input() enableBackButton: boolean = false;

  private subsink: SubSink = new SubSink();

  //Common chart variables
  public chartTitle = '';
  public chartIcon = 'group.svg';
  public chartClassification = '';
  public chartDataNotEmpty: boolean = true;
  public chartIconWidth = 2.8;
  public chartIndicator = '';
  public chartData: any = [];
  public pieChartData: any = [];
  public barChartData: any = [];
  public barChartCategories: any = [];
  public realEstateTotal: number = 0;
  public populationGrowthYOY: number = 0;
  public populationGrowthQOQ: number = 0;
  public lastQuarter: string = '';
  public previousQuarter: string = '';
  public lastYear: number = 0;
  public previousYear: number = 0;
  public filterObject: any = geoMapKeys.defaultQueryParams;
  public showPieChart: boolean = false;
  public piechartLoading = false;
  public barChartLoading = false;
  public enableSettingsIcon = true;

  // Citizenship variables
  public selectedFilters: MultiSelect[] = [];
  public citizenshipStats = {
    emirati: 0,
    nonEmirati: 0,
    emiratiCode: geoMapKeys.citizenshipIndicator.emiratiCode,
    nonEmiratiCode: geoMapKeys.citizenshipIndicator.nonEmiratiCode,
  };

  // Pouplation Chart type flags
  public itsTotalChart: boolean = false;
  public itsGenderChart: boolean = false;
  public itsCitizenChart: boolean = false;
  public itsEductionChart: boolean = false;
  public itsRegionChart: boolean = false;
  public itsMaritalChart: boolean = false;
  public itsDisiabilityChart: boolean = false;
  public itsHouseHoldTypeChart: boolean = false;
  public itsHouseHoldSizeChart: boolean = false;
  public itsPopulationOver15Chart: boolean = false;
  public itsPopulationByReligionChart: boolean = false;
  public itsPopulationNonCitizinChart: boolean = false;
  public itsPopulationByEnrollmentChart: boolean = false;
  public itsPopulationBySpecializationChart: boolean = false;
  public itsAgeDependancyRatioChart: boolean = false;
  public itsAgeDependancyRatioCitizinChart: boolean = false;
  public itsAgeDependancyRatioGenderChart: boolean = false;
  public itsTop10NationalitiesChart: boolean = false;
  public itsPopulationGrowthYOYChart: boolean = false;
  public itsPopulationGrowthQOQChart: boolean = false;
  public itsPopulationGrowthGenderYOYChart: boolean = false;
  public itsPopulationGrowthGenderQOQChart: boolean = false;
  public itsPopulationGrowthCitizenYOYChart: boolean = false;
  public itsPopulationGrowthCitizenQOQChart: boolean = false;

  // Laber Force Chart type flags
  public itsJobSeekersAgeChart: boolean = false;
  public itsTotalJobSeekersEmiratiChart: boolean = false;
  public itsEmployedSectorChart: boolean = false;
  public itsJobSeekerOccupation: boolean = false;
  public itsJobSeekerEducation: boolean = false;
  public itsEmployedEcoActivity: boolean = false;

  // Real State Chart type flags
  public itsBuildingTypeChart: boolean = false;
  public itsBuildingTotalChart: boolean = false;
  public itsUnitsTotalChart: boolean = false;
  public itsBuildingUseChart: boolean = false;
  public itsHousingUnitTypeChart: boolean = false;
  public itsUnitUse: boolean = false;
  public itsUnitType: boolean = false;
  public itsDetachedHousingUnitType: boolean = false;

  public quartersCategories: any = [];
  public dataLabelColor: string = '#000000';
  public chartOptions!: Options;
  public chartSettingsShow: boolean = false;

  public language: string = 'en';


  isToolTip: boolean = true;
  highcharts: typeof Highcharts = Highcharts;
  public isCollapsed: boolean = false;
  public compareLineChartData!: any;
  public compareChartCategories: string[] = [];
  public isChangeUpdate: boolean = false;
  public showChart: boolean = false;
  public indicatorId!: string;
  public isNotificationEnabled: boolean = false;
  public isCompareEnabled: boolean = false;
  public alertTimeOutInSec: number = 5;
  public isSubscNotifOpen: boolean = false;
  public currentLang: string = '';
  public disableNonEmiratiDot: boolean = true;
  public disableEmiratiDot: boolean = true;

  constructor(
    private _cdr: ChangeDetectorRef,
    private themeService: ThemeService,
    public commonService: CommonService,
    private gisSharedService: CensusServiceService,
    private store: Store,
    private readonly _alertBoxService: AlertBoxService
  ) {
    this.themeService.defaultLang$.subscribe((lang: string) => {
      this.language = lang;
    })
  }



  ngOnChanges(changes: SimpleChanges) {
    if (changes['chartStats']) {
      this.showChart = false;
      this.indicatorId = this.chartStats.data[0].INDICATOR_ID;
      if (this.indicatorId) {
        this.checkNotification();
      }
      if (this.chartStats.data.length > 0 && !this.commonService.isCompareEnabled) {
        this.chartType = 'progress';
        this.initChartData();
      } else if (this.chartStats.data.length > 0 && this.commonService.isCompareEnabled) {
        this.initChartData();
        this.createCompareChartData();
      }
    }
  }

  ngOnInit(): void {

    this.subsink.add(
      this.themeService.defaultTheme$.subscribe(val => {
        if (val == 'dark') {
          this.dataLabelColor = '#ccc';
        } else {
          this.dataLabelColor = '#000000';
        }
      })
    );
  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
  }


  changeChartType(chartType: string) {
    this.chartType = chartType;
    this.enableChartSettings();

    if (this.chartType == 'pie' || this.chartType == 'dougnut') {
      this.createPieChart();
    } else if (this.chartType === 'bar') {
      const validData = this.chartData.filter(
        (x: { POPULATION?: number; VALUE?: number }) =>
          (x.POPULATION ?? 0) > 0 || (x.VALUE ?? 0) > 0
      );

      this.barChartData = validData.map((x: any) => x.POPULATION ?? x.VALUE);
      this.barChartCategories = validData.map((x: any) => this.language == "ar" ? x.AR : x.EN);
      this.quartersCategories = validData.map((x: any) => this.language == "ar" ? x.AR : x.EN);

      this.createBarChart();
    }

  }


  initChartData() {

    this.chartIndicator = this.chartStats.indicator;
    this.itsTotalChart = this.chartIndicator == this.indicatorsListValues.TOTAL || this.chartIndicator === this.indicatorsListValues.TOTAL_EMP;
    this.itsCitizenChart = this.chartIndicator == this.indicatorsListValues.CITIZENS;
    this.itsGenderChart = this.chartIndicator == this.indicatorsListValues.GENDER;
    this.itsEductionChart = this.chartIndicator == this.indicatorsListValues.EDUCATION;
    this.itsRegionChart = this.chartIndicator == this.indicatorsListValues.REGION;
    this.itsMaritalChart = this.chartIndicator == this.indicatorsListValues.MARITAL;
    this.itsDisiabilityChart = this.chartIndicator == this.indicatorsListValues.DISABILITY_TYPE;
    this.itsHouseHoldTypeChart = this.chartIndicator == this.indicatorsListValues.HOUSEHOLD_TYPE;
    this.itsHouseHoldSizeChart = this.chartIndicator == this.indicatorsListValues.AVERAGE_HOUSEHOLD_SIZE;
    this.itsPopulationOver15Chart = this.chartIndicator == this.indicatorsListValues.POPULATION_OVER_15;
    this.itsPopulationByReligionChart = this.chartIndicator == this.indicatorsListValues.POPULATION_BY_RELIGION;
    this.itsPopulationNonCitizinChart = this.chartIndicator == this.indicatorsListValues.POPULATION_NON_CITIZEN;
    this.itsPopulationByEnrollmentChart = this.chartIndicator == this.indicatorsListValues.POPULATION_BY_ENROLLMENT;
    this.itsPopulationBySpecializationChart = this.chartIndicator == this.indicatorsListValues.POPULATION_BY_SPECIALIZATION;
    this.itsTop10NationalitiesChart = this.chartIndicator == this.indicatorsListValues.POPULATION_BY_NATIONALITY;
    this.itsAgeDependancyRatioChart = this.chartIndicator == this.indicatorsListValues.AGE_DEPENDENCY_RATIO;
    this.itsAgeDependancyRatioCitizinChart = this.chartIndicator == this.indicatorsListValues.AGE_DEPENDENCY_RATIO_CITIZEN;
    this.itsAgeDependancyRatioGenderChart = this.chartIndicator == this.indicatorsListValues.AGE_DEPENDENCY_RATIO_GENDER;
    this.itsBuildingTotalChart = this.chartIndicator == this.indicatorsListValues.BUILDING_TOTAL;
    this.itsUnitsTotalChart = this.chartIndicator == this.indicatorsListValues.UNIT_TOTAL;
    this.itsBuildingUseChart = this.chartIndicator == this.indicatorsListValues.BUILDING_USE;
    this.itsJobSeekersAgeChart = this.chartIndicator == this.indicatorsListValues.JOB_SEEKER_BY_AGE_GROUP;
    this.itsTotalJobSeekersEmiratiChart = this.chartIndicator == this.indicatorsListValues.TOTAL_JOB_SEEKERS_EMIRATI;
    this.itsEmployedSectorChart = this.chartIndicator == this.indicatorsListValues.EMPLOYED_BY_SECTOR;
    this.itsJobSeekerOccupation = this.chartIndicator == this.indicatorsListValues.EMPLOYED_BY_OCCU_LEVEL;
    this.itsJobSeekerEducation = this.chartIndicator == this.indicatorsListValues.JOB_SEEKER_BY_EDUCATION_LEVEL;
    this.itsEmployedEcoActivity = this.chartIndicator == this.indicatorsListValues.EMPLOYED_BY_ECO_ACTIVITY;
    this.itsHousingUnitTypeChart = this.chartIndicator == this.indicatorsListValues.HOUSING_UNIT_TYPE;
    this.itsUnitUse = this.chartIndicator == this.indicatorsListValues.UNIT_USE;
    this.itsUnitType = this.chartIndicator == this.indicatorsListValues.UNIT_TYPE;
    this.itsBuildingTypeChart = this.chartIndicator == this.indicatorsListValues.BUILDING_TYPE;
    this.itsDetachedHousingUnitType = this.chartIndicator == this.indicatorsListValues.DETACHED_HOUSING_UNIT_TYPE;
    this.itsPopulationGrowthYOYChart = this.chartIndicator == this.indicatorsListValues.POPULATION_GROWTH_YOY;
    this.itsPopulationGrowthQOQChart = this.chartIndicator == this.indicatorsListValues.POPULATION_GROWTH_QOQ;
    this.itsPopulationGrowthGenderYOYChart = this.chartIndicator == this.indicatorsListValues.POPULATION_GROWTH_GENDER_YOY;
    this.itsPopulationGrowthGenderQOQChart = this.chartIndicator == this.indicatorsListValues.POPULATION_GROWTH_GENDER_QOQ;
    this.itsPopulationGrowthCitizenYOYChart = this.chartIndicator == this.indicatorsListValues.POPULATION_GROWTH_CITIZEN_YOY;
    this.itsPopulationGrowthCitizenQOQChart = this.chartIndicator == this.indicatorsListValues.POPULATION_GROWTH_CITIZEN_QOQ;
    this.chartData = this.chartStats.data

    if (this.itsTotalChart || this.itsUnitsTotalChart || this.itsBuildingTotalChart || this.itsTotalJobSeekersEmiratiChart) {
      this.enableSettingsIcon = false;
    }

    this.chartTitle = (Object.values(this.indicatorsList) as IndicatorItem[]).find(
      (indicator) => indicator.indicator_value === this.chartIndicator
    )?.title ?? '';

    this.chartIcon = (Object.values(this.indicatorsList) as IndicatorItem[]).find(
      (indicator) => indicator.indicator_value === this.chartIndicator
    )?.light_icon ?? '';

    this.chartIcon = geoMapKeys.stagingCmsUrl + this.chartIcon;

    this.chartClassification = (Object.values(this.indicatorsList) as IndicatorItem[]).find(
      (indicator) => indicator.indicator_value === this.chartIndicator
    )?.security.label ?? '';

    if (this.itsTotalChart) {
    }
    else if (this.itsCitizenChart) {
      this.citizenshipStats.emirati = 0;
      this.citizenshipStats.nonEmirati = 0;

      this.chartData.forEach((element: any) => {
        if (element.CODE == geoMapKeys.citizenshipIndicator.emiratiCode) {
          this.citizenshipStats.emiratiCode = element.CODE;
          this.citizenshipStats.emirati = element.POPULATION;
          // update disableEmiratiDot
          this.disableEmiratiDot = this.citizenshipStats.emirati <= 0;
        }
        if (element.CODE == geoMapKeys.citizenshipIndicator.nonEmiratiCode) {
          this.citizenshipStats.nonEmiratiCode = element.CODE;
          this.citizenshipStats.nonEmirati = element.POPULATION;
          // update disableNonEmiratiDot
          this.disableNonEmiratiDot = this.citizenshipStats.nonEmirati <= 0;
        }
      });
      this.chartDataNotEmpty = this.citizenshipStats.emirati > 0 && this.citizenshipStats.nonEmirati > 0;

      this.pieChartData = [];
      this.chartData.forEach((element: any) => {
        if (element.CODE == geoMapKeys.citizenshipIndicator.emiratiCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.citizenshipIndicator.emiratiColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
        if (element.CODE == geoMapKeys.citizenshipIndicator.nonEmiratiCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.citizenshipIndicator.nonEmiratiColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
      });
      this.chartDataNotEmpty = this.pieChartData?.length > 0;
    }
    else if (this.itsGenderChart) {
      this.chartType = 'pie';
      this.pieChartData = [];
      this.chartData.forEach((element: any) => {
        if (element.CODE == geoMapKeys.genderIndicator.maleCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.genderIndicator.maleColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
        if (element.CODE == geoMapKeys.genderIndicator.femaleCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.genderIndicator.femaleColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
      });
      this.chartDataNotEmpty = this.pieChartData?.length > 0;
      this.piechartLoading = true;
      this.createPieChart();
    }
    else if (this.itsEductionChart) {
      this.initBarChart();
    }
    else if (this.itsRegionChart) {
      this.pieChartData = [];
      this.chartData.forEach((element: any) => {
        if (element.CODE == geoMapKeys.regionIndicator.abudhabiCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.regionIndicator.abudhabiColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
        if (element.CODE == geoMapKeys.regionIndicator.alainCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.regionIndicator.alainColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
        if (element.CODE == geoMapKeys.regionIndicator.aldhafraCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.regionIndicator.aldhafraColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
      });
      this.chartDataNotEmpty = this.pieChartData?.length > 0;
      this.initBarChart();
    }
    else if (this.itsMaritalChart) {
      //this.initBarChart();
    }
    else if (this.itsHouseHoldTypeChart) { }
    else if (this.itsHouseHoldSizeChart) { }
    else if (this.itsJobSeekersAgeChart) {
      this.initBarChart();
    }
    else if (this.itsJobSeekerEducation) {
      this.initBarChart();
    }
    else if (this.itsEmployedSectorChart) {
      this.initBarChart();
    }
    else if (this.itsJobSeekerOccupation) {
      //this.initBarChart();
    }
    else if (this.itsEmployedEcoActivity) {
      //this.initBarChart();
    }
    else if (this.itsBuildingTotalChart) {
      this.realEstateTotal = this.totalBuildings;
    }
    else if (this.itsUnitsTotalChart) {
      this.realEstateTotal = this.totalUnits;
    }
    else if (this.itsBuildingUseChart) {
      this.realEstateTotal = this.totalBuildings;
      //this.initBarChart();
    }
    else if (this.itsHousingUnitTypeChart) {
      this.realEstateTotal = this.totalUnits;
      this.initBarChart();
    }
    else if (this.itsUnitUse) {
      this.realEstateTotal = this.totalUnits;
      //this.initBarChart();
    }
    else if (this.itsUnitType) {
      this.realEstateTotal = this.totalUnits;
      this.initBarChart();
    }
    else if (this.itsBuildingTypeChart) {
      this.realEstateTotal = this.totalBuildings;
      //this.initBarChart();
    }
    else if (this.itsDetachedHousingUnitType) {
      this.realEstateTotal = this.totalUnits;
      this.initBarChart();
    }
    else if (this.itsPopulationOver15Chart) { }
    else if (this.itsPopulationByReligionChart) {
      this.chartData = this.chartData
        .sort((a: any, b: any) => b.POPULATION - a.POPULATION)
        .slice(0, 3);
      this.initBarChart();
    }
    else if (this.itsPopulationByEnrollmentChart) { }
    else if (this.itsPopulationBySpecializationChart) { }
    else if (this.itsAgeDependancyRatioChart) { }
    else if (this.itsAgeDependancyRatioCitizinChart) {
      this.chartType = 'pie';
      this.pieChartData = [];
      this.chartData.forEach((element: any) => {
        if (element.CODE == geoMapKeys.citizenshipIndicator.emiratiCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.citizenshipIndicator.emiratiColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
        if (element.CODE == geoMapKeys.citizenshipIndicator.nonEmiratiCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.citizenshipIndicator.nonEmiratiColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
      });
      this.chartDataNotEmpty = this.pieChartData?.length > 0;
      this.piechartLoading = true;
      this.createPieChart();
    }
    else if (this.itsAgeDependancyRatioGenderChart) {
      this.chartType = 'pie';
      this.pieChartData = [];
      this.chartData.forEach((element: any) => {
        if (element.CODE == geoMapKeys.genderIndicator.maleCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.genderIndicator.maleColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
        if (element.CODE == geoMapKeys.genderIndicator.femaleCode && element.POPULATION > 0) {
          this.pieChartData.push({
            indicator: this.chartIndicator,
            code: element.CODE,
            name: this.language == 'ar' ? element.AR : element.EN,
            y: element.POPULATION,
            color: geoMapKeys.genderIndicator.femaleColor,
            quarter: element.QUARTER,
            indicator_id: element.INDICATOR_ID
          });
        }
      });
      this.chartDataNotEmpty = this.pieChartData?.length > 0;
      this.piechartLoading = true;
      this.createPieChart();
    }
    else if (this.itsPopulationNonCitizinChart) {
    }
    else if (this.itsTop10NationalitiesChart) {
      this.chartData = this.chartData
        .sort((a: any, b: any) => b.POPULATION - a.POPULATION)
        .slice(0, 10);

      this.chartData = this.chartData.filter((item: any) => {
        return item.EN && item.EN.trim() !== '' && item.AR && item.AR.trim() !== '';
      });
      //this.initBarChart();
    }
    else if (this.itsPopulationGrowthYOYChart) {
      this.populationGrowthYOY = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_YOY)?.POPULATION;
      this.lastYear = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_YOY)?.YEAR;
      this.lastQuarter = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_YOY)?.QUARTER;
      this.previousYear = this.lastYear - 1;
      this.previousQuarter = this.lastQuarter;
    }
    else if (this.itsPopulationGrowthQOQChart) {
      this.populationGrowthQOQ = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_QOQ)?.POPULATION;
      this.lastYear = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_QOQ)?.YEAR;
      this.lastQuarter = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_QOQ)?.QUARTER;
      this.previousYear = this.lastYear - 1;
      this.previousQuarter = this.lastQuarter;
    }
    else if (this.itsPopulationGrowthCitizenYOYChart) {
      this.lastYear = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_CITIZEN_YOY)?.YEAR;
      this.lastQuarter = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_CITIZEN_YOY)?.QUARTER;
      this.previousYear = this.lastYear - 1;
      this.previousQuarter = this.lastQuarter;
    }
    else if (this.itsPopulationGrowthCitizenQOQChart) {
      this.lastYear = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_CITIZEN_QOQ)?.YEAR;
      this.lastQuarter = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_CITIZEN_QOQ)?.QUARTER;
      this.previousYear = this.lastYear - 1;
      this.previousQuarter = this.lastQuarter;

      this.citizenshipStats.emirati = 0;
      this.citizenshipStats.nonEmirati = 0;

      this.chartData.forEach((element: any) => {
        if (element.CODE == geoMapKeys.citizenshipIndicator.emiratiCode) {
          this.citizenshipStats.emiratiCode = element.CODE;
          this.citizenshipStats.emirati = element.POPULATION;
        }
        if (element.CODE == geoMapKeys.citizenshipIndicator.nonEmiratiCode) {
          this.citizenshipStats.nonEmiratiCode = element.CODE;
          this.citizenshipStats.nonEmirati = element.POPULATION;
        }
      });
    }
    else if (this.itsPopulationGrowthGenderYOYChart) {
      this.lastYear = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_GENDER_YOY)?.YEAR;
      this.lastQuarter = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_GENDER_YOY)?.QUARTER;
      this.previousYear = this.lastYear - 1;
      this.previousQuarter = this.lastQuarter;
    }
    else if (this.itsPopulationGrowthGenderQOQChart) {
      this.lastYear = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_GENDER_QOQ)?.YEAR;
      this.lastQuarter = this.chartData.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_GROWTH_GENDER_QOQ)?.QUARTER;
      this.previousYear = this.lastYear - 1;
      this.previousQuarter = this.lastQuarter;
    }
    else if (this.itsDisiabilityChart) {
      this.chartData = this.chartData.filter((item: any) => {
        return item.EN && item.EN.trim() !== '' && item.AR && item.AR.trim() !== '';
      });
      //this.initBarChart();
    }
  }

  initBarChart() {
    this.chartType = "bar";
    this.barChartData = [];
    this.barChartCategories = [];
    this.sortedChartData.forEach((element: any) => {
      const value = element.POPULATION || element.VALUE;
      if (value > 0) {
        this.barChartCategories.push(this.language == "ar" ? element.AR : element.EN)
        const dataPoint = {
          y: value,
          indicator: this.chartIndicator,
          code: element.CODE
        };
        this.barChartData.push(dataPoint);
      }
    });
    this.chartDataNotEmpty = this.barChartData?.length > 0;
    this.barChartLoading = true;
    this.createBarChart();
  }


  createBarChart() {
    let that = this;
    this.chartOptions = {
      chart: {
        type: that.chartType,
        // height: this.chartType != 'bar' ? this.height : (this.sortedChartData.length * 100 > 300 ? this.sortedChartData.length * 60 : 300),
         height: this.chartType != 'bar' ? this.height : (this.sortedChartData.length * 100 > 300 ? this.sortedChartData.length * 60 : 220),
        backgroundColor: 'transparent',
        spacingTop: 20,
        spacingBottom: 0,
        spacingLeft: 10,
        spacingRight: 0,
        style: {
          fontFamily: that.language == "ar" ? '"Tajawal", sans-serif' : '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
        },
        animation: {
          duration: 800,
          easing: 'easeOutQuart'
        }
      },
      title: {
        text: ''
      },
      subtitle: {
        text: ''
      },
      exporting: {
        buttons: {
          contextButton: {
            enabled: false
          }
        }
      },
      legend: {
        itemStyle: {
          fontSize: '13px',
          fontWeight: '500',
          color: '#333'
        },
        itemHoverStyle: {
          color: '#666'
        },
        align: 'center',
        verticalAlign: 'bottom',
        layout: 'horizontal',
        enabled: false,
        symbolRadius: 4,
        symbolHeight: 12,
        symbolWidth: 12,
        itemMarginTop: 8,
        itemMarginBottom: 8
      },
      credits: {
        enabled: false
      },
      tooltip: {
        enabled: this.isToolTip,
        animation: {
          duration: 200
        },
        hideDelay: 100,
        outside: true,
        backgroundColor: '#333',
        borderRadius: 8,
        borderWidth: 0,
        shadow: {
          color: 'rgba(0, 0, 0, 0.15)',
          offsetX: 0,
          offsetY: 4,
          opacity: 0.15,
          width: 8
        },
        style: {
          color: '#000',
          fontSize: '13px',
          fontWeight: '500',
          padding: '12px 16px'
        },
        useHTML: true,
        className: 'enhanced-bar-tooltip',
        formatter: function (this: Highcharts.TooltipFormatterContextObject): string {
          const pointName = this.point.category || this.series.name || 'Undefined';
          const pointValue = this.point.y !== undefined ? this.point.y.toLocaleString() : 'N/A';
          const seriesName = this.series.name;
          // return `<div class="enhanced-tooltip-content">
          //           <div class="tooltip-header">${pointName}</div>
          //           <div class="tooltip-body">
          //             <div class="tooltip-series">${seriesName}</div>
          //             <div class="tooltip-value">${pointValue}</div>
          //           </div>
          //         </div>`;
          return `<div class="ifp-chart-tooltip__wrapper">
                  <span class="ifp-chart-tooltip__header">${pointName}</span>
                  <span class="ifp-chart-tooltip__value">${seriesName}<br>${pointValue}</span>
                  </div>`;
        }
      },
      plotOptions: {
        series: {
          cursor: this.isBarChartClicable ? 'pointer' : 'default',
          animation: {
            duration: 800,
            easing: 'easeOutQuart'
          },
          states: {
            hover: {
              brightness: 0.1,
              animation: {
                duration: 200
              }
            },
            select: {
              borderColor: '#333',
              borderWidth: 2
            }
          },
          point: {
            events: {
              click: function (event) {
                const point = this;
                that.toggleBarChartSelection(point);
              },
              // mouseOver: function () {
              //   if (this.graphic) {
              //     this.graphic.attr({
              //       'stroke-width': 1,
              //       'stroke': '#333'
              //     });
              //   }
              // },
              mouseOut: function () {
                if (this.graphic && !this.selected) {
                  this.graphic.attr({
                    'stroke-width': 0
                  });
                }
              }
            }
          }
        },
        column: {
          borderRadius: 6,
          borderWidth: 0,
          dataLabels: {
            enabled: true,
            format: '{point.y}',
            color: '#333',
            style: {
              fontSize: '11px',
              fontWeight: '600',
              textOutline: 'none',
              fontFamily: that.language == "ar" ? '"Tajawal", sans-serif' : '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            },
            padding: 4,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 4,
            borderWidth: 1,
            borderColor: '#e0e0e0',
            // shadow: {
            //   color: 'rgba(0, 0, 0, 0.1)',
            //   offsetX: 0,
            //   offsetY: 1,
            //   opacity: 0.1,
            //   width: 2
            // },
            crop: false,
            overflow: 'justify',
            y: -8
          },
          pointPadding: 0.15,
          groupPadding: 0.2,
          maxPointWidth: 60,
          // Enhanced gradient colors
          color: {
            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
            stops: [
              [0, '#4A90A4'],
              [1, '#2E6B7A']
            ]
          }
        },
        bar: {
          borderRadius: 6,
          borderWidth: 0,
          dataLabels: {
            enabled: true,
            format: '{point.y}',
            color: '#333',
            style: {
              fontSize: '11px',
              fontWeight: '600',
              textOutline: 'none',
              fontFamily: that.language == "ar" ? '"Tajawal", sans-serif' : '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            },
            padding: 4,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 4,
            borderWidth: 1,
            borderColor: '#e0e0e0',
            // shadow: {
            //   color: 'rgba(0, 0, 0, 0.1)',
            //   offsetX: 0,
            //   offsetY: 1,
            //   opacity: 0.1,
            //   width: 2
            // },
            crop: false,
            overflow: 'justify',
            x: 8
          },
          pointPadding: 0.1,
          groupPadding: 0.1,
          maxPointWidth: 40,
          pointWidth: 25,

          color: {
            linearGradient: { x1: 0, y1: 0, x2: 1, y2: 0 },
            stops: [
              [0, '#4A90A4'],
              [1, '#2E6B7A']
            ]
          }
        }
      },
      xAxis: {
        categories: this.commonService.isCompareEnabled ? this.compareChartCategories : this.barChartCategories,
        title: {
          text: '',
          style: {
            fontSize: '13px',
            fontWeight: '600',
            color: '#333',
            fontFamily: that.language == "ar" ? '"Tajawal", sans-serif' : '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
          }
        },
        labels: {
          style: {
            fontSize: '12px',
            fontWeight: '500',
            color: '#666',
            fontFamily: that.language == "ar" ? '"Tajawal", sans-serif' : '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
          },
          rotation: 0,
          autoRotation: []
        },
        lineColor: '#e0e0e0',
        lineWidth: 1,
        tickColor: '#e0e0e0',
        tickWidth: 1,
        tickLength: 6,
        gridLineColor: 'transparent'
      },
      yAxis: {
        title: {
          text: '',
          style: {
            fontSize: '13px',
            fontWeight: '600',
            color: '#333',
            textAlign: 'left',
            fontFamily: that.language == "ar" ? '"Tajawal", sans-serif' : '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
          }
        },
        labels: {
          align: 'left',
          style: {
            fontSize: '12px',
            fontWeight: '500',
            color: '#666',
            fontFamily: that.language == "ar" ? '"Tajawal", sans-serif' : '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
          }
        },
        gridLineColor: '#f5f5f5',
        gridLineWidth: 1,
        lineColor: '#e0e0e0',
        lineWidth: 1,
        tickColor: '#e0e0e0',
        tickWidth: 1,
        tickLength: 6,
        min: 0,
        minorGridLineColor: 'transparent'
      },
      series: this.commonService.isCompareEnabled ?
        that.compareLineChartData?.map((series: any, index: number) => ({
          ...series,
          color: this.getSeriesColor(index),
          type: that.chartType
        })) :
        [{
          type: that.chartType,
          name: that.chartTitle,
          data: this.barChartData,
          color: {
            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
            stops: [
              [0, '#4A90A4'],
              [1, '#2E6B7A']
            ]
          }
        }],
      // Responsive design
      responsive: {
        rules: [{
          condition: {
            maxWidth: 600
          },
          chartOptions: {
            plotOptions: {
              column: {
                dataLabels: {
                  enabled: false
                } 
              },
              bar: {
                dataLabels: {
                  enabled: false
                }
              }
            },
            xAxis: {
              labels: {
                style: {
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#000000'
                },
              }
            },
            yAxis: {
              labels: {
                align: 'left',
                style: {
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#000000',
                  textAlign: 'left'
                }
              }
            }
          }
        }]
      }
    };

    this.isChangeUpdate = true;
    this.barChartLoading = false;
    this._cdr.detectChanges();
  }
  

  // Helper method for series colors
  private getSeriesColor(index: number): any {
    const colors = [
      { stops: [[0, '#4A90A4'], [1, '#2E6B7A']] },
      { stops: [[0, '#DC6F6F'], [1, '#B85A5A']] },
      { stops: [[0, '#5329E3'], [1, '#4020B8']] },
      { stops: [[0, '#FF6B35'], [1, '#E55A2B']] },
      { stops: [[0, '#2A9F08'], [1, '#228007']] }
    ];

    const colorSet = colors[index % colors.length];
    return {
      linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
      stops: colorSet.stops
    };
  }


  createPieChart() {
    let that = this;
    this.chartOptions = {
      chart: {
        type: 'pie',
        height: this.height,
        reflow: true,
        backgroundColor: 'transparent',
        // spacingTop: 20,
         spacingTop: -20,
        spacingBottom: 0,
        spacingLeft: 0,
        spacingRight: 0,
        style: {
          fontFamily: '"Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
        }
      },
      title: {
        text: ''
      },
      subtitle: {
        text: ''
      },
      exporting: {
        buttons: {
          contextButton: {
            enabled: false
          }
        }
      },
      legend: {
        itemStyle: {
          fontSize: '13px',
          fontWeight: '500',
          color: '#333'
        },
        itemHoverStyle: {
          color: '#666'
        },
        align: 'center',
        verticalAlign: 'bottom',
        layout: 'horizontal',
        enabled: false,
        symbolRadius: 6,
        symbolHeight: 12,
        symbolWidth: 12,
        itemMarginTop: 8,
        itemMarginBottom: 8
      },
      credits: {
        enabled: false
      },
      tooltip: {
        enabled: this.isToolTip,
        animation: false,
        hideDelay: 100,
        outside: true,
        backgroundColor: '#333',
        borderRadius: 8,
        borderWidth: 0,
        shadow: {
          color: 'rgba(0, 0, 0, 0.15)',
          offsetX: 0,
          offsetY: 4,
          opacity: 0.15,
          width: 8
        },
        style: {
          color: '#000',
          fontSize: '10px',
          fontWeight: '500',
          padding: '12px 16px'
        },
        useHTML: true,
        className: 'enhanced-pie-tooltip',
        formatter: function (this: Highcharts.TooltipFormatterContextObject): string {
          const pointName = this.point.name || 'Undefined';
          const pointValue = this.point.y !== undefined ? this.point.y.toLocaleString() : 'N/A';
          const percentage = this.point.percentage !== undefined ? this.point.percentage.toFixed(1) : '0.0';
          // return `<div class="enhanced-tooltip-content">
          //           <div class="tooltip-header">${pointName}</div>
          //           <div class="tooltip-body">
          //             <div class="tooltip-value">${pointValue}</div>
          //             <div class="tooltip-percentage">${percentage}%</div>
          //           </div>
          //         </div>`;
          return `<div class="ifp-chart-tooltip__wrapper">
                  <span class="ifp-chart-tooltip__header">${pointName}</span>
                  <span class="ifp-chart-tooltip__value" >${pointValue}<br>${percentage}%</span>
                  </div>`;
        }
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          borderWidth: 2,
          borderColor: '#fff',
          slicedOffset: 8,
          states: {
            hover: {
              halo: {
                size: 5,
                opacity: 0.25
              }
            },
            select: {
              borderColor: '#333',
              borderWidth: 3
            }
          },
          dataLabels: {
            enabled: true,
            distance: 15,
            connectorWidth: 2,
            connectorColor: '#666',
            connectorPadding: 5,
            format: '<div class="pie-label"><div class="pie-label-name">{point.name}</div><div class="pie-label-percentage">{point.percentage:.1f}%</div></div>',
            // format: `<div class="pie-label">
            //          <div class="pie-label-name">{point.name}</div>
            //          <div class="pie-label-value">{point.y:,.0f}</div>
            //          <div class="pie-label-percentage">{point.percentage:.1f}%</div>
            //        </div>`,
            useHTML: true,
            style: {
              fontSize: '12px',
              fontWeight: '500',
              textOutline: 'none',
              color: '#333'
            },
            // Ensure labels are always visible
            crop: false,
            overflow: 'justify',
            padding: 4,
            // backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 4,
            // borderWidth: 1,
            // borderColor: '#e0e0e0',
            // shadow: {
            //   color: 'rgba(0, 0, 0, 0.1)',
            //   offsetX: 0,
            //   offsetY: 2,
            //   opacity: 0.1,
            //   width: 4
            // }
          },
          innerSize: that.chartType == 'pie' ? '0%' : '60%',
          center: ['50%', '50%'],
          size: '55%',
          showInLegend: true,
          point: {
            events: {
              click: function (event) {
                const point = this;
                that.togglePieChartSelection(point);
              },
              mouseOver: function () {
                if (this.graphic) {
                  // this.graphic.attr({
                  //   'stroke-width': 3,
                  //   'stroke': '#666'
                  // });
                }
              },
              mouseOut: function () {
                if (this.graphic && !this.selected) {
                  this.graphic.attr({
                    'stroke-width': 2,
                    'stroke': '#fff'
                  });
                }
              }
            }
          },
          // Enhanced color palette
          colors: [
            '#4A90A4', '#DC6F6F', '#5329E3', '#FF6B35', '#2A9F08',
            '#8B5A3C', '#6B46C1', '#F59E0B', '#EF4444', '#10B981',
            '#8B5CF6', '#F97316', '#06B6D4', '#84CC16', '#EC4899'
          ]
        }
      },
      series: [{
        type: 'pie',
        name: that.chartTitle,
        data: this.pieChartData
      }],
      // Responsive design
      // responsive: {
      //   rules: [{
      //     condition: {
      //       maxWidth: 400
      //     },
      //     chartOptions: {
      //       chart: {
      //         spacingTop: 30,
      //         spacingBottom: 30,
      //         spacingLeft: 60,
      //         spacingRight: 60
      //       },
      //       plotOptions: {
      //         pie: {
      //           size: '65%',
      //           dataLabels: {
      //             distance: 0,
      //             style: {
      //               fontSize: '11px'
      //             }
      //           }
      //         }
      //       },
      //       legend: {
      //         itemStyle: {
      //           fontSize: '12px'
      //         }
      //       }
      //     }
      //   }, {
      //     condition: {
      //       maxWidth: 300
      //     },
      //     chartOptions: {
      //       chart: {
      //         spacingTop: 25,
      //         spacingBottom: 25,
      //         spacingLeft: 40,
      //         spacingRight: 40
      //       },
      //       plotOptions: {
      //         pie: {
      //           size: '70%',
      //           dataLabels: {
      //             distance: 0,
      //             style: {
      //               fontSize: '10px'
      //             }
      //           }
      //         }
      //       }
      //     }
      //   }]
      // }
    };

    this.isChangeUpdate = true;
    this.piechartLoading = false;
    this._cdr.detectChanges();
  }


  outsideClick(event: any) {
    if (!this.outsideRef?.nativeElement?.contains(event?.target)) {
      this.chartSettingsShow = false;
    }
  }

  createCompareChartData() {
    this.chartType = 'column';
    this.isCompareEnabled = true;
    this.chartData = this.chartStats.data;
    const data = this.commonService.createCompareChartData(this.chartData);
    this.compareChartCategories = data.category;
    this.compareLineChartData = data.data;
    this.createBarChart();
  }

  ngAfterViewChecked(): void {
    if (this.isChangeUpdate) {
      this.showChart = true;
      this.isChangeUpdate = false;
    }
  }


  formatNumber(num: number): string {
    if (num)
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    else return '0';
  }

  roundNumber(num: number): number {
    if (num)
      return round(num, 2);
    else return 0;
  }

  calculatePercentage(value: number): number {
    if(value <=  0 || this.totalPopulation <= 0) return 0;
    const rounded = Math.round((value / this.totalPopulation) * 100);
    if (rounded === 0) {
      return Number(((value / this.totalPopulation) * 100).toFixed(2));
    }
    return rounded;
  }

  calculatePercentageForEod(value: number): number {
    if (value <= 0 ||  this.eodPopulation <= 0) return 0;

    const rounded = Math.round((value / this.eodPopulation) * 100);
    return rounded === 0 ?
      Number(((value / this.eodPopulation) * 100).toFixed(2)) :
      rounded;
  }


  calculateBarPercentage(value: number, total: number): number {
    if (value <= 0 || total <= 0) return 0;

    const rounded = Math.round((value / total) * 100);
    return rounded === 0 ?
      Number(((value / total) * 100).toFixed(2)) :
      rounded;
  }

  calculateRealEstatePercentage(value: number): number {
    if (value <= 0 || this.realEstateTotal <= 0) return 0;

    const rounded = Math.round((value / this.realEstateTotal) * 100);
    return rounded === 0 ?
      Number(((value / this.realEstateTotal) * 100).toFixed(2)) :
      rounded;
  }

  enableChartSettings() {
    this.chartSettingsShow = !this.chartSettingsShow;
  }


  toggleCitizenshipSelection(citizenCode: number) {
    if (citizenCode === this.citizenshipStats.emiratiCode) {
      this.disableEmiratiDot = !this.disableEmiratiDot;
      this.disableNonEmiratiDot = false;
    } else if (citizenCode === this.citizenshipStats.nonEmiratiCode) {
      this.disableNonEmiratiDot = !this.disableNonEmiratiDot;
      this.disableEmiratiDot = false;
    }

    // Build filter array based on enabled states
    this.filterObject.CITIZEN_CODE = [];

    if (!this.disableEmiratiDot) {
      this.filterObject.CITIZEN_CODE.push(this.citizenshipStats.emiratiCode);
    }

    if (!this.disableNonEmiratiDot) {
      this.filterObject.CITIZEN_CODE.push(this.citizenshipStats.nonEmiratiCode);
    }

    this.filterChange.emit(this.filterObject);
    this.gisSharedService.queryHeatMapBySelect(this.filterObject);
  }

  togglePieChartSelection(point: any) {
    if (point.indicator == this.indicatorsListValues.GENDER || point.indicator == this.indicatorsListValues.AGE_DEPENDENCY_RATIO_GENDER) {
      this.filterObject.GENDER_CODE = [point.code];
    }
    if (point.indicator == this.indicatorsListValues.CITIZENS || point.indicator == this.indicatorsListValues.AGE_DEPENDENCY_RATIO_CITIZEN) {
      this.filterObject.CITIZEN_CODE = [point.code];
    }
    this.filterChange.emit(this.filterObject);
    this.gisSharedService.queryHeatMapBySelect(this.filterObject);
  }

  toggleBarChartSelection(point: any) {
    if (point.indicator == this.indicatorsListValues.EDUCATION) {
      this.filterObject.ATTAINMENT_CODE = [point.code];
    }
    if (point.indicator == this.indicatorsListValues.REGION) {
      this.filterObject.REGION_CODE = [point.code];
      this.regionChangeFromChart.emit(this.filterObject.REGION_CODE);
      this.commonService.setIsMapUpdated([point.category]);
    }
    if (point.indicator == this.indicatorsListValues.MARITAL) {
      this.filterObject.MARITAL_CODE = [point.code];
    }
    // if (point.indicator == this.indicatorsListValues.BUILDING_TYPE) {
    //   this.filterObject.BUILDING_TYPE_CODE = [point.code];
    // }
    // if (point.indicator == this.indicatorsListValues.BUILDING_USE) {
    //   this.filterObject.BUILDING_USE_CODE = [point.code];
    // }
    // if (point.indicator == this.indicatorsListValues.UNIT_TYPE) {
    //   this.filterObject.UNITS_TYPE_CODE = [point.code];
    // }
    // if (point.indicator == this.indicatorsListValues.UNIT_USE) {
    //   this.filterObject.UNITS_USE_CODE = [point.code];
    // }
    // if (point.indicator == this.indicatorsListValues.DISABILITY_TYPE) {
    //   this.filterObject.DISABILITY_TYPE_CODE = [point.code];
    // }
    if (point.indicator != this.indicatorsListValues.DISABILITY_TYPE &&
      point.indicator != this.indicatorsListValues.POPULATION_BY_NATIONALITY &&
      point.indicator != this.indicatorsListValues.EMPLOYED_BY_ECO_ACTIVITY &&
      point.indicator != this.indicatorsListValues.JOB_SEEKER_BY_AGE_GROUP &&
      point.indicator != this.indicatorsListValues.JOB_SEEKER_BY_EDUCATION_LEVEL &&
      point.indicator != this.indicatorsListValues.EMPLOYED_BY_SECTOR &&
      point.indicator != this.indicatorsListValues.EMPLOYED_BY_OCCU_LEVEL &&
      point.indicator != this.indicatorsListValues.BUILDING_TYPE &&
      point.indicator != this.indicatorsListValues.BUILDING_USE &&
      point.indicator != this.indicatorsListValues.UNIT_TYPE &&
      point.indicator != this.indicatorsListValues.UNIT_USE &&
      point.indicator != this.indicatorsListValues.HOUSING_UNIT_TYPE &&
      point.indicator != this.indicatorsListValues.DETACHED_HOUSING_UNIT_TYPE) {
      this.filterChange.emit(this.filterObject);

      this.gisSharedService.queryHeatMapBySelect(this.filterObject);
    }
  }

  toggleProgressChartSelection(point: any) {
    if (point.INDICATOR == this.indicatorsListValues.EDUCATION) {
      this.filterObject.ATTAINMENT_CODE = [point.CODE];
    }
    if (point.INDICATOR == this.indicatorsListValues.MARITAL) {
      this.filterObject.MARITAL_CODE = [point.CODE];
    }
    if (point.INDICATOR == this.indicatorsListValues.HOUSEHOLD_TYPE) {
      this.filterObject.HOUSEHOLD_CODE = [point.CODE];
    }
    this.filterChange.emit(this.filterObject);
    this.gisSharedService.queryHeatMapBySelect(this.filterObject);
  }

  checkNotification() {
    if (this.indicatorId) {
      this.subsink.add(this.store.select(selectGetMappedData(this.indicatorId)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(data => {
        this.isNotificationEnabled = data[this.indicatorId]?.isNotification ? data[this.indicatorId].isNotification : false;
      }));
    }
  }

  addOrRemoveNotification() {
    if (!this.isNotificationEnabled) {
      const notificationData = {
        id: this.indicatorId,
        emailStatus: false,
        contentType: 'Spatial-Analytics',
        appType: 'Spatial-Analytics'
      };
      this.store.dispatch(setNotificationUpdate(notificationData));
    } else {
      this.store.dispatch(unsubscribeNotificationUpdate({ id: this.indicatorId }));
    }

    // if (!this.isNotificationEnabled) {
    //   const notificationData = {
    //     id: this.indicatorId,
    //     emailStatus: false,
    //     contentType: 'Spatial-Analytics',
    //     appType: 'Spatial-Analytics'
    //   };
    //   this.store.dispatch(setNotificationUpdate(notificationData));
    //   this.isSubscNotifOpen = true;
    //   this._alertBoxService.openAlertBox(this.alertBox, this.card.nativeElement);
    //   setTimeout(() => {
    //     this._alertBoxService.closeAlertBox(this.alertBox);
    //     this.isSubscNotifOpen = false;
    //   }, this.alertTimeOutInSec * 1000);
    // } else {
    //   this.store.dispatch(unsubscribeNotificationUpdate({ id: this.indicatorId }));
    // }
  }

  setEmailNotifStatus(status: boolean) {
    if (status) {
      this.store.dispatch(setEmailUpdate({ id: this.indicatorId }));
      setTimeout(() => {
        this._alertBoxService.closeAlertBox(this.alertBox);
        this.isSubscNotifOpen = false;
      }, 400);
    } else {
      this._alertBoxService.closeAlertBox(this.alertBox);
    }
  }

  closeEmailModal() {
    this._alertBoxService.closeAlertBox(this.alertBox);
  }


  get hasChartData(): boolean {
    this.chartDataNotEmpty = this.chartData?.some(
      (stat: any) => stat.POPULATION > 0 || stat.VALUE > 0
    ) ?? false;
    return this.chartData?.some((stat: any) => stat.POPULATION > 0 || stat.VALUE > 0);
  }

  get hasPopulationData(): boolean {
    return this.chartData?.some((stat: any) => stat.POPULATION > 0);
  }

  get canShowPieChart() {
    return !(
      this.itsTotalChart || this.itsEductionChart || this.itsMaritalChart ||
      this.itsDisiabilityChart || this.itsBuildingTotalChart || this.itsHouseHoldTypeChart ||
      this.itsHouseHoldSizeChart || this.itsJobSeekersAgeChart || this.itsEmployedSectorChart ||
      this.itsJobSeekerOccupation || this.itsJobSeekerEducation || this.itsBuildingTypeChart || this.itsDetachedHousingUnitType ||
      this.itsHousingUnitTypeChart || this.itsBuildingUseChart || this.itsUnitsTotalChart || this.itsUnitUse || this.itsUnitType ||
      this.itsPopulationBySpecializationChart || this.itsPopulationNonCitizinChart || this.itsTop10NationalitiesChart ||
      this.itsPopulationOver15Chart || this.populationGrowthQOQ ||
      this.populationGrowthYOY || this.itsPopulationGrowthCitizenQOQChart || this.itsPopulationGrowthCitizenYOYChart ||
      this.itsPopulationGrowthGenderQOQChart || this.itsPopulationGrowthGenderYOYChart || this.itsPopulationGrowthQOQChart ||
      this.itsPopulationGrowthYOYChart || this.itsEmployedEcoActivity || this.itsTotalJobSeekersEmiratiChart
    );
  }

  get canShowDoughnutChart() {
    return !(
      this.itsTotalChart || this.itsEductionChart || this.itsMaritalChart ||
      this.itsDisiabilityChart || this.itsBuildingTotalChart || this.itsHouseHoldTypeChart ||
      this.itsHouseHoldSizeChart || this.itsJobSeekersAgeChart || this.itsEmployedSectorChart ||
      this.itsJobSeekerOccupation || this.itsJobSeekerEducation || this.itsBuildingTypeChart || this.itsDetachedHousingUnitType ||
      this.itsHousingUnitTypeChart || this.itsBuildingUseChart || this.itsUnitsTotalChart || this.itsUnitUse || this.itsUnitType ||
      this.itsPopulationBySpecializationChart || this.itsPopulationNonCitizinChart || this.itsTop10NationalitiesChart ||
      this.itsPopulationOver15Chart || this.populationGrowthQOQ ||
      this.populationGrowthYOY || this.itsPopulationGrowthCitizenQOQChart || this.itsPopulationGrowthCitizenYOYChart ||
      this.itsPopulationGrowthGenderQOQChart || this.itsPopulationGrowthGenderYOYChart || this.itsPopulationGrowthQOQChart ||
      this.itsPopulationGrowthYOYChart || this.itsEmployedEcoActivity || this.itsTotalJobSeekersEmiratiChart
    );
  }

  get canShowBarChart() {
    return !this.itsAgeDependancyRatioCitizinChart && !this.itsAgeDependancyRatioGenderChart;
  }

  get canShowDefaultChart() {
    return !this.itsTotalChart && !this.itsCitizenChart && !this.itsBuildingTotalChart &&
      !this.itsPopulationNonCitizinChart && !this.itsPopulationOver15Chart && !this.itsUnitsTotalChart &&
      !this.itsPopulationGrowthYOYChart && !this.itsPopulationGrowthQOQChart && !this.itsPopulationGrowthCitizenQOQChart &&
      !this.itsPopulationGrowthCitizenYOYChart && !this.itsPopulationGrowthGenderYOYChart && !this.itsPopulationGrowthGenderQOQChart &&
      !this.itsAgeDependancyRatioCitizinChart && !this.itsAgeDependancyRatioGenderChart && !this.itsTotalJobSeekersEmiratiChart;
  }

  get hideDefaultChartSetting() {
    return !this.itsAgeDependancyRatioCitizinChart && !this.itsAgeDependancyRatioGenderChart && !this.populationGrowthQOQ &&
      !this.populationGrowthYOY && !this.itsPopulationGrowthCitizenQOQChart && !this.itsPopulationGrowthCitizenYOYChart &&
      !this.itsPopulationGrowthGenderQOQChart && !this.itsPopulationGrowthGenderYOYChart && !this.itsPopulationGrowthQOQChart &&
      !this.itsPopulationGrowthYOYChart;
  }


  get itsGrowthIndicators() {
    return this.itsPopulationGrowthYOYChart || this.itsPopulationGrowthQOQChart ||
      this.itsPopulationGrowthCitizenYOYChart || this.itsPopulationGrowthCitizenQOQChart ||
      this.itsPopulationGrowthGenderYOYChart || this.itsPopulationGrowthGenderQOQChart
  }

  get isBarChartClicable() {
    return this.itsEductionChart || this.itsMaritalChart || this.itsRegionChart 
    // || this.itsBuildingTypeChart || this.itsBuildingUseChart || this.itsUnitType || this.itsUnitUse
  }

  // Add this method to sort  data
  get sortedChartData() {
    if (!this.chartData) return [];

    if (this.itsJobSeekersAgeChart) {
      // Apply specific sorting for job seekers age chart
      return [...this.chartData].sort((a, b) => {
        const getStartAge = (ageGroup: string) => parseInt(ageGroup.split('-')[0], 10);
        return getStartAge(a.EN) - getStartAge(b.EN);
      });
    }

    // remove sorting for education only
    else if (!this.itsEductionChart && !this.itsJobSeekerEducation) {
      return [...this.chartData].sort((a, b) => {
        const valueA = a.POPULATION || a.VALUE || 0;
        const valueB = b.POPULATION || b.VALUE || 0;
        return valueB - valueA;
      });
    }

    else return this.chartData;
  }

  get chartShowChartsSettings() {
     return false
  }

  getTooltipText(): string {
    let baseText = '';

    // Determine base text based on domain_id
    switch (this.domain_id) {
      case 1:
        baseText = this.language == 'ar' ? 'السكان' : 'Population';
        break;
      case 2:
        baseText = this.language == 'ar' ? 'القوى العاملة' : 'Employed Population';
        break;
      case 3:
        baseText = this.language == 'ar' ? 'مجموع المباني' : 'Total Buildings';
        break;
      default:
        baseText = this.language == 'ar' ? 'البيانات' : 'Data';
    }

    if (this.itsTotalChart) {
      return this.language == 'ar' ? ` مجموع ${baseText} ` : `Total ${baseText}`;
    } else if (this.itsTotalJobSeekersEmiratiChart) {
      return '';
    } else if (this.itsEmployedSectorChart) {
      return '';
    } else if (this.itsJobSeekersAgeChart) {
      return '';
    } else if (this.itsJobSeekerOccupation) {
      return '';
    } else if (this.itsJobSeekerEducation) {
      return '';
    } else if (this.itsEmployedEcoActivity) {
      return '';
    } else if (this.itsBuildingTotalChart) {
      return '';
    } else if (this.itsUnitsTotalChart) {
      return '';
    } else if (this.itsBuildingTypeChart) {
      return this.language == 'ar' ? `${this.chartTitle} من ${baseText}` : `${this.chartTitle} from ${baseText}`;
    } else if (this.itsBuildingUseChart) {
      return this.language == 'ar' ? `${this.chartTitle} من ${baseText}` : `${this.chartTitle} from ${baseText}`;
    } else if (this.itsHousingUnitTypeChart) {
      return this.language == 'ar' ? `${this.chartTitle} من مجموع الوحدات` : `${this.chartTitle} from total Units`;
    } else if (this.itsUnitUse) {
      return this.language == 'ar' ? `${this.chartTitle} من مجموع الوحدات` : `${this.chartTitle} from total Units`;
    } else if (this.itsDetachedHousingUnitType) {
      return this.language == 'ar' ? `${this.chartTitle} من مجموع الوحدات` : `${this.chartTitle} from total Units`;
    } else {
      return this.language == 'ar' ? `${baseText} حسب ${this.chartTitle}` : `${baseText} By ${this.chartTitle}`;
    }

  }

  resetFilter(event: any) {
    this.triggerResetFilter.emit();
  }
}

