@use "../../../assets/ifp-styles/abstracts/index" as *;
:host{
  display: block;
}
.ifp-gen-ai-dashboard-preview {
  background-color: $ifp-color-white;
  border-radius: 20px;
  padding-top: $spacer-5 ;
  padding-bottom: $spacer-5 ;
  box-shadow: 1px 1px 20px #99a09e1f;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding: $spacer-0  $spacer-5  $spacer-5  $spacer-5;
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    border-radius: 20px 20px 0 0;
    border-bottom: 1px  solid $ifp-color-grey-7;
  }
  &__response-icon {
    color: $ifp-color-blue-menu;
  }
  &__body {
    padding: $spacer-0  $spacer-5  ;
  }
  &__btn-reanalyze {
    margin-inline-end: $spacer-3;
  }
  &__number {
    display: flex;
    padding: $spacer-4  $spacer-0  ;
  }
  &__number-value {
    font-weight: $fw-bold;
    margin: $spacer-0 $spacer-2;
  }

  &__number-icon {
    margin-inline-end: $spacer-2;
  }
  &__questions-number {
    width: 40px;
    height: 40px;
    background-color: $ifp-color-blue-1 ;
    border-radius: 50%;
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    padding: $spacer-1;
    align-items: center;
    margin-inline-end:$spacer-3 ;
    justify-content: center;
    display: flex;
    color: $ifp-color-primary-blue;
    &--active {
      background-color: $ifp-color-blue-menu;
      color: $ifp-color-white;
    }
  }
  &__header-icon{
    color: $ifp-color-blue-hover;
    cursor: pointer;
    margin-inline-end: $spacer-2;
  }
  &__header-preview {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }
  &__qus {
    width: calc(100% - 56px);
  }
  &__questions {
    display: flex;
    margin-bottom: $spacer-5;
  }
  &__response-separator {
    margin: $spacer-0 $spacer-2;
  }
&__questions-loader {
  padding: $spacer-4;
  border-radius: 10px;
  width: 100%;
  border: 1px solid $ifp-color-grey-7;
}
&__loader-card-wrapper {
  display: flex;
  align-items: center;

  width: 100%;
  border-radius: 10px;
}
&__loader-card {
  width: 100px;
  margin-inline-end: $spacer-3;
}
&__questions-loader-text {
  font-weight: $fw-bold;
}
&__questions-loader-wrapper {
  display: flex;
  justify-content: space-between;
}

&__response-progess-bar {
 display: block;
 width: 100%;
 &::ng-deep {
  .ifp-progress-bar {
    border-radius: 0;
    &__inner {
      border-radius: 0;
    }
  }
 }
}
&__dropdown-wrapper {
  display: flex;
}
&__dropdown-version {
  margin-inline-end: $spacer-3;
}
&__edit {
margin-left: auto;
}
&__loader {
  display: block;
  margin-top: $spacer-2;
    margin-bottom: $spacer-2;
}
}

:host-context(.ifp-dark-theme) {
  .ifp-gen-ai-dashboard-preview {
    &__questions-number{
      background-color: $ifp-color-section-white;
}
}
}
