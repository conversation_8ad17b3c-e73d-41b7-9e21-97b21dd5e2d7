<div class="ifp-gen-ai-dashboard-preview">
  <div class="ifp-gen-ai-dashboard-preview__header">

    <div class="ifp-gen-ai-dashboard-preview__header-preview ">
      <em class="ifp-icon ifp-icon-leftarrow ifp-gen-ai-dashboard-preview__header-icon" (click)="goBack()"></em>
      {{heading() | translate}}
    </div>

    @if (!preview() &&( responseOfQuestionEnable() ? questionResponseArray() !==undefined &&
    questionResponseArray().length >0: numberOfQuestion() !==undefined&& numberOfQuestion() !==0)) {
    <div>
      @if (runStatus()) {
      @if(!edit()) {
      @if (!loaderQuestion()) {
      <ifp-button [buttonClass]="buttonClass.transpreantBlue" [iconClass]="'ifp-icon-thunder-only'"
        class="ifp-gen-ai-dashboard-preview__btn-reanalyze" [label]="'Reanalyze'" (ifpClick)="run()"></ifp-button>
      }
      <ifp-button
        [buttonClass]="(numberOfQuestion()===responseOfQuestion() ? buttonClass.hoverBlue : buttonClass.disabled)"
        class="ifp-gen-ai-dashboard-preview__btn-run" [label]="'Submit'" (ifpClick)="submitAllReview()"></ifp-button>
      }@else {
      <ifp-button [buttonClass]="buttonClass.transpreantBlue" class="ifp-gen-ai-dashboard-preview__btn-reanalyze"
        [label]="'Cancel'" (ifpClick)="CancelEdit()"></ifp-button>
      <ifp-button [buttonClass]="buttonClass.hoverBlue" class="ifp-gen-ai-dashboard-preview__btn-run" [label]="'Submit'"
        (ifpClick)="submitAllEdit()"></ifp-button>
      }

      } @else {
      <div class="ifp-gen-ai-dashboard-preview__dropdown-wrapper">
        @if (this.versionSwitchAccess()) {
        <app-ifp-dropdown (dropDownItemClicked)="dropdownVersion($event)"
          class="ifp-gen-ai-dashboard-preview__dropdown-version" [dropDownItems]="modalDropdown() ?? []"
          [key]="'value'"></app-ifp-dropdown>
        }
        <ifp-button class="ifp-gen-ai-dashboard-preview__btn-run" [label]="'Run'" [buttonClass]="buttonClass.hoverBlue"
          (ifpClick)="run()"></ifp-button>
      </div>

      }
    </div>
    }

  </div>
  @if (loaderQuestion()) {

  <ifp-progress-bar class="ifp-gen-ai-dashboard-preview__response-progess-bar" [color]="'#5DB14E'"
    [width]="(((loadedQuestion()) /numberOfQuestion())*100) +'%'"></ifp-progress-bar>
  }
  <div class="ifp-gen-ai-dashboard-preview__body">
    @if (loader() ||(questionLoader() && responseOfQuestionEnable())) {
    <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader" [type]="'small'"></app-ifp-card-loader>
    }
    @else {
    @if (( responseOfQuestionEnable() ? questionResponseArray().length === 0: numberOfQuestion() ===0) ) {
    <app-ifp-no-data [isTransparent]="true" [img]="'../../../../../assets/images/no-data.svg'"
      [description]="'Once you upload a file using the input above, the relevant data will appear here'"></app-ifp-no-data>
    }@else {
    <div class="ifp-gen-ai-dashboard-preview__number">
      <div class="ifp-gen-ai-dashboard-preview__response-valuated">
        <em class="ifp-icon ifp-icon-help-circle-outline ifp-gen-ai-dashboard-preview__number-icon"></em>
        {{'Number of questions' | translate}}: <span
          class="ifp-gen-ai-dashboard-preview__number-value">{{responseOfQuestionEnable() ?
          questionResponseArray().length: numberOfQuestion()}}</span>
      </div>
      @if ( runStatus()) {
      <div class="ifp-gen-ai-dashboard-preview__response-separator">
        |
      </div>

      <div class="ifp-gen-ai-dashboard-preview__response-valuated">
        <em class="ifp-icon ifp-icon-circle-tick ifp-gen-ai-dashboard-preview__response-icon"></em>
        {{'Response Evaluated'| translate}}: <span
          class="ifp-gen-ai-dashboard-preview__number-value">{{responseOfQuestion()}} </span>
      </div>
      <!-- @if (!loaderQuestion() && !edit()) {
    <span class="ifp-link ifp-gen-ai-dashboard-preview__edit" (click)="edit.set(true)"><em class="ifp-icon ifp-icon-edit ifp-gen-ai-dashboard-preview__edit-icon"></em> {{'Edit'| translate}}</span>

  } -->

      }

    </div>

    @if (this.dropdown[questionDropDown.dataClassification] && this.dropdown[questionDropDown.referenceQComplexity] &&
    this.dropdown[questionDropDown.referenceQType] &&this.dropdown[questionDropDown.sourceData]) {
    @for (question of (responseOfQuestionEnable() ? questionResponseArray():questionList()); track question;let idx =
    $index;let last = $last) {
    <div class="ifp-gen-ai-dashboard-preview__questions">
      <div class="ifp-gen-ai-dashboard-preview__questions-number">
        {{(idx + 1) + ((pages.page - 1) * 20)}}
      </div>
      @if (question?.status === 'inprogress') {

      <div class="ifp-gen-ai-dashboard-preview__questions-loader">
        <app-ifp-card-loader [type]="loaderType.smallSingle"></app-ifp-card-loader>
        <div class="ifp-gen-ai-dashboard-preview__loader-card-wrapper">
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
        </div>
      </div>
      }@else {
      <ifp-gen-ai-dashboard-question [previewEdit]="edit()" (changeDetect)="saveChange($event)"
        (changeDetectForQuestionChange)="questionChangeDetect($event)" [validation]="validation()"
        [enableDelete]="responseOfQuestionEnable() ? questionResponseArray().length>1 :questionList().length > 1"
        (delete)="deleteQuestion($event)" [statusValue]="question?.rating?.rating ?? 0" [preview]="preview()"
        [feedbackValue]="question?.rating?.additional_feedback ?? ''"
        [isSatisfied]="question?.rating?.is_satisfied ?? null" [dropdownObject]="dropdownObject" [dropdown]="dropdown"
        (add)="saveAndAddQuestion($event)" (saveValue)="saveQuestion($event)" [addQuestionEnable]="last"
        [question]="question" [response]="questionResponse()" (submit)="addRating($event)" [feedback]="runStatus()"
        [qusEnable]="responseOfQuestionEnable()" class="ifp-gen-ai-dashboard-preview__qus"
        (ratingUpdate)="ratingUpdate($event)" (addQuestionEvent)="addQuestion()"
        [id]="idx"></ifp-gen-ai-dashboard-question>
      }



    </div>


    }
    @if(loaderQuestion()&& questionList().length< numberOfQuestion() ) { <div
      class="ifp-gen-ai-dashboard-preview__questions">
      <div class="ifp-gen-ai-dashboard-preview__questions-number">
        {{(responseOfQuestionEnable() ? questionResponseArray().length:questionList().length )+1}}
      </div>
      <div class="ifp-gen-ai-dashboard-preview__questions-loader">
        <app-ifp-card-loader [type]="loaderType.smallSingle"></app-ifp-card-loader>
        <div class="ifp-gen-ai-dashboard-preview__loader-card-wrapper">
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
          <app-ifp-card-loader class="ifp-gen-ai-dashboard-preview__loader-card"
            [type]="loaderType.smallSingle"></app-ifp-card-loader>
        </div>
      </div>
  </div>
  }
  }
  }
  }

  @if(numberOfQuestion() !==0 && !runStatus()){
  <app-pagination [offset]="0" [limit]="20" class="ifp-library__pagination" (pageChange)="pageChange($event)"
    [customPagination]="true" [size]="numberOfQuestion()"></app-pagination>
  }
</div>
</div>
