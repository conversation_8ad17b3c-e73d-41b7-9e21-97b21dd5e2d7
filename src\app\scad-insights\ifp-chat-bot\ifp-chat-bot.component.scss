@use "../../../assets/ifp-styles/abstracts/index" as *;

.ifp-ai {
  $ai-icon-width: 85px;
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 1200;

  &__icon {
    width: $ai-icon-width;
  }

  &__button {
    cursor: pointer;

    &::after {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      cursor: pointer;
      border-radius: 35px;
    }
  }

  &__modal {
    border-radius: 20px;
  }

  &__modal-header {
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacer-3 $spacer-4;
    background-color: $ifp-color-section-white;
    border-bottom: 1px solid $ifp-color-grey-3;
    position: relative;
    z-index: 2;

    .ifp-icon {
      cursor: pointer;
    }
  }

  &__modal-heading {
    font-size: $ifp-fs-7;
    font-weight: $fw-bold;
  }

  &__modal-body {
    display: flex;
  }

  &__modal-left {
    width: calc(100% - 330px);
    padding: $spacer-5;
    display: flex;
    flex-direction: column;
  }

  &__left-inner {
    height: calc(100vh - 355px);
    flex-direction: column;
    justify-content: flex-end;
    display: flex;

  }

  &__chat-wraper {
    display: flex;
    flex-direction: column;
    @include ifp-scroll-y($ifp-color-grey-11, $ifp-color-grey-1, 8px, 16px);
    min-height: 200px;
    scroll-behavior: smooth;
    margin-bottom: $spacer-2;
  }

  &__sidebar {
    width: 20%;
    min-width: 380px;
    padding: $spacer-4;
    background-color: $ifp-color-blue-light-50;
    border-inline-end: 1px solid $ifp-color-grey-7;
    display: flex;
    height: calc(100vh - 150px);
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);

    .ifp-collapse {
      display: none;
    }
  }

  &__sidebar-inner {
    margin-bottom: $spacer-4;
  }

  &__title-wrapper {
    display: flex;
    align-items: center;

  }

  &__info {
    display: flex;
    align-items: center;

    .ifp-icon {
      margin-inline-start: $spacer-3;
      position: relative;
      top: 1px;
      font-size: $ifp-fs-5;
    }
  }

  &__input-sec {
    border: 1px solid $ifp-color-grey-5;
    border-radius: 20px;
    padding: $spacer-2 + 4px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: auto;
  }

  &__input {
    width: 100%;
    color: $ifp-color-black;
    background-color: transparent;
    padding: $spacer-2 $spacer-0;
    margin: $spacer-0 $spacer-2 $spacer-2;
  }

  &__actions-wrapper {
    display: flex;
    margin: $spacer-0 (
      -$spacer-2
    );
}

&__send {
  font-size: $ifp-fs-9;
  background-color: $ifp-color-sky-blue;
  width: 50px;
  height: 50px;
  min-width: 50px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: auto $spacer-0;

  &--disabled {
    background-color: $ifp-color-grey-7;
    pointer-events: none;
  }
}

&__send-icon {
  font-size: $ifp-fs-9;
  color: $ifp-color-white-global;
  position: relative;
  top: 4px;
}

&__stop-icon {
  width: 20px;
  height: 20px;
  min-width: 20px;
  border-radius: 3px;
  background-color: $ifp-color-white-global;
}

&__response {
  padding: $spacer-3;
  position: relative;
  margin-bottom: $spacer-4;

  &:last-child {
    padding-top: $spacer-5;
    margin-top: $spacer-5;
  }

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
  }

  &--new {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
    margin: $spacer-0 auto;
    padding-bottom: $spacer-0;
  }

  &--user {
    border-radius: 20px 20px 0 20px;
    background-color: $ifp-color-grey-16;
    margin-inline-start: auto;
    margin-inline-end: $spacer-4;
    text-align: end;
    max-width: 90%;
    margin-bottom: $spacer-0;

    &::after {
      left: 100%;
      border-inline-start: 10px solid $ifp-color-grey-16;
      border-bottom: 10px solid $ifp-color-grey-16;
      border-inline-end: 10px solid transparent;
      border-bottom-right-radius: 10px;
      margin-inline-start: -2px;
    }
  }

  &--ai {
    border-radius: 20px 20px 20px 0;
    background-color: $ifp-color-section-white;
    width: 100%;

    &::after {
      right: 100%;
      border-inline-end: 10px solid $ifp-color-section-white;
      border-bottom: 10px solid $ifp-color-section-white;
      border-inline-start: 10px solid transparent;
      border-bottom-left-radius: 10px;
      margin-inline-end: -2px;
    }

    .ifp-ai {
      &__query-sec {
        display: flex;
      }

      &__result {
        width: 100%;
      }
    }
  }
}

&__query-sec {
  margin-bottom: $spacer-4;

  &:last-child {
    margin-bottom: $spacer-0;
  }
}

&__bot-img {
  font-size: $ifp-fs-8;
  color: $ifp-color-blue-hover;
  width: 40px;
  height: 40px;
  min-width: 40px;
  line-height: 40px !important;
  text-align: center;
  border-radius: 50%;
  margin-inline-end: $spacer-2;
  border: 1px solid $ifp-color-grey-5;
  background-color: $ifp-color-section-white;
  padding: ($spacer-1+2);

  &--profile {
    object-fit: cover;
    object-position: center;
  }
}

&__animation-wrapper {
  width: 150px;
  margin: 0 auto;
}

&__animation-svg {
  width: 100%;
  height: auto;
  pointer-events: none;
}

&__query,
&__desc {
  margin-bottom: $spacer-2;
  line-height: 1.5;

  &:last-child {
    margin-bottom: $spacer-0;
  }
}

&__query-ai {
  color: $ifp-color-black;
}

&__query {
  font-size: $ifp-fs-4;
  color: $ifp-color-black-global;
  // font-weight: $fw-semi-bold;
}

&__response-empty {
  text-align: center;
  min-width: 90%;
  margin: auto $spacer-0;
}

&__empty-text {
  font-size: $ifp-fs-6;
  color: $ifp-color-grey-9;
}

&__empty-icon {
  margin: $spacer-0 auto $spacer-4;
  width: 100px;
}

&__start-button {
  width: 100%;
  display: block;
  margin-bottom: $spacer-3;

  &:last-child {
    margin: $spacer-0;
  }

  &::ng-deep {
    .ifp-btn {
      width: 100%;
      text-transform: capitalize;
    }
  }
}

&__btn-sec {
  margin-bottom: $spacer-3;
}

&__sidebar-title {
  font-size: $ifp-fs-5;
  font-weight: $fw-bold;
  margin-bottom: ($spacer-3 - 4);
  color: $ifp-color-grey-6;
}

&__tab-title {
  font-size: $ifp-fs-4;
  color: $ifp-color-grey-6;
  margin-bottom: $spacer-2;
}

&__tabs {
  display: flex;
  border: 1px solid $ifp-color-sky-blue;
  border-radius: 8px;
  padding: 2px;
}

&__tab-cnt {
  padding: ($spacer-3 - 3) $spacer-2;
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: $ifp-color-sky-blue;
  cursor: pointer;
  white-space: nowrap;
  transition: 0.3s;
  position: relative;
  &::before {
    content: "";
    width: 1px;
    height: calc(100% - 26px);
    background-color: $ifp-color-grey-13;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  &:first-child,
  &--active {
    &::before {
      content: none;
    }
  }
  &:first-child {
    border-radius: 8px 0 0 8px;
  }

  &:last-child {
    border-radius: 0 8px 8px 0;
  }

  &--active {
    color: $ifp-color-white-global;
    background-color: $ifp-color-sky-blue;
  }
}

&__tab-section {
  margin-bottom: $spacer-3;

  &:last-child {
    margin-bottom: $spacer-0;
  }
}

&__tab-wrapper {
  min-width: 350px;
  padding: ($spacer-3 + 4) ($spacer-3 - 4);
  background-color: $ifp-color-section-white;
  border-radius: 0 10px 10px 10px;
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: (-$spacer-2);

  .ifp-icon {
    margin: $spacer-2;
    font-size: $ifp-fs-4;
    color: $ifp-color-sky-blue;
    cursor: pointer;
  }
}

&__sub-title {
  font-weight: $fw-semi-bold;
  margin-bottom: $spacer-2;
}

&__accordian-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ($spacer-2+2px) $spacer-3;
  cursor: pointer;
}

&__accordian-body {
  max-height: 0;
  overflow: hidden;
  transition: 0.3s;
}

&__accordian-list {
  border-top: 1px solid $ifp-color-grey-3;
  max-height: 150px;
  background-color: $ifp-color-grey-bg;
  @include ifp-scroll-y($ifp-color-grey-11, $ifp-color-grey-1, 8px, 8px);
}

&__accordian-item {
  display: flex;
  align-items: center;
  padding: ($spacer-1 + 1px) $spacer-3;
  cursor: pointer;
  overflow: hidden;
  transition: 0.3s;

  .ifp-icon {
    font-size: $ifp-fs-6;
    color: $ifp-color-grey-1;
    margin-inline-end: $spacer-2;
  }

  &:hover,
  &--active {
    background-color: $ifp-color-grey-bg-2;
  }

  &--active {
    .ifp-icon {
      color: $ifp-color-black;
    }
  }
}

&__accordian-query {
  font-weight: $fw-medium;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

&__accordian {
  border: 1px solid $ifp-color-grey-3;
  background-color: $ifp-color-white;
  margin-top: -1px;

  &:first-child {
    margin-top: $spacer-0;
  }

  &--expand {
    .ifp-ai {
      &__accordian-body {
        overflow: visible;
        max-height: 1000px;
      }
    }
  }
}

&__sidebar-feedback-error {
  color: $ifp-color-red;
  font-size: $ifp-fs-3;
  margin-top: $spacer-2;
  margin-inline-start: $spacer-1;
}

&__accordians {
  margin-bottom: 12px;
}

&__threads-outer {
  padding-right: $spacer-1;
  margin-right: -($spacer-1);
  @include ifp-scroll-y($ifp-color-grey-11, $ifp-color-grey-1, 4px, 8px);
}

&__thread-wrapper {
  background-color: $ifp-color-white;
  padding: $spacer-3;
  border-radius: 10px;
}

&__card-wrapper,
// &__type-card,
&__cat-selected,
&__selected-inner,
&__cat-item {
  display: flex;
}

&__card-wrapper {
  flex-wrap: wrap;
  margin: $spacer-4 (
    -$spacer-2) $spacer-3;
  width: calc(100% + (2 * $spacer-2)
  );

&--all {
  max-height: calc(100vh - 480px);
  @include ifp-scroll-y($ifp-color-grey-11, $ifp-color-grey-1, 8px, 16px);
}
}

&__prompt-card {
  width: calc(50% - (2 * $spacer-2));
  padding: $spacer-3;
  margin: $spacer-2;
  border-radius: 10px;
  box-shadow: 0 23px 9px -10px rgba(171, 186, 181, 0.11);
  cursor: pointer;
  background-color: $ifp-color-section-white;
  transition: 0.3s;

  &:hover {
    transform: scale(1.02);
  }
}

&__card-title {
  font-weight: $fw-medium;
}

&__card-desc {
  margin-top: $spacer-1;
  color: $ifp-color-grey-14;
}

&__prompt-arrow {
  width: 25px;
  min-width: 25px;
  height: 25px;
  line-height: 25px !important;
  border-radius: 50%;
  text-align: center;
  border: 1px solid $ifp-color-grey-7;
  cursor: pointer;
  transition: 0.3s;
  position: relative;
  top: -2px;
  margin-inline-start: $spacer-2;

  &:hover {
    background-color: $ifp-color-active-blue;
    color: $ifp-color-white-global;
  }
}

&__cat-icon {
  font-size: $ifp-fs-6;
  color: $ifp-color-sky-blue;
  margin-inline-end: $spacer-2;
}

&__cat-title,
&__cat-item {
  font-weight: $fw-semi-bold;
  text-transform: capitalize;
}

&__cat-title {
  color: $ifp-color-grey-14;
}

&__cat-desc {
  font-weight: $fw-regular;
  color: $ifp-color-grey-14;
  margin-top: $spacer-1;
  transition: 0.3s;
}

&__cat-selected {
  align-items: center;
  justify-content: space-between;
  padding: ($spacer-2 + 4px) $spacer-4;
  border: 1px solid $ifp-color-grey-5;
  border-radius: 50px;
  cursor: pointer;
  background-color: $ifp-color-white;
  transition: 0.3s;
}

&__input-action {
  margin: $spacer-0 $spacer-2;
  border-radius: 50px;
  background-color: $ifp-color-section-white;
  transition: 0.3s;

  &:hover,
  &--active {
    .ifp-ai {
      &__cat-selected {
        border-color: $ifp-color-sky-blue;
      }

      &__cat-title {
        color: $ifp-color-sky-blue;
      }
    }
  }

  &--active {
    .ifp-ai {
      &__cat-selected {
        background-color: rgba(5, 148, 201, 0.15);
      }
    }
  }
}

&__selected-inner {
  align-items: center;
}

&__cat-list {
  width: 100%;
  min-width: 150px;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.16);
  position: absolute;
  bottom: 100%;
  margin: $spacer-2 $spacer-0;
  transform: scaleY(0);
  transform-origin: bottom;
  transition: 0.3s;
  background-color: $ifp-color-white;
}

&__cat-item {
  align-items: center;
  border-bottom: 1px solid $ifp-color-pale-grey;
  padding: $spacer-2 $spacer-3;
  cursor: pointer;
  transition: 0.3s;

  &:first-child {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
  }

  &:last-child {
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    border: none;
  }

  &:hover:not(.ifp-ai__cat-item--active) {
    background-color: rgba(5, 148, 201, 0.15);
  }

  &--active {
    background-color: $ifp-color-sky-blue;
    color: $ifp-color-white-global;

    .ifp-icon {
      color: $ifp-color-white;
    }

    .ifp-ai {

      &__cat-title,
      &__cat-desc {
        color: $ifp-color-white;
      }
    }
  }
}

&__cat-dropdown-arrow {
  transition: 0.3s;
}

&__cat-dropdown {
  position: relative;

  &--active {
    .ifp-ai {
      &__cat-list {
        transform: scaleY(1);
      }

      &__cat-dropdown-arrow {
        transform: rotate(180deg);
      }
    }
  }
}

&__tools-inner {
  display: flex;
  align-items: center;
  transition: 0.3s;
  padding-inline-end: $spacer-3;
}

&__tools-text {
  white-space: nowrap;
  margin-inline-end: $spacer-3;
}

&__tools-icon {
  padding: $spacer-0 $spacer-2;
}

&__tools-dropdown {
  min-width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 40px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $ifp-color-white-global;
  background: linear-gradient(45deg, #0054AF, #2687FD);
  box-shadow: inset -2px 2px 3px #2787FD, inset -3px 3px 8px rgba(255, 255, 255, 0.63);

  &>.ifp-icon {
    font-size: $ifp-fs-6;
  }
}

&__tools {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  cursor: pointer;

  .ifp-ai__cat-list {
    z-index: 1;
    bottom: auto;
    top: 100%;
    right: 0;
    transform-origin: top;
    min-width: 250px;
  }

  &:hover,
  &--active {
    .ifp-ai {
      &__tools-inner {
        overflow: visible;
      }
    }
  }

  &--active {
    .ifp-ai {
      &__cat-list {
        transform: scale(1);
      }
    }
  }
}

&__search {
  display: block;
  margin-bottom: ($spacer-3 - 4);


}

&__chart-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (
    -$spacer-2
  );
}

&__chart {
  width: calc(50% - ($spacer-2 * 2));
  margin: $spacer-2;

  &--expand {
    width: calc(100% - ($spacer-2 * 2));
  }
}

&__chart-actions {
  display: flex;
  align-items: center;
}

&__action-icon {
  font-size: $ifp-fs-9;
  color: $ifp-color-grey-2;
  margin: $spacer-0 $spacer-2;
  font-weight: $fw-extra-light;
  opacity: 0.6;
  cursor: pointer;
}

&__response-wrapper {
  display: flex;
  margin-top: $spacer-5;
  width: 90%;

  .ifp-ai__response--ai {
    margin-top: $spacer-0;
  }
}

&__disclaimer-tit-wrap {
  display: flex;
  align-items: center;
  margin-bottom: $spacer-2;
}

&__disclaimer {
  text-align: center;
  color: $ifp-color-grey-14;
  padding: $spacer-0 (
    $ai-icon-width - 25
  );
}

&__tool-info {
  font-size: $ifp-fs-5;
  margin-inline-start: $spacer-2;
  position: relative;
  top: 2px;
}

&__view-all {
  margin-inline-start: auto;
  margin-bottom: $spacer-2;

  .ifp-icon {
    display: inline-block;
    margin-inline-end: $spacer-1;
    position: relative;
    top: 1px;
  }
}

&__prompt-right,
&__prompt-left,
&__prompt-cat-wrapper {
  display: flex;
}

&__prompt-right {
  align-items: center;
  margin-inline-start: $spacer-3;
}

&__prompt-left {
  flex-wrap: wrap;
  margin: $spacer-0 (
    -$spacer-2
  );
}

&__prompt-cat-wrapper {
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

&__prompt-category {
  justify-content: space-between;
  margin: $spacer-2;

  &::ng-deep {
    .ifp-btn {
      padding: $spacer-1 $spacer-3 !important;
    }

    .ifp-btn--tertiary {
      background-color: $ifp-color-grey-bg-2 !important;
      border: 1px solid $ifp-color-grey-7 !important;

      &:hover {
        border: 1px solid $ifp-color-active-blue !important;
        color: $ifp-color-active-blue !important;
      }
    }
  }

}

&__search-wrapper {
  margin-top: $spacer-2;
}

&__prompt-search {
  &::ng-deep {
    .ifp-search-box {
      height: 38px;
      border-radius: 30px;
    }
  }
}

&__button-sec {
  margin-top: $spacer-3;
  text-align: end;
}

&__beta-icon {
  width: 35px;
  height: auto;
  margin-inline-start: $spacer-2;
}

&__disclaimer-imp {
  margin-top: $spacer-2;
  text-align: center;
  font-weight: $fw-light;
}

&__sidebar-overlay {
  display: none;
}

&__fd-hand {
  margin-inline-start: $spacer-2;

  .ifp-icon {
    font-size: $ifp-fs-8;
    color: $ifp-color-grey-8;
    cursor: pointer;

    &--active {
      color: $ifp-color-secondary-blue;
    }
  }
}

&__fd-text-wrapper {
  position: absolute;
  background-color: #05264a4a;
  border-radius: 10px;
  transition: .3s;
  display: block;
  padding: $spacer-4;
  margin-top: $spacer-3;
  backdrop-filter: blur(10px);
  z-index: 1201;
}

&__fd-hand-up,
&__fd-hand-down {
  &--active {
    color: $ifp-color-blue-hover !important;
  }
}

&__fd-text-outer {
  position: relative;


}

&__fd-text {
  margin-bottom: $spacer-3;
  width: 340px;
  height: 120px;
  border-radius: 9px;
  outline: 0;
  resize: none;
  border: 1px solid $ifp-color-grey-5;
  padding: $spacer-3;
  display: block;
}


&__sidebar-feedback-outer {
  position: relative;
  opacity: 0;
  z-index: 1200;
  visibility: hidden;
  transition: 0.3s;

  &--active {
    opacity: 1;
    visibility: visible;
  }
}

&__sidebar-feedback {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: $spacer-4;
  border-inline-end: 1px solid $ifp-color-grey-7;
  background-color: $ifp-color-section-white;
}

&__sidebar-feedback-header {
  margin-bottom: $spacer-2;
  font-size: $ifp-fs-5;
  font-weight: $fw-bold;
}

&__sidebar-feedback-desc {
  margin-bottom: $spacer-2;
  font-size: $ifp-fs-4;
}

&__sidebar-feedback-text {
  width: 400px;
  height: 180px;
  resize: none;
  outline: 0;
  padding: $spacer-3;
  border-radius: 10px;
  border: 1px solid $ifp-color-grey-5;
}

&__sidebar-feedback-btn-wrapper {
  display: flex;
  margin-top: $spacer-4;
}

&__sidebar-feedback-btn-save {
  margin-inline-end: $spacer-4
}

&__sidebar-bottom {
  margin-top: auto;
  padding-bottom: $spacer-3;
}

&__sidebar-feedback-btn {
  display: block;
  margin-bottom: $spacer-3;

  &:last-child {
    margin-bottom: $spacer-0;
  }
}

&__sidebar-feedback-btn,
&__start-button {
  &::ng-deep {
    .ifp-btn {
      width: 100%;
    }

    .ifp-icon {
      font-size: $ifp-fs-7;
    }

    .ifp-btn--secondary {
      color: $ifp-color-sky-blue;
      text-transform: capitalize;
      border: 1px solid $ifp-color-sky-blue;
      border-radius: 35px;

      .ifp-icon {
        color: $ifp-color-sky-blue;
      }

      &:hover {
        color: $ifp-color-white;
        background-color: $ifp-color-sky-blue;

        .ifp-icon {
          color: $ifp-color-white;
        }
      }
    }
  }
}

&__star-rating {
  display: flex;
  justify-content: flex-end;
  margin: $spacer-3 (
    - $spacer-2
  );
flex-direction: row-reverse;
}

&__star-input,
&__radio {
  display: none;
}

&__star-input {
  &:checked {
    &~.ifp-ai__star-label {
      svg {
        fill: $ifp-color-yellow;
      }
    }
  }

}

&__sidebar-sec-1 {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

&__star-label {
  margin: $spacer-0 $spacer-2;
  cursor: pointer;

  svg {
    stroke: $ifp-color-yellow;
    transition: 0.3s;
  }

  &:hover {

    &~.ifp-ai__star-label svg,
    svg {
      stroke: $ifp-color-yellow;
      fill: $ifp-color-yellow;
    }
  }
}

&__modal-footer {
  padding: $spacer-3;
  text-align: center;
  background-color: $ifp-color-section-white;
  border-top: 1px solid $ifp-color-grey-7;
  border-radius: 0 0 0 20px;
}

&__maintainance {
  width: 100%;
}

&__menu {
  padding: $spacer-2 $spacer-3 $spacer-3;
  border-radius: 10px 10px 0 0;
  background-color: $ifp-color-section-white;
  display: inline-block;
  cursor: pointer;
  position: relative;

  &::after {
    content: "";
    width: 100%;
    height: 8px;
    background-color: $ifp-color-section-white;
    display: none;
    position: absolute;
    bottom: 6px;
    left: 0;
    z-index: 1;
  }

  .ifp-icon {
    font-size: $ifp-fs-5;
    position: relative;
    top: 3px;
  }
}

&__settings-dropdown {
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
}

&__settings {
  position: relative;
  margin-bottom: (-$spacer-3);
  margin-inline-start: (-$spacer-2);

  &--open {
    .ifp-ai {
      &__settings-dropdown {
        opacity: 1;
        visibility: visible;
      }

      &__menu,
      &__tab-wrapper {
        box-shadow: 0 0 6px $ifp-color-black-32;
      }

      &__menu::after {
        display: block;
      }
    }
  }
}

&__action-detail {
  border: 1px solid $ifp-color-blue-light-2;
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  padding: $spacer-2;
}

&__action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: $spacer-2;
  cursor: pointer;

  .ifp-icon {
    font-size: $ifp-fs-5;
  }
}

&__action-title {
  font-size: $ifp-fs-4;
  display: flex;
  font-weight: $fw-semi-bold;

  .ifp-icon {
    display: inline-block;
    margin-inline-end: $spacer-2;
  }
}

&__action-desc {
  line-height: 1.5;
  max-height: 0;
  overflow: hidden;
  transition: 0.3s;
  padding: $spacer-0 $spacer-2;

  &::ng-deep .customMarkdown {

    h2,
    h4 {
      font-weight: $fw-semi-bold;
      margin: $spacer-0 $spacer-0 $spacer-2;
    }

    p {
      margin-top: $spacer-0;
    }
  }
}

&__action-toggle-arrow {
  transition: 0.3s;
}

&__action-toggle {
  display: none;

  &:checked {
    &+.ifp-ai__action-header {
      .ifp-ai__action-toggle-arrow {
        transform: rotateX(180deg);
      }
    }

    &~.ifp-ai__action-desc {
      max-height: 400px;
      @include ifp-scroll-y(transparent, $ifp-color-grey-7, 5px, 5px);
    }
  }
}

&__dropdown-version {
  &::ng-deep {
    .ifp-dropdown {
      max-width: 100%;
    }

    .ifp-dropdown__list {
      width: 100%;
    }
  }
}

&--active {
  z-index: 1203;
}



:host-context([dir="rtl"]) {
  .ifp-ai {
    left: 32px;
    right: auto;
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-ai {

    &__query,
    &__input {
      color: $ifp-color-white-global;
    }
  }
}

&__link-item {
  color: inherit;
  font-size: $ifp-fs-3;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  transition: 0.3s;
}

&__hyper-link {
  font-size: $ifp-fs-3;
  color: $ifp-color-grey-9;
  background-color: $ifp-color-pale-blue;
  padding: ($spacer-2 + 2);
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  max-width: calc(33.33% - 33px);
  margin: $spacer-2;
  cursor: pointer;
  .ifp-icon {
    font-size: $ifp-fs-4;
    margin: $spacer-0 $spacer-2;
    transition: 0.3s;
  }
  &--more {
    font-weight: $fw-bold;
    .ifp-icon {
      margin: $spacer-0;
    }
  }
  &:hover {
    color: $ifp-color-blue-hover;
  }
}

&__hyper-link-official {
  color: $ifp-color-blue-hover;
}

&__smart-sugesion-wrapper {
  margin-bottom: $spacer-2;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-inline-end: $spacer-2;


  .ifp-icon {
    color: $ifp-color-orange;
    margin-inline-start: $spacer-1;
    font-size: $ifp-fs-6;
  }
}

&__smart-sugesion {
  padding: $spacer-2 $spacer-3;
  max-width: calc(33.33% - 16px);
  background-color: rgba(254, 195, 46, 0.19);
  border-radius: 10px;
  cursor: pointer;
}

&__sugesion-item {
  color: $ifp-color-secondary-grey;
  font-size: $ifp-fs-4;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

}

@include desktop-lg {
  .ifp-ai {
    &__icon {
      width: 60px;
    }

    &__sidebar {
      min-width: 330px;
    }

    &__modal-left {
      width: calc(100% - 330px);
    }
  }
}

@include desktop-sm {
  .ifp-ai {
    &__empty-icon {
      width: 60px;
      margin-bottom: $spacer-3;
    }

    &__empty-text {
      font-size: $ifp-fs-4;
    }

    &__cat-selected {
      padding: $spacer-2 $spacer-3;
    }

    &__selected-inner {
      margin-inline-end: $spacer-2;
    }

    &__cat-title,
    &__cat-item {
      font-size: $ifp-fs-3;
    }


    &__input {
      margin: $spacer-0 $spacer-2;
    }

    &__send {
      width: 35px;
      height: 35px;
      line-height: 35px !important;
      min-width: 35px;
      cursor: pointer;
    }

    &__send-icon {
      font-size: $ifp-fs-4;
    }

    &__stop-icon {
      width: 16px;
      height: 16px;
      min-width: 16px;
    }

    &__cat-icon {
      margin-inline-end: $spacer-2;
    }

    &__input-sec {
      padding-inline-end: $spacer-1;
    }

    &__accordian-head {
      padding: $spacer-2 + 2;
    }

    &__sidebar-overlay,
    &__sidebar {
      position: absolute;
      height: 100%;
      top: 0;
      bottom: 0;
      left: 0;
    }

    &__accordians {
      height: auto;
      max-height: calc(100vh - 370px);
    }

    &__sidebar-overlay {
      right: 0;
      background-color: $ifp-color-black-32;
      z-index: 1;
      border-radius: 0 0 20px 20px;
    }

    &__sidebar {
      z-index: 1;
      min-width: 0;
      width: auto;
      padding-top: $spacer-6;

      .ifp-collapse {
        display: inline-block;
        position: absolute;
        top: 16px;
        right: 16px;
      }
    }

    &__sidebar-sec-1 {
      width: 300px;
      transition: 0.3s;
      max-width: 0;
      overflow: hidden;
    }

    &__modal-body {
      position: relative;

      &--expand-sidebar {
        .ifp-ai {
          &__sidebar-sec-1 {
            max-width: 300px;
            overflow: visible;
          }

          &__sidebar-overlay {
            display: block;
          }
        }
      }
    }

    &__modal-left {
      width: 100%;
      margin-inline-start: $spacer-6 + 10;
    }

    &__chart {
      width: calc(100% - ($spacer-2 * 2));
    }

    &__query {
      font-size: $ifp-fs-3;
    }

    &__card-wrapper {
      margin-top: $spacer-2;
    }

    &__response {
      &--new {
        height: calc(100vh - 260px);
        @include ifp-scroll-y($ifp-color-grey-11, $ifp-color-grey-1, 8px, 8px);
      }
    }

    &__prompt-search {
      height: auto;

      &::ng-deep {
        .ifp-search-box__input {
          font-size: $ifp-fs-3;
        }
      }
    }

    &__prompt-category {
      margin: $spacer-2;
    }

    &__sidebar-title {
      font-size: $ifp-fs-3;
      margin-bottom: $spacer-3;
    }

    &__search {
      &::ng-deep {
        .ifp-search-box {
          height: auto;
        }
      }
    }

    &__disclaimer-imp {
      margin-top: $spacer-1;
    }

    &__left-inner {
      height: calc(100vh - 300px);
    }
  }
}

@include desktop-xl {
  .ifp-ai {
    &__empty-icon {
      width: 60px;
      margin-bottom: $spacer-2;
    }

    &__modal-left {
      padding: 12px $spacer-5;
    }
  }
}
