import { Injectable } from '@angular/core';
import { HttpService } from './http/http.service';
import { ToasterService } from './tooster/ToastrService.service';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ApiGenAiService {
  public apiUrl: string = environment.genAiBaseUrl+'/'  + environment.genAiChatVersion +'/';
 constructor(private _http: HttpClient, private __toast: ToasterService) { }


  getParams(params: any): any {
    return params ? Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&') : '';
  }


  getMethodRequest(url: string, params?: any, version ?: any): any{
    const query = this.getParams(params);
    return this._http.get(`${version ? environment.genAiBaseUrl+`/${version}/`  : this.apiUrl}${url}${query === '' ? query : `?${query}`}`);
  }

  getDownloadRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return  this._http.get(`${this.apiUrl}${url}${query === '' ? query : `?${query}`}`, {responseType: 'blob', observe: 'response'});
  }

  postMethodRequest(url: string, data?: any, doc?: { file?: File, key?: string }): any {
    if (doc?.file) {
      const formData = new FormData();
      formData.append(doc?.key ?? '', doc.file, 'signature.png');
      formData.append('data', JSON.stringify(data));
      return this._http.post(`${this.apiUrl}${url}`, formData);
    }
    return this._http.post(`${this.apiUrl}${url}`, data);
  }


  putMethodRequest(url: string, data?: any) : any{
    return this._http.put(`${this.apiUrl}${url}`, data);
  }

  patchMethodRequest(url: string, data?: any): any {
    return this._http.patch(`${this.apiUrl}${url}`, data);
  }


  getDeleteRequest(url: string, params?: any): any {
    const query = this.getParams(params);
    return this._http.delete(`${this.apiUrl}${url}${query === '' ? query : `?${query}`}`);
  }

  deleteWithoutQuery(url: string): any {
    return this._http.delete(this.apiUrl+url);
  }

  getUploadData(url: string, data: FormData ) {
    return this.fileUpload(url, data);
  }
  fileUpload(url: string, data: any = {} ): Observable<any | any[]> {
    return this._http.post(this.apiUrl + url, data, {
      reportProgress: true,
      observe: 'events'
    } );
  }

  errorHandler(error: any) {
    if (typeof error === 'string' ) {
      if (error!== '') {
        this.__toast.error(error);
      }
    } else if (error?.message) {
      this.__toast.error(error?.message);
    } else if (error?.error) {
      this.__toast.error(error?.error);
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string' ) {
            data = element;
          } else {
            element?.forEach((elementValue:string) => {
              data =  `${data} ${elementValue}`;
            });
          }
          if (data!== '') {
            this.__toast.error(data);
          }
        }
      }
    }

  }
}
