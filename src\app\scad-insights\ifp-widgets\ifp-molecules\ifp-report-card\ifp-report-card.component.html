@if(unauthorized && addMyAppsLanding) {
  <ifp-unauthorized-card [nodeId]="id" [contentType]="contentType" [isPending]="isAccessPending"></ifp-unauthorized-card>
} @else {
  <div class="ifp-report-card" *ngIf="!loader;else load" [ngClass]="{'ifp-report-card--custom': isCustomNewsLetter}" (click)="openNewsLetter()">
    <div class="ifp-report-card__head">
      <img [src]="img" alt="" class="ifp-report-card__banner">
      <ifp-analysis-card-header  (myApps)="myappsEvent($event)" [openInNewTabEnable]="openInNewTabEnable" [queryParams]="queryParams" [addMyApps]="addMyApps" [addMyAppsLanding]="addMyAppsLanding" [small]="small" [icons]="domains" [iconColor]="'white'" [id]="this.id" [title]="name" [link]="url" [contentType]="contentType" [enableNotification]="enableNotification" ></ifp-analysis-card-header>

      <!-- Header with notificaiton -->
      <!-- <ifp-analysis-card-header [openInNewTabEnable]="openInNewTabEnable" [queryParams]="queryParams" [addMyApps]="addMyApps"  [addMyAppsLanding]="addMyAppsLanding"  [notification]="notificationSelector" [small]="small" [icons]="domains" [iconColor]="'white'" (notificationEvent)="addNotification($event)" [id]="this.id" [title]="name" [link]="url" [contentType]="contentType" ></ifp-analysis-card-header> -->

    </div>
    <div class="ifp-analysis ifp-analysis--hybrid">
      <div class="ifp-analysis__left" [ngClass]="{'ifp-analysis__left--minimize': !this.small}">
        <h3 class="ifp-analysis__heading" *ngIf="name">{{name.charAt(0).toUpperCase() + name.slice(1) | translate}}</h3>
        <p class="ifp-analysis__substitle">{{(description | quotRemove) | translate}}</p>
        <div class="ifp-analysis__txt-icons">
          <div class="ifp-analysis__txt-icon" *ngIf="publish">
            <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="publish"
              [key]="dateLabel"></ifp-icon-text>
          </div>
          <div class="ifp-analysis__txt-icon" *ngIf="source">
            <ifp-icon-text [icon]="'ifp-icon-text'" [text]="source" [key]="'Source'"></ifp-icon-text>
          </div>
        </div>

        <div class="ifp-report-card__button-sec">
          <!-- <ifp-button  *ngIf="remove"  [buttonClass]="buttonClass.icon" [buttonColor]="'black'" class="ifp-report-card__cross"
          [iconClass]="'ifp-icon-round-cross'" (ifpClick)="removeEvent()"></ifp-button> -->
          <div class="ifp-report-card__tags-wrapper">
            @if (security && security.name) {
              <ifp-tag class="ifp-report-card__tag" [isBoxView]="true" [background]="'transparent'" [tagName]="security.name" [color]="security.color ?? ''" [infoHead]="('Data classification' | translate) + ': ' + security.name" [info]="security.description ?? ''"></ifp-tag>
            }
            @if (shouldShowDropdownTag) {
              <ifp-tag class="ifp-report-card__dropdown-tag" [isBoxView]="true" [background]="'#bcbcbc'" [tagName]="dropdownTagText" [color]="'#f3f6f4'" [border]="true"></ifp-tag>
            }
          </div>

          <div class="ifp-report-card__btn-footer">
            @if (isHideShareCheck) {
              <app-ifp-checkbox [type]="'checkbox'" [enableFor]="true" [hideLabel]="true" [id]="id" (checkedEvent)="selectNode($event)" [defualtChecked]="isSelected"></app-ifp-checkbox>
            }
            @if (remove) {
              <ifp-button [buttonClass]="buttonClass.icon" [buttonColor]="buttonColor.black" class="ifp-report-card__cross"
              [iconClass]="'ifp-icon-round-cross'" (ifpClick)="removeEvent()"></ifp-button>
            }
            @if (type === 'publications') {
              <ifp-button [buttonClass]="buttonClass.icon" [buttonColor]="showDocuments ?  'blue' : 'black'" [tooltipValue]="'Download'" class="ifp-report-card__download" [iconClass]="'ifp-icon-download'" (ifpClick)="showDocuments = !showDocuments"></ifp-button>
            }
          </div>
        </div>
        <app-ifp-document-download [documentList]="documentList" [inputId]="id" [tncState]="tncState" class="ifp-report-card__doc-sec" [ngClass]="{'ifp-report-card__doc-sec--with-dropdown': shouldShowDropdownTag}" (checkTnC)="isTnCagreed($event)" *ngIf="showDocuments"></app-ifp-document-download>
      </div>
    </div>
  </div>
  <ng-template #load>
    <app-ifp-card-loader class="ifp-loader" [type]="'small'"></app-ifp-card-loader>
  </ng-template>
  <app-ifp-modal #subscribeNotification [modalClass]="'ifp-modal__box-sm'" [enableOverlay]="false"
    [modalModifier]="'ifp-modal--alert-box'">
    <app-ifp-alert-box *ngIf="isSubscNotifOpen" (alertResponse)="setEmailNotifStatus($event)" (closeAlert)="closeModal()"
      style="display: block;"></app-ifp-alert-box>
  </app-ifp-modal>
}


<app-ifp-modal #tncModal [modalClass]="'ifp-modal__template-certificate'">
  <app-ifp-tnc-modal (termsResponse)="termsResponse($event)" [isAccepted]="false"></app-ifp-tnc-modal>
</app-ifp-modal>
