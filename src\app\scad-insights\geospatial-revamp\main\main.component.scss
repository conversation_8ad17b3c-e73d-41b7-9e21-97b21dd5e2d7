@use "../../../../assets/ifp-styles/abstracts/index" as *;

::ng-deep html, ::ng-deep body {
  height: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;
  padding-top: 0px!important;
}

::ng-deep body[dir="rtl"] {
  span, p, div, label, h1, h2, h3, h4, h5, h6 {
    font-family: $ff-tajawal ; 
  }
}

.geospatial-revamp-container {
  position: relative;
  height: 100vh; 
  overflow: hidden; 
}

@media only screen and (min-width: 1920px) {
  .geospatial-revamp-container {
    width: 100%;
    max-width: 1920px;
    margin-right: auto;
    margin-left: auto;
  }
}

// Filters Animation Wrapper
.filters-wrapper {
  position: relative;
  z-index: 4;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center top;
}

.filters-wrapper--visible {
  transform: translateY(0) scale(1);
  opacity: 1;
  pointer-events: auto;
}

.filters-wrapper--hidden {
  transform: translateY(-100vh) scale(0.8);
  opacity: 0; 
  pointer-events: none;
}

// Indicators Animation Wrapper  
.indicators-wrapper {
  position: relative;
  z-index: 3;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center bottom;
}

.indicators-wrapper--visible {
  transform: translateY(0) scale(1);
  opacity: 1;
  pointer-events: auto;
}

.indicators-wrapper--hidden {
  transform: translateY(100vh) scale(0.8);
  opacity: 0;
  pointer-events: none;
}

// Map container animations
.map-container--preview {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  // transform: scale(1.02);
}

.full-screen {
  height: 750px;
  transition: height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

// Staggered animation delays for smoother experience
.filters-wrapper--hidden {
  transition-delay: 0s;
}

.indicators-wrapper--hidden {
  transition-delay: 0.1s; // Slight delay for staggered effect
}

.filters-wrapper--visible {
  transition-delay: 0.2s; // Delay when coming back
}

.indicators-wrapper--visible {
  transition-delay: 0.1s;
}

.ifp-geospatial {
  &__breadcrumb-wraper {
    position: relative;
    left: 25px;
    z-index: 3;
    display: inline-flex;
    align-items: center;
    padding: 0px $spacer-4;
    border-radius: 5px;

    .ifp-icon {
      position: relative;
      top: 2px;
      margin: $spacer-0 ($spacer-1 + 2);
      font-size: $ifp-fs-1;
    }
  }

  &__bread-title {
    font-size: $ifp-fs-3;
    font-weight: $fw-semi-bold;

    &--last {
      color: $ifp-color-grey-9;
    }
  }

  &__btn-wrapper {
    transform-origin: left;
    transform: rotate(90deg);
    left: 15px;
    top: 85px;
    position: fixed;
    z-index: 10; // Higher z-index to stay on top during animations
    display: flex;
    transition: all 0.3s ease;
  }

  &__btn-wrapper__ar{
    transform-origin: right;
    transform: rotate(270deg);
    right: 15px;
  }

  &__custom-btn {
    background: rgba(255, 255, 255, 0.4)!important; // Increased opacity for more visible white
    -webkit-backdrop-filter: blur(8px) saturate(180%)!important;
    backdrop-filter: blur(8px) saturate(180%)!important;
    border: 1px solid rgba(255, 255, 255, 0.5); // Increased border opacity
    padding: $spacer-2 ($spacer-2 + 2);
    position: relative;
    z-index: 3;
    color: #333;
    display: block;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin: $spacer-0 $spacer-2;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.4); // Increased inset highlight

    &:hover {
      background: rgba(255, 255, 255, 0.5)!important; // Increased hover opacity
      border: 1px solid rgba(255, 255, 255, 0.6);
      transform: scale(1.02) translateY(-1px);
      box-shadow: 
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    &--active {
      background: rgba(255, 255, 255, 0.6)!important; // Increased active opacity
      border: 1px solid rgba(255, 255, 255, 0.7);
      color: #333;
      pointer-events: none;
      box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 2px 4px rgba(255, 255, 255, 0.4);
    }

    &--unactive {
      visibility: hidden!important;
    }

    &--checkbox {
      width: 13px;
      height: 13px;
      margin-top: 5px;
      appearance: none;
      background: rgba(255, 255, 255, 0.4); // Increased checkbox background
      border: 1px solid rgba(255, 255, 255, 0.6); // Increased checkbox border
      border-radius: 4px;
      cursor: pointer;
      position: relative;
      top: 3px;
      left: 5px;
      right: 5px;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
        border: 1px solid rgba(74, 144, 226, 0.7); // Increased blue border
        box-shadow: 0 0 10px rgba(74, 144, 226, 0.4); // Enhanced glow
        transform: scale(1.05);
      }
    }

    input[type="checkbox"]:checked::before {
      content: '\2714'; 
      position: absolute;
      top: -1px;
      left: 0;
      right: 0;
      bottom: 0;
      font-size: 12px;
      color: #fff;
      text-align: center;
      line-height: 13px;
      font-weight: bold;
    }
    
    input[type="checkbox"]:checked {
      background: rgba(74, 144, 226, 0.9); // Increased checked background
      border: 1px solid rgba(74, 144, 226, 1);
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3); // Enhanced checked glow
    }

    // Support for older browsers without backdrop-filter
    @supports not (backdrop-filter: blur(8px)) {
      background: rgba(255, 255, 255, 0.7)!important; // Higher fallback opacity
      
      &:hover {
        background: rgba(255, 255, 255, 0.8)!important;
      }
      
      &--active {
        background: rgba(255, 255, 255, 0.9)!important;
      }
    }
  }
}

.level-selection {
  width: 140px;
  position: absolute;
  bottom: 118px;
  left: 365px;
  background-color: #4F4F4F;
  padding: 6px 12px;
  color: #fff;
  cursor: pointer;
  border-radius: 5px;
  z-index: 3;
  transition: all 0.3s ease;

  &:hover {
    background-color: #5F5F5F;
    transform: translateY(-2px);
  }
}

.arrow-down {
  position: relative;
  top: -2px;
  left: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
  transition: transform 0.2s ease;
}

.level-selection:hover .arrow-down {
  transform: rotate(45deg) translateY(2px);
}

.download-btn {
  position: absolute;
  bottom: 118px;
  left: 600px;
  background-color: #4F4F4F;
  padding: 5px 7px;
  color: #fff;
  cursor: pointer;
  border-radius: 5px;
  z-index: 3;
  transition: all 0.3s ease;

  &:hover {
    background-color: #5F5F5F;
    transform: translateY(-2px);
  }
}

// Loading indicator
.loading-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #ccc;
  border-top-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Enhanced animations for mobile devices
@media (max-width: 768px) {
  .filters-wrapper--hidden,
  .indicators-wrapper--hidden {
    transition-duration: 0.4s; // Faster on mobile
  }
  
  .filters-wrapper--visible,
  .indicators-wrapper--visible {
    transition-duration: 0.4s;
  }
}

