@use "../../../../../../../assets/ifp-styles/abstracts/index" as *;

/* Common styles for elements outside the container */
.indicator-open, .indicator-close {
  background: #ffffff99;
  transition: height 0.5s ease, background 0.5s ease;
  padding: 15px;
  width: 250px;
  position: relative;
  z-index: 2;
}

.indicator-open {
  border-radius: 15px 15px 0px 0px;
  margin-bottom: 3px;
}

.indicator-close {
  border-radius: 0px 0px 15px 15px;
  margin-top: 3px;
}

/* Common styles for all indicators */
.main-indicator,
.child-indicator {
  background: #ffffff99; 
  transition: all 0.4s ease;
  padding: 10px 15px;
  margin-bottom: 3px;
  height: 260px;
  width: 250px;
}

/* Positioning for indicators when all are visible */
.child-indicator.population.all-visible  {
  transform: translateX(252px);
  position: absolute;
  top: 33px;
  z-index: 5;
  display: block !important;
}

.child-indicator.real-estate.all-visible {
  transform: translateX(504px);
  position: absolute;
  top: 33px;
  z-index: 5;
  display: block !important;
}

.main-indicator.active,
.child-indicator.active {
  z-index: 10;
  background: #ffffffd9;
}

/* Indicator header styles */
.main-indicator .indicator-header,
.child-indicator .indicator-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ifp-horizontal-icons{
  border: 1px solid #000;
  border-radius: 50px;
  position: relative;
  top: -3px;
  em {font-size: 10px!important}
}

.indicator-header .right-icons em {
  margin: 0px 5px;
}

.indicator-header .ifp-icon {
  cursor: pointer;
  font-size: 18px;
}

.indicator-header .left-icons em {
  font-size: 24px;
}

.indicator-header .right-icons {
  position: relative;
}

/* National split styles */
.main-indicator-national-split,
.child-indicator .main-indicator-national-split {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}

.main-indicator-national-split .national-chart {
  display: flex;
  flex-direction: row;
}

.main-indicator-national-split .national-chart img {
  width: 2em;
}

.main-indicator-national-split .national-chart .indicator-values {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.main-indicator-national-split .national-chart .indicator-values span {
  font-size: 12px;
}

.main-indicator-national-split .national-chart .indicator-values .label {
  font-weight: 300;
}

.main-indicator-national-split .national-chart .indicator-values .value {
  font-weight: bold;
}

.main-indicator-national-split .national-chart .progress-bar-charts {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.main-indicator-national-split .national-chart .progress-bar-charts .progress-bar-line {
  height: 8px;
  cursor: pointer!important;
  border-radius: 5px;
}

.main-indicator-national-split .national-chart .progress-bar-charts .progress-bar-line.green {
  background-color: #2A9F08;
}

.main-indicator-national-split .national-chart .progress-bar-charts .progress-bar-line.orange {
  background-color: #ff6a10;
}

.main-indicator-national-split .national-chart .progress-bar-charts span {
  font-size: 8px;
  font-weight: 400;
  margin: -2px 0px 0px 2px;
}

/* Gender split styles */
.main-indicator-gender-split,
.child-indicator .main-indicator-gender-split {
  display: flex;
  flex-direction: row;
  margin-top: 20px;
}

.main-indicator-gender-split .gender-chart {
  display: flex;
  flex-direction: row;
}

.main-indicator-gender-split .gender-chart img {
  width: 1.5em;
  height: 1.5em;
  display: flex;
  flex-direction: row;
}

.main-indicator-gender-split .gender-chart .indicator-values {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.main-indicator-gender-split .gender-chart .indicator-values span {
  font-size: 12px;
}

.main-indicator-gender-split .gender-chart .indicator-values .label {
  font-weight: 300;
}

.main-indicator-gender-split .gender-chart .indicator-values .value {
  font-weight: bold;
}

/* Indicator footer styles */
.main-indicator-footer,
.child-indicator .main-indicator-footer {
  position: relative;
  left: 180px;
  margin-top: 20px;
}

.child-indicator .main-indicator-footer {
  left: 150px;
}

.main-indicator-footer em,
.child-indicator .main-indicator-footer em {
  margin: 10px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.main-indicator-footer em:hover,
.child-indicator .main-indicator-footer em:hover {
  transform: scale(1.2);
  color: #0056b3;
}

.main-indicator-footer em.disabled,
.child-indicator .main-indicator-footer em.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.main-indicator-footer em.disabled:hover,
.child-indicator .main-indicator-footer em.disabled:hover {
  transform: none;
  color: inherit;
}

/* Main indicator specific styles */
.main-indicator .main-indicator-title {
  display: flex;
  flex-direction: column;
}

.main-indicator .main-indicator-title #district-name {
  font-size: 12px;
  margin-bottom: 5px;
}

.main-indicator .main-indicator-title span {
  font-size: 11px;
  font-weight: bold;
}

/* Child indicator specific styles */
.child-indicator .child-indicator-title {
  display: flex;
  flex-direction: column;
  margin-top: 15px;
}

.child-indicator .child-indicator-title span {
  font-size: 12px;
}

.child-indicator .indicator-header .right-icons {
  display: flex;
  justify-content: center;
}

.child-indicator .indicator-header .right-icons span {
  font-size: 12px;
}

.child-indicator .indicator-header .right-icons input {
  margin-left: 5px;
}

.child-indicator .indicator-header .left-icons img {
  width: 1.8em;
}

/* Indicator card details styles (outside container) */
.indicator-card-details {
  background: #ffffff99;
  transition: height 0.5s ease, background 0.5s ease;
  padding: 10px 15px;
  width: 250px;
  position: relative;
  z-index: 2;
}

.indicator-card-details .indicator-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.indicator-card-details .indicator-header .right-icons em {
  margin: 0px 5px;
}

.indicator-card-details .indicator-header .ifp-icon {
  cursor: pointer;
  font-size: 18px;
}

.indicator-card-details .indicator-header .left-icons em {
  font-size: 24px;
}

.indicator-card-details .indicator-header .right-icons {
  position: relative;
}

.indicator-card-details .indicator-chart {
  background-color: #ffffffc3;
  padding: 10px;
  border-radius: 10px;
  margin-bottom: 3px;
}

.indicator-card-details .indicator-chart .chart-header {
  display: flex;
  justify-content: space-between;
}

.indicator-card-details .indicator-chart .chart-header span {
  font-size: 12px;
  font-weight: bold;
}

.indicator-card-details .indicator-chart .progress-bar-charts {
  display: flex;
}

.indicator-card-details .indicator-chart .progress-bar-charts .progress-bar-line {
  height: 8px;
  cursor: pointer!important;
}

.indicator-card-details .indicator-chart .progress-bar-charts .progress-bar-line.total {
  border-radius: 5px 0px 0px 5px;
  background-color: #2A9F08; 
}

.indicator-card-details .indicator-chart .progress-bar-charts .progress-bar-line.remaining-stats {
  border-radius: 0px 5px 5px 0px;
  background-color: #979494;
}

.indicator-card-details .indicator-chart .progress-bar-charts span {
  font-size: 8px;
  font-weight: 400;
  margin: -2px 0px 0px 2px;
}

.indicator-card-details .statistics-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.indicator-card-details .statistics-container .indicator-statistics {
  margin-top: 10px;
  flex: 1 1 calc(50% - 10px);
}

.indicator-card-details .statistics-container .indicator-statistics .statistics {
  display: flex;
}

.indicator-card-details .statistics-container .indicator-statistics .statistics img {
  width: 1.5em;
  margin-right: 5px;
}

.indicator-card-details .statistics-container .indicator-statistics .statistics .statistics-values {
  display: flex;
  flex-direction: column;
}

.indicator-card-details .statistics-container .indicator-statistics .statistics .statistics-values span {
  font-size: 10px;
}

.indicator-card-details .statistics-container .indicator-statistics .statistics .statistics-values span#value {
  font-size: 10px;
  font-weight: bold;
}

/* Animation styles */
:host {
  display: block;
  overflow: hidden;
}

/* Sliding animation styles */
.main-indicator, 
.child-indicator {
  backface-visibility: hidden;
  will-change: transform, opacity;
  transition: transform 0.4s ease-out, opacity 0.4s ease-out;
}