.custom-multiselect {
    position: relative;
    width: 100%;
    font-family: Arial, sans-serif;
    border-right: 1px solid #FFF;
  }

  .select-trigger {
    position: relative; 
    padding: 8px 12px;
    border-radius: 4px;
    background-color: transparent;
    color: #000;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
  }


  .arrow-down {
    position: relative;
    top: -5px;
    border: solid #000;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg); 
  }

  .options-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    border-radius: 4px;
    margin-top: 4px; 
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
  }

  .options-list{
    background-color: #ffffff99;
    padding-top: 20px;
    padding-bottom: 20px;
    .region-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      padding: 8px 12px 8px 12px;
    }
    .region-item.active{
      background-color: #ffffff;
    }
    .region-item:hover{
      background-color: #ffffff;
    }
  }

  



 
  