@use "../../../../../../assets/ifp-styles/abstracts/index" as *;

.geospatial-revamp-box {
  z-index: 2;
  padding: 15px;
  background: #ffffff99;
  transition: height 0.5s ease, background 0.5s ease;
  margin: 2.5px 0px;
}

.geospatial-revamp-box .indicator-card {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.indicator-card .indicator-header{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.indicator-header em{
  font-size: 1.6rem;
}

.indicator-header .right-icons em{
  margin: 0px 5px;
}

.indicator-card .indicator-title{
  margin: 5px 0px;
  font-weight: bold;
}


.right-icons .ifp-icon {
  cursor: pointer;
}

.left-icons img{
  width: 2.8rem;
}

.right-icons {
  position: relative;
}


.ifp-chart-icon {
  background-color: $ifp-color-white;
  box-shadow: 0 0 $spacer-3 $ifp-color-black-16;
  border-radius: 6px;
  display: flex;
  align-items: center;
  left: auto;
  right: 14px;
  position: absolute;
  padding: $spacer-2;
  z-index: 3;

  .ifp-icon {
    padding: $spacer-2;
    border: 1px solid $ifp-color-grey-7;
    margin: $spacer-2;
    cursor: pointer;
  }
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #998f8f;
}


.ifp-geo-indicator {
  &__loading-overlay {
    // position: absolute;
    // top: 0;
    // left: 0;
    // right: 0;
    // bottom: 0;
    // background: rgba(255, 255, 255, 0.15);
    // backdrop-filter: blur(8px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    pointer-events: all;
    user-select: none;
  }
  
  &__loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacer-3;
    
    .wave-loader {
      display: flex;
      gap: 4px;
      align-items: flex-end;
      height: 40px;
      
      .wave-bar {
        width: 4px;
        background: #149ece;
        border-radius: 2px;
        animation: wave 1.2s ease-in-out infinite;
        
        &:nth-child(1) { animation-delay: 0s; }
        &:nth-child(2) { animation-delay: 0.1s; }
        &:nth-child(3) { animation-delay: 0.2s; }
        &:nth-child(4) { animation-delay: 0.3s; }
        &:nth-child(5) { animation-delay: 0.4s; }
      }
    }
    
    .loading-text {
      font-size: $ifp-fs-4;
      font-weight: $fw-semi-bold;
      color: #149ece;
      margin: 0;
    }
  }
}

// Keyframe animation for wave bars
@keyframes wave {
  0%, 40%, 100% { 
    height: 8px; 
    opacity: 0.4;
  }
  20% { 
    height: 32px; 
    opacity: 1;
  }
}

// Alternative wave animation (more fluid)
@keyframes wave-fluid {
  0%, 100% { 
    height: 8px; 
    transform: scaleY(0.4);
  }
  50% { 
    height: 32px; 
    transform: scaleY(1);
  }
}
