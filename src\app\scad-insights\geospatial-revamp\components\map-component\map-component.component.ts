import { AfterViewInit, Component, EventEmitter, OnDestroy, OnInit, Input, Output, ViewChild, input } from '@angular/core';
import { CensusMapComponent } from './census-map/census-map.component';

@Component({
  selector: 'ifp-map-component',
  standalone: true,
  imports: [
    CensusMapComponent,
  ],
  templateUrl: './map-component.component.html',
  styleUrl: './map-component.component.scss'
})
export class MapComponentComponent implements OnInit, AfterViewInit, OnDestroy {

  @Input() isPreviewOpen: boolean = false;
  @Input() isCustomizeOpen: boolean = false;
  @Input() filtersData: any = [];
  @Output() filterChange = new EventEmitter();


  @Output() regionChanged = new EventEmitter();
  @Output() regionChangedFromMap = new EventEmitter();

  @Output() districtsChange = new EventEmitter();
  @Output() districtChangedFromMap = new EventEmitter();

  @Output() communitiesChange = new EventEmitter();
  @Output() communityChangedFromMap = new EventEmitter();

  @ViewChild('censusMapCmp') censusMapCmp!: CensusMapComponent;
  


  constructor() {}

  ngOnInit(): void {
   
  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }


  filterChanged(filter: any) {
    this.filterChange.emit(filter);
  }

  resetFilter(reset: boolean) {

    this.censusMapCmp.resetSelectionLevel(reset);
  }

  updateSelectionLevel(level: string) {
    this.censusMapCmp.updateSelectionLevel(level);
  }

  regionChange(regions: any) {
    this.regionChanged.emit(regions);
  }
  regionChangeFromMap(regions: any) {
    this.regionChangedFromMap.emit(regions);
  }

  districtChange(districts: any) {
    this.districtsChange.emit(districts);
  }
  districtChangeFromMap(communites: any) {
    this.districtChangedFromMap.emit(communites);
  }

  communityChange(communites: any) {
    this.communitiesChange.emit(communites);
  }
  communityChangeFromMap(communities: any) {
    this.communityChangedFromMap.emit(communities);
  }

  


}
