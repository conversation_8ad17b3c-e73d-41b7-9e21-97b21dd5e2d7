import {
  Component,
  OnInit,
  OnDestroy,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  ViewChild,
  ElementRef,
  EventEmitter,
  AfterViewInit,
} from '@angular/core';
import MapView from '@arcgis/core/views/MapView';
import Expand from '@arcgis/core/widgets/Expand';
import { TranslateService } from '@ngx-translate/core';
import { CensusServiceService } from '../../../../../../census-service.service';


import {SubSink} from "subsink";
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { geoMapKeys } from 'src/app/scad-insights/geospatial-revamp/geospatial.contants';

interface District {
  district_name: string;
  district_id: number;
}

@Component({
  selector: 'ifp-map-search',
  standalone: true,
  imports: [],
  template: `<div #districtSearchNode></div>`,
  styleUrl: './map-search.component.scss',
})
export class MapSearchComponent
  implements OnInit, OnDestroy, OnChanges, AfterViewInit
{

  subsink: SubSink = new SubSink();

  @ViewChild('districtSearchNode') districtSearchNode: ElementRef | undefined;
  @Input() view!: MapView;
  @Input() filtersData: any = [];

  @Output() districtChange = new EventEmitter();
  @Output() filterChange = new EventEmitter();
  @Output() locationChange = new EventEmitter();

  private expand: Expand | undefined;

  public filterObject: any = geoMapKeys.defaultQueryParams;
  public districtSelect: any = [];
  public selectElement: any;
  public language: string = 'en';

  constructor(
    private _translate: TranslateService,
    private gisSharedService: CensusServiceService,
    private themeService: ThemeService
  ) {
  }

  ngOnInit(): void {

    this.subsink.add(
      this.themeService.defaultLang$.subscribe((lang: string) => {
        this.language = lang;
      })
      // this.gisSharedService._districtLayerUrl$.subscribe((layerUrl: string) => {
      //   if(layerUrl) {
      //     console.log(layerUrl);
      //   }
      // })
    );

    const districtdata = this.gisSharedService.getDistrictsFromLocalStorage();
    if (districtdata && Array.isArray(districtdata.features)) {
      this.districtSelect = districtdata.features.map((feature: any) => ({
        district_name: (feature.attributes.district_eng).trim(),
        district_name_ara: (feature.attributes.district_ara).trim(),
        district_id: feature.attributes.district_id,
      }));
      // Sorting with special condition
      this.districtSelect.sort((a: District, b: District) => {
        if (a.district_name === "Abu Dhabi") return -1;
        if (b.district_name === "Abu Dhabi") return 1;
        return a.district_name.localeCompare(b.district_name);
      });
    } else {
      console.error('No valid data found in local storage.');
    } 
    this.subsink.add(
      this.gisSharedService.insightSearchSelectedDistrict$.subscribe((district_id: any) => {
        if(district_id) {
          this.selectedValue(this.selectElement, district_id);
        }
      })
    );
  }

  ngAfterViewInit(): void {
    this.initializeWidget();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['view'] && !changes['view'].firstChange) {
      this.initializeWidget();
    }
  }

  ngOnDestroy(): void {
    this.destroyWidget();
    this.subsink.unsubscribe();
  }

  private initializeWidget() {
    this.destroyWidget();
    if (this.view) {
      const content = document.createElement('div');
      content.className = 'map-district-search';

      this.selectElement = document.createElement('select');
      this.selectElement.className = 'map-district-search-select';
     
      this.filtersData.districts = [geoMapKeys.allDistricts, ...this.filtersData.districts];
      this.filtersData.districts.forEach((optionData: any) => {
        const optionElement = document.createElement('option');
        optionElement.value = optionData.DISTRICT_CODE;
        optionElement.textContent = this.language == 'en' ? optionData.DISTRICT_EN : optionData.DISTRICT_AR;
        this.selectElement.appendChild(optionElement);
      });

      this.selectElement.addEventListener('change', (event: Event) => {
        const target = event.target as HTMLSelectElement;
        this.onChangeDistrictSelect(target.value);
      });

      content.appendChild(this.selectElement);

      this.expand = new Expand({
        view: this.view,
        content: content,
        expandIconClass: 'esri-icon-search',
        group: 'bottom-left',
        expandTooltip: this.language == 'en' ? 'Districts Search' : 'البحث حسب المناطق',
        collapseTooltip: this.language == 'en' ? 'Collapse' : 'تصغير'
      } as __esri.ExpandProperties); 

      this.view.ui.add(this.expand, 'bottom-left');
    }
  }

  private destroyWidget() {
    if (this.expand) {
      this.expand.destroy();
      this.expand = undefined;
    }
  }

  onChangeDistrictSelect(district_code: any) {
    const selectedDistrict = this.filtersData.districts.find((district: any) => district.DISTRICT_CODE == district_code);
    this.filterObject.DISTRICT_CODE = [selectedDistrict.DISTRICT_CODE];
    this.filterObject.COMMUNITY_CODE = [];
    const obj = [{
      'REGION_CODE': selectedDistrict.REGION_CODE,
      'DISTRICT_CODE': selectedDistrict.DISTRICT_CODE,
      'DISTRICT_AR': selectedDistrict.DISTRICT_AR,
      'DISTRICT_EN': selectedDistrict.DISTRICT_EN
    }];

    if(district_code == 0){
      this.filterObject.REGION_CODE = geoMapKeys.defaultRegionCodes;
      this.filterObject.DISTRICT_CODE = [];
      this.filterObject.COMMUNITY_CODE = [];
      this.filterChange.emit(this.filterObject);
      this.locationChange.emit(selectedDistrict.DISTRICT_EN);
      this.gisSharedService.MapToInitialLevel("fromIcon");
    } else {
      this.districtChange.emit(obj);
      this.filterChange.emit(this.filterObject);
      this.locationChange.emit(selectedDistrict.DISTRICT_EN);
      this.gisSharedService.zoomToDistrictBySelect([selectedDistrict]);
    }
  }

  selectedValue(selectElement: any, valueToSelect: any) {
    selectElement.value = valueToSelect; 
    // const event = new Event('change', { bubbles: true });
    // selectElement.dispatchEvent(event);
  }
}
