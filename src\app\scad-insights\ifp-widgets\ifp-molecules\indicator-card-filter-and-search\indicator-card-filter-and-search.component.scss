@use '../../../../../assets/ifp-styles/abstracts' as *;

.ifp-search-filter {
  &__filter-wrapper {
    display: flex;

    &--reverse {
      flex-direction: row-reverse;

      .ifp-search-filter__search {
        margin-right: $spacer-3;
      }
    }
  }

  &__dropdown-wrapper {
    display: flex;
    margin: 0px (-$spacer-2);
  }

  &__dropdown,
  &__button {
    margin: 0px $spacer-2;
  }

  &__dropdown {
    // &::ng-deep {
    //   .ifp-dropdown {
    //     min-width: 270px;
    //   }
    // }
  }

  &__search {
    margin-left: auto;
    display: block;
  }

  &__tools {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: $spacer-3;
  }

  &__domain-controls {
    display: flex;
    align-items: center;
    gap: $spacer-3;
    margin-right: auto;
  }

  &__domain-dropdown {
    min-width: 160px;
  }

  &__date-picker {
    min-width: 200px;
  }

  &--sort {
    .ifp-search-filter {
      &__tools {
        display: flex;
        align-items: center;
      }
      &__tools-item {
        margin: $spacer-0 $spacer-1;
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-search-filter {
    &__filter-wrapper {
      &--reverse {
        .ifp-search-filter__search {
          margin-right: $spacer-3;
          margin-left: $spacer-0;
        }
      }
    }
    &__search,
    &__tools
     {
      margin-right: auto;
      margin-left: $spacer-0;
    }
  }
}
:host::ng-deep {
  .ifp-search-filter {
    &__dropdown {
      .ifp-dropdown {
        max-width: none !important;
      }
    }
    // &__domain-controls {
    //   .ifp-dropdown {

    //   }
    // }
  }
}
::ng-deep .ifp-dropdown {
  // min-width: 0 !important;
  max-width: none !important;
}

@include mobile {
  .ifp-search-filter {
    &__filter-wrapper,
    &__dropdown-wrapper {
      display: block;
    }
    &__dropdown-wrapper {
      margin: $spacer-0;
    }
    &__tools {
      margin-top: $spacer-3;
      margin-left: $spacer-0;
    }
    &__button {
      margin: $spacer-0;
      display: block;
    }
  }
}

:host::ng-deep {
  @include mobile {
    .ifp-search-filter {
      &__dropdown {
        .ifp-dropdown__selected-text {
          font-size: $ifp-fs-4;
        }
      }
    }
  }
}
