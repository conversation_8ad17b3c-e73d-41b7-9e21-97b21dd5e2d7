
<div class="ifp-ai-insight-report-consolidated-domain">

    <div class="ifp-ai-insight-report-consolidated-domain__header">
      @if (domainIconSelector$ | async; as icon) {
        @if ((_themeService.defaultTheme$ | async) === 'dark') {
          <img [src]="icon?.body?.icon ??'../../../../assets/images/icon-placeholder.png'" alt="" width="25px" height="25px" class="ifp-ai-insight-report-consolidated-domain__domain-icon">
        } @else {
          <img [src]="icon?.body?.light_icon ?? '../../../../assets/images/icon-placeholder.png'" width="25px" height="25px"  alt="" class="ifp-ai-insight-report-consolidated-domain__domain-icon">
        }
      }

      <h3  class="ifp-ai-insight-report-consolidated-domain__head-text">{{domainSection()?.name}}</h3>
    </div>
    <div  class="ifp-ai-insight-report-consolidated-domain__body">
     <!-- Impact module start -->
     <div class="ifp-ai-report__impact ">
      <div class="ifp-ai-report__module-header">
        <h2 class="ifp-ai-report__module-title">{{'Impact' }}</h2>
        @if (previewNot()) {
        <ifp-ai-button [disableTranslate]="disableTranslate()" [iconClass]="'ifp-icon-edit'" [theme]="'ifp-ai-button--round'" [label]="'Edit'" (ifpClick)="callImpactEdit.emit({impacts: report()?.impact  , keyinsights:impacts()})"></ifp-ai-button>
        }
      </div>
      <div class="ifp-ai-report__section">
        <div class="ifp-ai-report__impact-card">
          <p class="ifp-ai-report__title">{{report()?.impact?.chart?.dashboardTitle ?? ''}}</p>
          @if ( report()?.impact?.chart?.charts?.[0]) {
            <ifp-ai-chart-card   [isDatalabel]="true"[enableAnimation]="previewNot()" [insightReport]="true" [isCustom]="true" [customChartData]=" report()?.impact?.chart?.charts?.[0]
          "></ifp-ai-chart-card>
          }

        </div>
        <div class="ifp-ai-report__impact">
          <p class="ifp-ai-report__title">{{report()?.impact?.heading ?? ''}}</p>
                     <ifp-mark-down  class="ifp-ai-report__matrics-markdown" [data]="report()?.impact?.text ?? ''"></ifp-mark-down>
          <!-- <p class="ifp-ai-report__desc">
            {{'Consumer spending has declined, with retail sales growth dropping from 5.2% in Q3 to 2.1% in Q4. The increased cost of borrowing has led consumers to cut back on discretionary spending, prioritizing essential goods and services instead. Higher interest rates have also affected borrowing activity, as mortgage applications fell by 17.9%, and credit card debt growth slowed from 8.5% to 6.3%, indicating reduced reliance on short-term credit.' }}
          </p>
          <p class="ifp-ai-report__desc">
            {{'Rising interest rates have also weakened consumer confidence, with the Consumer Confidence Index declining by 6.3 points, as individuals anticipate further economic uncertainty. Many are delaying large purchases such as homes and cars due to higher financing costs. This cautious approach has contributed to a slowdown in key sectors like real estate, retail, and automobile sales.' }}
          </p> -->
        </div>

      </div>
      <div class="ifp-ai-report__clear"></div>

        <p class="ifp-ai-report__module-title">{{'Key Insights:' }}</p>
        <div class="ifp-ai-report__insight-card-wrapper">
        @for (item of impacts(); track $index) {

          <ifp-ai-insight-analysis-card [enableAnimation]="previewNot()" [customChartData]="item.chart?.charts?.[0]" [shadow]="true" [title]="item.heading" [description]="item.text" [statusDown]="item.whether_positive_trend" [badge]="false" class="ifp-ai-report__insight-card"></ifp-ai-insight-analysis-card>
        }

      </div>
    </div>
    <!-- Impact module end -->
    @if (report()?.comparison?.rows?.length !==0 && report()?.comparison?.rows?.length) {
    <!-- Comparison module start -->
     <div class="ifp-ai-insight-report-consolidated-domain__compare">
      <div class="ifp-ai-insight-report-consolidated-domaint__compare-inner ifp-ai-insight-report-consolidated-domain__module-curve">
        <ifp-ai-insight-compare-module  [preview]="previewNot()" [compairData]="report()?.comparison" (edit)="callComparisonEdit.emit(report()?.comparison)" class="ifp-ai-report__compare-main"></ifp-ai-insight-compare-module>
      </div>
     </div>
    <!-- Comparison module end -->
    }

    <!-- Sentiment Analysis start -->
    <div>
      <div class="ifp-ai-report__module-header">
        <h2 class="ifp-ai-report__module-title">{{'Sentiment Analysis' }}</h2>
        @if (previewNot()) {
        <ifp-ai-button [disableTranslate]="disableTranslate()" [iconClass]="'ifp-icon-edit'"[theme]="'ifp-ai-button--round'" [label]="'Edit'" (ifpClick)="callSentimentalEdit.emit(sentimentanalysis())" ></ifp-ai-button>
        }
      </div>
      <div class="ifp-ai-report__module-body ">
        <div class="ifp-ai-report__sec-1">
          <ifp-ai-insight-consolidated-report-sentiment-card [enableAnimation]="previewNot()" [insights]="true" [sentimentanalysis]="sentimentanalysis()"></ifp-ai-insight-consolidated-report-sentiment-card>
        </div>
        <div class="ifp-ai-report__sec-2">
          <!-- <div  class="ifp-ai-report__card ifp-ai-report__sentimental-card">
            <p class="ifp-ai-report__title">{{'Sentiment Analysis'}}</p>
            <ifp-ai-chart-card [enableAnimation]="previewNot()" [height]="220" class="ifp-ai-insight-card__chart"  [insightReport]="true" [isCustom]="true" [customChartData]="sentimentanalysis()?.chart?.charts?.[0]
          "></ifp-ai-chart-card>
          </div> -->
          <p class="ifp-ai-report__title">{{'Key Insights:' }}</p>

          <p class="ifp-ai-report__desc">
            {{sentimentanalysis()?.key_insights_desc}}
          </p>
        </div>
      </div>
      <p class="ifp-ai-report__source">{{'Data sources' }} : <span class="ifp-ai-report__sources">{{sentimentanalysis()?.data_source}}</span>
      </p>
    </div>

    </div>


</div>
