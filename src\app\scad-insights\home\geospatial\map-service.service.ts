import { Injectable, EventEmitter } from '@angular/core';
import MapView from "@arcgis/core/views/MapView";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import DotDensityRenderer from "@arcgis/core/renderers/DotDensityRenderer.js";
import esriConfig from "@arcgis/core/config";
import { BehaviorSubject } from 'rxjs';
import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";
import {Polygon} from "@arcgis/core/geometry";
import Graphic from "@arcgis/core/Graphic";
import Expand from "@arcgis/core/widgets/Expand";
import LayerList from "@arcgis/core/widgets/LayerList";
import GroupLayer from "@arcgis/core/layers/GroupLayer";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol.js";
import Bookmarks from "@arcgis/core/widgets/Bookmarks";
import PortalBasemapsSource from "@arcgis/core/widgets/BasemapGallery/support/PortalBasemapsSource";
import BasemapGallery from "@arcgis/core/widgets/BasemapGallery";
import Portal from "@arcgis/core/portal/Portal.js";
import Measurement from "@arcgis/core/widgets/Measurement";
import ClassBreaksRenderer from "@arcgis/core/renderers/ClassBreaksRenderer.js";
import * as colorRendererCreator from "@arcgis/core/smartMapping/renderers/color.js";
import ColorVariable from "@arcgis/core/renderers/visualVariables/ColorVariable.js";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol.js";
import {ThemeService} from "../../core/services/theme/theme.service";
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class MapServiceService {

  public colorObjects: any = [
    [
      { name: "UAE Nationals", color: "#d75d5d" },
      { name: "Non-UAE Nationals", color: "#584cde" },
      // { name: "UAE Nationals", color: "#FF9D03" },
      // { name: "Non-UAE Nationals", color: "#4E81BD" },
    ],
    [
      { name: "UAE Nationals", color: "#FFFFFF" },
      { name: "Non-UAE Nationals", color: "blue" },
    ]
  ]

  public nationalsColrs: any = [];

  public genderColrs: any = [
    { name: "MALE", color: "#2986cc" },
    { name: "FEMALE", color: "#c90076" },
  ];

  constructor(
    private themeService: ThemeService, private _translate: TranslateService
  ) {
    this.themeService.defaultTheme$.subscribe((theme: any) => {
      if(theme === 'light') {
        this.nationalsColrs = this.colorObjects[0];
      } else {
        this.nationalsColrs = this.colorObjects[1];
      }
    })
  }

  private _onMaximizedClicked: EventEmitter<void> = new EventEmitter();
  public get onMaximizedClicked() {
    return this._onMaximizedClicked.asObservable();
  }

  private authStatusForServices = new BehaviorSubject<boolean>(false);
  authStatusForServices$ = this.authStatusForServices.asObservable();
  setAuthenticatedauthStatusForServices(authenticated: boolean) {
    this.authStatusForServices.next(authenticated);
  }

  private _dotDensityModule: any;
  set dotDensityModule(dotDensityModule: any) {
    this._dotDensityModule = dotDensityModule;
  }
  get dotDensityModule() {
    return this._dotDensityModule;
  }

  // Mapview Object is created with getter and setter
  private _view: MapView | undefined;
  set view(view: MapView) {
    this._view = view;
  }
  get view(): MapView {
    return <MapView>this._view;
  }

  setupEsriConfig(urls: any, token: string) {
    // @ts-ignore
    esriConfig.request.interceptors.push({
      urls: urls,
      before: function(params) {
        params.requestOptions.cacheBust = true;
        params.requestOptions.query = params.requestOptions.query || {};
        params.requestOptions.query.token = token;
      },
    });
    this.setAuthenticatedauthStatusForServices(true);
  }

  // Creating DotDensityLayer
  createDotdensityFeatureLayer(_url: any, layerName: string) {
    const url = _url;
    const dotDensityRenderer = new DotDensityRenderer({
      dotValue: 100,
      dotSize: 2,
      dotBlendingEnabled: false,
      outline: {
        color: [ 255,255,255, 0 ],
        width: 0.5
      },
      backgroundColor: [255, 255, 255, 0],
      legendOptions: {
        unit: "People"
      }
    });
    dotDensityRenderer.attributes = [
      // @ts-ignore
      { field: "TOTAL_CITIZEN", color: this.nationalsColrs[0].color, label: this.nationalsColrs[0].name },
      // @ts-ignore
      { field: "TOTAL_NON_CITIZEN", color: this.nationalsColrs[1].color,  label: this.nationalsColrs[1].name }
    ];

    const layer = new FeatureLayer({
      url: url,
      title: layerName,
      popupEnabled: false,
      outFields: ["*"],
      popupTemplate: {
        title: "{DISTRICTNAMEENG}",
        // content: [
        //   {
        //     type: "fields",
        //     fieldInfos: [
        //       {
        //         fieldName: "TOTAL_CITIZEN",
        //         label: "UAE",
        //         format: {
        //           digitSeparator: true,
        //           places: 0
        //         }
        //       },
        //       {
        //         fieldName: "TOTAL_NON_CITIZEN",
        //         label: "NON UAE",
        //         format: {
        //           digitSeparator: true,
        //           places: 0
        //         }
        //       },
        //     ]
        //   }
        // ]
        outFields: ["*"],
        content: (info: any) => {
          //
        },
      },
      renderer: dotDensityRenderer
    });
    // @ts-ignore
    this._view.map.layers.add(layer);
  }

  createChoroplethFeatureLayer(_url: string) {

    let layer = new FeatureLayer({
      url: _url,
      title: "Official Population Overview",
      popupEnabled: false,
      outFields: ["*"],
      opacity: 0.7,
    });

    let params = {
      layer: layer,
      field: "TOTAL_POPULATION",
      view: this.view,
      theme: "high-to-low",
      basemap: this.view.map.basemap,
      colorMixMode: "replace"
    };

    // @ts-ignore
    colorRendererCreator.createContinuousRenderer(params)
      .then(function(response) {
        layer.renderer = response.renderer;
        //let colorVariable = response.renderer.visualVariables[0];
        let colorVariable = response.renderer?.visualVariables?.[0];
        // @ts-ignore
        colorVariable.stops = [
          // @ts-ignore
          { value: colorVariable.stops[0].value, color: "#e4e5de" },
          // @ts-ignore
          { value: colorVariable.stops[colorVariable.stops.length - 1].value, color: "#A10F15" }
        ];
        //response.renderer.visualVariables = [colorVariable];
        layer.renderer = response.renderer;
      });
    // @ts-ignore
    this._view.map.layers.add(layer);
  }

  createFeatureLayerToMapDistricts(url: string, opacity: any, layerName: any, visible: any) {
    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName
    });

    const fillSymbol = new SimpleFillSymbol({
      color: [255, 255, 255, 0.2],
      outline: {
        color: [255, 255, 255],
        width: 1,
      },
    });

    featureLayer.renderer = {
      type: 'simple',
      //@ts-ignore
      symbol: fillSymbol,
    };

    // @ts-ignore
    this._view.map.layers.add(featureLayer);
  }

  setupClickHandler(view: MapView, callback: Function): void {
    view.on("click", (event) => {
      view.hitTest(event).then((response) => {
        if (response.results.length > 0) {
          //@ts-ignore
          const feature = response.results[0].graphic;
          callback(feature);
        }
      });
    });
  }

  drawPolygonToMap(featureRings: any) {
    this.view.graphics.removeAll();
    const convertedCoordinates = featureRings.map((coords: any) => {
      const [x, y] = coords;
      const point = webMercatorUtils.xyToLngLat(x, y);
      return [point[0], point[1]];
    });
    const polygon = new Polygon({
      rings: [convertedCoordinates]
    });
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: {
        //@ts-ignore
        type: 'simple-fill',
        color: [0, 100, 100, 0],
        outline: {
          color: [0, 255, 255],
          width: 3
        }
      }
    });
    this.view.graphics.add(polygonGraphic);
    // this.view.goTo(polygon.extent);
  }
  drawPolygonToMapDirect(featureRings: any) {
    this.view.graphics.removeAll();
    const polygon = new Polygon({
      rings: featureRings
    });
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: {
        //@ts-ignore
        type: 'simple-fill',
        color: [0, 100, 100, 0], // Set the fill color and transparency (red with 50% transparency)
        outline: {
          color: [0, 255, 255],
          width: 3
        }
      }
    });
    this.view.graphics.add(polygonGraphic);
    // this.view.goTo(polygon.extent);
  }

  drawPolygonToMapFromDistrictId(districtId: any) {
    const url = 'https://arcgisdev.scad.gov.ae/server/rest/services/IFP2/MapInsights_population_Static_20230831_official_Dev/MapServer/6';
      this.getSingleFeatureWithGeom(url, "district_id = "+districtId+"").then((feature: any) => {
        this.drawPolygonToMapDirect(feature.features[0].geometry.rings);
        // this.sharedService.displayDistrictName = "District: " + feature.features[0].attributes.DISTRICT_ENG;
        // this.calculateResults(feature.features[0].geometry, this.polygonSketchLayer);
        // this.addPolygonToMapDirect(feature.features[0].geometry.rings);
      });
    // this.view.graphics.removeAll();
    // const convertedCoordinates = featureRings.map((coords: any) => {
    //   const [x, y] = coords;
    //   const point = webMercatorUtils.xyToLngLat(x, y);
    //   return [point[0], point[1]];
    // });
    // const polygon = new Polygon({
    //   rings: [convertedCoordinates]
    // });
    // const polygonGraphic = new Graphic({
    //   geometry: polygon,
    //   symbol: {
    //     //@ts-ignore
    //     type: 'simple-fill',
    //     color: [0, 100, 100, 0],
    //     outline: {
    //       color: [0, 255, 255],
    //       width: 3
    //     }
    //   }
    // });
    // this.view.graphics.add(polygonGraphic);
    // // this.view.goTo(polygon.extent);
  }

  getSingleFeatureWithGeom(layerUrl: any, whereClause: any) {
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*'],
    });
    let query = layer.createQuery();
    query.where = whereClause;
    query.returnGeometry = true;
    return layer.queryFeatures(query);
  }

  async createSearchWidget(position: any) {
    // var searchDistrictDiv = document.createElement('div');
    // searchDistrictDiv.className = "search-district-tool-div";
    // var selectDistrict = document.createElement('select');
    // selectDistrict.className = "selectDistrict";
    // const res  = await this.sharedService.getDistinctValues(this.sharedService.mapConfig.layers[3], 'DISTRICT_ENG');
    // let infos = res.uniqueValueInfos;
    // const filteredArray = infos.filter(obj => !Object.values(obj).includes(null));
    // filteredArray.forEach((d: any) => {
    //   d.name = this.sharedService.allCapitalize(d.value)
    // });
    // //@ts-ignore
    // filteredArray.sort((a, b) => a.name.localeCompare(b.name));
    // filteredArray.forEach(option => {
    //   const optionElement = document.createElement('option');
    //   // @ts-ignore
    //   optionElement.value = option.value;
    //   // @ts-ignore
    //   optionElement.innerText = option.name;
    //   selectDistrict.appendChild(optionElement);
    // });
    // // Event listener for select box changes
    // selectDistrict.addEventListener('change', (event) => {
    //   // @ts-ignore
    //   const selectedValue = event.target.value;
    //   this.sharedService.getSingleFeatureWithGeom(this.sharedService.mapConfig.layers[3], "DISTRICT_ENG = '"+selectedValue+"'").then((feature: any) => {
    //     this.sharedService.displayDistrictName = "District: " + feature.features[0].attributes.DISTRICT_ENG;
    //     this.calculateResults(feature.features[0].geometry, this.polygonSketchLayer);
    //     this.addPolygonToMapDirect(feature.features[0].geometry.rings);
    //   });
    // });
    // searchDistrictDiv.appendChild(selectDistrict);
    // // const searchWidget = new Search();
    // const searchExpand = new Expand({
    //   view: this.mainView,
    //   content: searchDistrictDiv,
    //   expanded: false,
    //   expandIconClass: 'bi bi-search fs-5',
    //   expandTooltip: 'Search by district'
    // });
    // this.mainView.ui.add(searchExpand, {
    //   position: position
    // });
  }

  createMaximizeWidget(position: string) {
    var maximizeDiv = document.createElement('div');
    maximizeDiv.innerHTML = '<calcite-icon icon="extent" text-label="360 degree view" scale="l" title="Maximize the map"></calcite-icon>';
    maximizeDiv.className = "esri-widget--button esri-widget esri-interactive";
    maximizeDiv.id = "standalone-maximize";
    maximizeDiv.addEventListener('click', (evt) => {
      this._onMaximizedClicked.emit();
    });
    this.view.ui.add(maximizeDiv, position);
  }



  createLayerListWidget(position: any) {
    let layerlists = new LayerList({
      view: this.view,
    });
    const layerListExpand = new Expand({
      view: this.view,
      content: layerlists,
      expanded: false,
      expandTooltip: this._translate.instant('Layer list')
    });
    this.view.ui.add(layerListExpand, {
      position: position
    });
  }
  createGroupLayers(commonLayers: any) {
    const otherGroupLayer = new GroupLayer({
      title: "Other Layers",
      // visible: true,
      // visibilityMode: "exclusive",
      layers: [],
      // opacity: 0.75
    });
    // this.mainMap.add(otherGroupLayer);
    this.view.map.layers.add(otherGroupLayer);
    commonLayers.map((obj: any) => {
      this.createFeatureLayerToMapOthers(obj.endpoint, 0.9, obj.name, false, obj.light_icon, otherGroupLayer);
    });
  }
  createFeatureLayerToMapOthers(url: string, opacity: any, layerName: any, visible: any, icon: any, groupLayer: any) {
    const iconSymbol = new PictureMarkerSymbol({
      url: icon,
      width: "12px",
      height: "12px",
    });
    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName,
      renderer: {
        // @ts-ignore
        type: 'simple',
        symbol: iconSymbol,
      },
    });
    groupLayer.layers.push(featureLayer);
  }
  getGrouplayers(map: any) {
    const groupLayers: any = [];
    map.layers.forEach((layer: any) => {
      if (layer instanceof GroupLayer) {
        groupLayers.push(layer);
      }
    });
    return groupLayers;
  }
  filterOnLayer(layerTitle: string) {
    const nonBasemapLayers = this.view.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      if (layer.title == layerTitle) {
        layer.renderer.attributes.shift();
        const fields = [];
        fields.push({ field: "TOTAL_CITIZEN", color: this.nationalsColrs[0].color, label: this.nationalsColrs[0].name });
        const renderer = {
          fields: fields,
        };

        layer.renderer = renderer;
      }
    });
  }

  // all other widgets
  createBookmarkWidget(position: any) {
    const bookmarks = new Bookmarks({
      view: this.view,
     // editingEnabled: true,
      visibleElements: {
        time: false
      }
    });
    const bkExpand = new Expand({
      view: this.view,
      content: bookmarks,
      expanded: false,
      expandTooltip: 'Bookmarks'
    });
    this.view.ui.add(bkExpand, position);
  }

  createMetadataWidget(position: any) {
    var metaToolDiv = document.createElement('div');
    metaToolDiv.className = "selection-tool-div";
    var jobseekersMetaDiv = document.createElement('div');
    jobseekersMetaDiv.innerHTML = '<a href="https://datamart.scad.gov.ae:9443/data/view/id/1026#!tab-data-summary" target="_blank" >Job Seekers</a>';
    metaToolDiv.appendChild(jobseekersMetaDiv);
    var jobVacanciesDiv = document.createElement('div');
    jobVacanciesDiv.innerHTML = '<a href="https://datamart.scad.gov.ae:9443/data/view/id/1027" target="_blank" >Job Vacancies</a>';
    metaToolDiv.appendChild(jobVacanciesDiv);
    const metaExpand = new Expand({
      view: this.view,
      content: metaToolDiv,
      expanded: false,
      expandIconClass: 'bi bi-code-slash fs-5',
     // expandIcon: 'bi bi-code-slash fs-5',
      expandTooltip: 'View metadata list'
    });
    this.view.ui.add(metaExpand, {
      position: position
    });
  }
  createBaseMapGalleryWidget(position: any) {
    const portal = new Portal();
    const source = new PortalBasemapsSource({
      portal,
      query: {
        // id: "bdb9d65e0b5c480c8dcc6916e7f4e099"
        id: "702026e41f6641fb85da88efe79dc166"
      }
    });
    let basemapGallery = new BasemapGallery({
      view: this.view,
      source: source
    });
    const basemapGalleryExpand = new Expand({
      view: this.view,
      content: basemapGallery,
      expanded: false,
      expandTooltip: this._translate.instant('Basemap gallery')
    });
    this.view.ui.add(basemapGalleryExpand, position);
  }
  createZoomWidget(position: any) {
    var zoomToolDiv = document.createElement('div');
    zoomToolDiv.className = "zoom-tool-div-alone";
    var zoomInDiv = document.createElement('div');
    zoomInDiv.innerHTML = `<i class="bi bi-plus" title="${this._translate.instant('Zoom In')}"></i>`;
    zoomInDiv.className = "esri-widget--button esri-widget esri-interactive";
    zoomInDiv.addEventListener('click', (evt) => {
      this.zoomIn();
    });
    zoomToolDiv.appendChild(zoomInDiv);
    var zoomOutDiv = document.createElement('div');
    zoomOutDiv.innerHTML = `<p style="font-size: 20px;font-weight: bolder;" title="${this._translate.instant('Zoom Out')}">-</p>`;
    zoomOutDiv.className = "esri-widget--button esri-widget esri-interactive";
    zoomOutDiv.addEventListener('click', (evt) => {
      this.zoomOut();
    });
    zoomToolDiv.appendChild(zoomOutDiv);
    this.view.ui.add(zoomToolDiv, position);
  }
  zoomIn() {
    this.view.goTo({
      target: this.view.center,
      zoom: this.view.zoom + 1,
      duration: 1000,
      easing: 'ease-in-out'
    });
    // this.mainView.zoom += 1;
  }
  zoomOut() {
    this.view.goTo({
      target: this.view.center,
      zoom: this.view.zoom - 1,
      duration: 1000,
      easing: 'ease-in-out'
    });
  }
  createMeasurementWidget(positionMeasurement: any, positionButtons: any) {
    let activeTool: 'line' | 'area' = 'line';
    let measurementWidget = new Measurement({
      view: this.view
    });
    this.view.ui.add(measurementWidget, positionMeasurement);
    var measureToolDiv = document.createElement('div');
    measureToolDiv.className = "tool-div";
    var lineMeasureDiv = document.createElement('div');
    lineMeasureDiv.innerHTML = '<calcite-icon icon="line" text-label="360 degree view" scale="l" title="Distance measurement"></calcite-icon>';
    lineMeasureDiv.className = "esri-widget--button esri-widget esri-interactive";
    lineMeasureDiv.addEventListener('click', (evt) => {
      deleteMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      polygonMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      lineMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive active';
      polygonMeasureDiv.removeAttribute("id");
      deleteMeasureDiv.removeAttribute('id');
      lineMeasureDiv.setAttribute('id', 'tool-div-measurement-line');
      activeTool = 'line';
      measurementWidget.activeTool = 'distance';
    });
    measureToolDiv.appendChild(lineMeasureDiv);
    var polygonMeasureDiv = document.createElement('div');
    polygonMeasureDiv.innerHTML = '<calcite-icon icon="measure-area" text-label="360 degree view" scale="l" title="Area measurement"></calcite-icon>';
    polygonMeasureDiv.className = "esri-widget--button esri-widget esri-interactive";
    polygonMeasureDiv.addEventListener('click', (evt) => {
      deleteMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      lineMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      polygonMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive active';
      lineMeasureDiv.removeAttribute("id");
      deleteMeasureDiv.removeAttribute("id");
      polygonMeasureDiv.setAttribute('id', 'tool-div-measurement-polygon');
      activeTool = 'area';
      measurementWidget.activeTool = 'area';
    });
    measureToolDiv.appendChild(polygonMeasureDiv);
    var deleteMeasureDiv = document.createElement('div');
    deleteMeasureDiv.innerHTML = '<calcite-icon icon="trash" text-label="360 degree view" scale="l" title="Remove measurement"></calcite-icon>';
    deleteMeasureDiv.className = "esri-widget--button esri-widget esri-interactive";
    deleteMeasureDiv.addEventListener('click', (evt) => {
      deleteMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive active';
      lineMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      polygonMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      lineMeasureDiv.removeAttribute("id");
      polygonMeasureDiv.removeAttribute("id");
      deleteMeasureDiv.setAttribute('id', 'tool-div-measurement-delete');
      measurementWidget.clear();
    });
    measureToolDiv.appendChild(deleteMeasureDiv);
    const measurehExpand = new Expand({
      view: this.view,
      content: measureToolDiv,
      expanded: false,
      expandIconClass: 'bi bi-bezier fs-5',
     // expandIcon: 'bi bi-bezier fs-5',
      expandTooltip: this._translate.instant('Measurements')
    });
    this.view.ui.add(measurehExpand, {
      position: positionButtons
    });
  }
}
