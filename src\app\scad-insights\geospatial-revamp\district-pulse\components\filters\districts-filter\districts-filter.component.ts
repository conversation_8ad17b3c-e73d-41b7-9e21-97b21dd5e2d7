import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, HostListener, ViewChild, ElementRef } from '@angular/core';
import { CommonService } from '../../../../common.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { geoMapKeys } from '../../../../geospatial.contants';


@Component({
  selector: 'ifp-districts-filter',
  standalone: true,
  imports: [
    CommonModule, FormsModule, TranslateModule
  ],
  templateUrl: './districts-filter.component.html',
  styleUrl: './districts-filter.component.scss'
})
export class DistrictsFilterComponent implements OnInit, OnDestroy {

  @Input() menuId!: string;
  @Input() openMenu!: string | null;
  @Output() toggleDropdownMenu = new EventEmitter<string>();
  
  constructor(
    private commonService: CommonService,
    private themeService: ThemeService,
  ) {
    
  }

  ngOnInit() {
  }

  ngOnDestroy() {
  }


  get isMenuOpen(): boolean {
    return this.openMenu === this.menuId; 
  }

  toggleMenu(): void {
    this.toggleDropdownMenu.emit(this.menuId);
  }
}

