import { Component, On<PERSON><PERSON>roy, OnInit, signal, ViewChild, WritableSignal } from '@angular/core';
import { Store } from '@ngrx/store';
import { getInitialData } from 'src/app/scad-insights/store/initialStore/initial.action';
import { SubSink } from 'subsink';
import { slaService } from 'src/app/scad-insights/core/services/sla/sla.service';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';

import { fadeInOut } from 'src/app/scad-insights/animation/fade.animation';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { UserJourneyService } from 'src/app/scad-insights/core/services/userJourney/user-journey.service';
import { ActivityMonitorService } from 'src/app/scad-insights/core/services/activity-monitor.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { CookieService } from 'src/app/scad-insights/core/services/cookie.service';
import { GeospatialNodeService } from 'src/app/scad-insights/core/services/geospatial-node.service';
import { SharedService } from 'src/app/scad-insights/home/<USER>/shared.service';
import { UaePassService } from 'src/app/scad-insights/core/services/uae-pass-service/uae-pass-service.service';
import { NavigationEnd, Router } from '@angular/router';
import { authTypes, localStorageKeys } from 'src/app/scad-insights/core/constants/auth.constants';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { ChatBotApiService } from 'src/app/scad-insights/core/services/chat-bot/chat-bot.service';
import { MapConfigService } from 'src/app/scad-insights/geospatial-revamp/map-config.service';
import { idToken } from 'src/app/scad-insights/core/constants/msalConfig.constants';

@Component({
  selector: 'app-ifp-pages',
  templateUrl: './ifp-pages.component.html',
  styleUrls: ['./ifp-pages.component.scss'],
  animations: [fadeInOut],
  standalone: false
})
export class IfpPagesComponent implements OnInit, OnDestroy {
  @ViewChild('modalcertificate', { static: true }) modalCertificate!: IfpModalComponent;
  @ViewChild('journeyStart') journeyStart!: IfpModalComponent;
  public subs = new SubSink();
  public currentTheme!: string | null;
  public currentFontSize!: string | null;
  public currentCursorStyle!: string | null;
  public current!: string | null;
  public showJourney: boolean = false;
  public surveyResponse!: any;
  public isStart: boolean = true;
  public showJourneyLater: string | null = 'false';
  public isGenAi: boolean = false;
  public isGeospatial: WritableSignal<boolean> = signal(false);

  constructor(
    private readonly store: Store, public _slaService: slaService,
    private readonly geospatialNode: GeospatialNodeService,
    private readonly sharedService: SharedService,
    public downloadService: DownloadService,
    public _journeyService: UserJourneyService,
    private readonly _activityMonitor: ActivityMonitorService,
    private readonly _router: Router,
    private readonly _themeService: ThemeService,
    private readonly _cookie: CookieService,
    private readonly _uaePassService: UaePassService,
    private readonly _msalService: IFPMsalService,
    private readonly _chatBotService: ChatBotApiService,
    private commonForGeospatial: MapConfigService
  ) {
    this.store.dispatch(getInitialData());
    const isTimeOutEnabled = localStorage.getItem('preventLogout');
    if (isTimeOutEnabled == 'true') {
      this._activityMonitor.startWatching(1800);
      // Start watching when user idle is starting.
      this.subs.add(this._activityMonitor.onTimerStart().subscribe());

      // Start watch when time is up.
      this.subs.add(
        this._activityMonitor.onTimeout().subscribe(() => {
          this._msalService.logout();
        })
      );
    }
    // this.showJourneyLater = localStorage.getItem('isJourneyLater') !== 'true' ? 'false' : 'true';
    if (window.matchMedia('(min-width: 1024px)').matches) {
      this.subs.add(
        this._themeService.startJourny$.subscribe((resp: any) => {
          if (resp.status && !resp?.data.status) {
            // this._themeService.isStartJourny=true;
            this.showJourney = this._router.url === '/home' && !resp?.data.skipModel;
            this.isStart = true;
            // resp?.data?.data.splice(1, 1);
            resp?.data?.data?.forEach((element: JourneyData) => {
              this._journeyService.journeyApiData[element.key] = element;
            });
          } else {
            this.isStart = false;
            this._journeyService.removeBlinker.next(false);
          }
        })
      );
    }
    this.checKuRls();
  }

  ngOnInit(): void {
    // this.subs.add(this._journeyService.getUserJourneyData().subscribe((data: Journey)=> {
    //   if (!data.status) {
    //     this._router.url === '/home' ? this.showJourney = true : this.showJourney = false;
    //     data?.data?.forEach((element: JourneyData) => {
    //       this._journeyService.journeyApiData[element.key] =   element;
    //     });
    //   }
    // }));

    if (localStorage.getItem(idToken) && localStorage.getItem(localStorageKeys.loginType) === authTypes.uaePass) {
      this._uaePassService.uaePassToken = localStorage.getItem(idToken) ?? '';
      this._uaePassService.startRefreshTokenTimer();
    }
    this.subs.add(this._slaService.feedback().subscribe((res: any) => {
      if (res.surveyStatus) {
        this.surveyResponse = res?.surveyDetails;
        this.modalCertificate.createElement();
      }
    }));
    this.getAuthenticatedNodeData();
    this.checkAiAuditDownloadAccess();
    const paths = ['advance', 'data-preparation', 'prep-library', 'auto-ml', 'exploratory', 'my-Scenarios', 'scenario-details', 'dashboard-builder', 'dashboards'];
    this.subs.add(this._router.events.subscribe(() => {
      this.isGenAi = paths.some(path => this._router.url.includes(path)) ? false : true;
    }));
  }

  closeJourney(event: string) {
    this.showJourney = false;
    if (event === 'later') {
      this._cookie.setCookie('journeyStatus', 'later');
    }
    // localStorage.setItem('isJourneyLater', 'true');
  }

  closeModal() {
    this.modalCertificate.removeModal();
  }

  getAuthenticatedNodeData() {
    this.subs.add(
      this.geospatialNode.getAllLayerArray().subscribe((layersToBeAuthenticated) => {
        this.subs.add(
          this.sharedService.generateTokenByBackend().subscribe((data: any) => {
            this.commonForGeospatial.setEsriTokenForAuthenticationForApi(data.access_token);
            this.sharedService.setupEsriConfig(layersToBeAuthenticated, data.access_token);
          })
        );
      })
    );
  }

  checkAiAuditDownloadAccess() {
    this.subs.add(
      this._chatBotService.downloadAuditReportAccessCheck().subscribe({
        next: (res: any) => {
          this._slaService.downloadAiAuditAccess.set(res.access);
        },
        error: () => {
          this._slaService.downloadAiAuditAccess.set(false);
        }
      })
    )
  }

  checKuRls() {
    this._router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const currentUrl = event.urlAfterRedirects;
        this.isGeospatial.set(
          currentUrl.startsWith('/geospatial/') ||
          currentUrl.startsWith('/district-pulse')
        ); 
      }
    })

  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }

}




interface JourneyData {
  key: string;
  title: string;
  description: string[];
  image: Image;
}

interface Image {
  url: string;
  alt: string;
}

