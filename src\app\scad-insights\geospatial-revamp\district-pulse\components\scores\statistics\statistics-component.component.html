<div class="ifp-statistics-container">
  <div class="indicator-header">
    <div class="left-icons">
      <em class="ifp-icon ifp-icon-location"></em>
    </div>
    <div class="right-icons">
      <em class="ifp-icon ifp-icon-dockside-left" ></em>
      <span class="ifp-horizontal-icons">
        <em class="ifp-icon ifp-icon-left-arrow" (click)="scrollLeft()"></em>
        <em class="ifp-icon ifp-icon-right-arrow" (click)="scrollRight()"></em>
      </span>
      <em class="ifp-icon ifp-icon-down-arrow-round"></em>
    </div>
  </div>
  <div class="main-indicator-title">
    <span>Statistics</span>
  </div>
  <div class="poi-list" #poiContainer>
    <div 
      class="poi-elm"
      *ngFor="let item of statisticsData; let last = last"
      [class.last-item]="last"
    >
      <img [src]="item.icon" [alt]="item.label + ' icon'">
      <div class="poi-label">{{ item.label }} <br> {{ item.value }} </div>
    </div>
  </div>
</div>