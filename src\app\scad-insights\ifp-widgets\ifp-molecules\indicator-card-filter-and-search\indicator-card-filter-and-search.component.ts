import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IfpSearchComponent } from '../../ifp-atoms/ifp-search/ifp-search.component';
import { IfpDropdownComponent } from '../../ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { domainTabType } from 'src/app/scad-insights/core/constants/domain.constants';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { IfpMatDateRangePickerComponent } from '../ifp-mat-date-range-picker/ifp-mat-date-range-picker.component';

@Component({
  selector: 'ifp-indicator-card-filter-and-search',
  imports: [CommonModule, IfpSearchComponent, IfpDropdownComponent, IfpButtonComponent, IfpTooltipDirective, TranslateModule, IfpMatDateRangePickerComponent],
  templateUrl: './indicator-card-filter-and-search.component.html',
  styleUrls: ['./indicator-card-filter-and-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IndicatorCardFilterAndSearchComponent implements OnInit {
  @Input() domainList: any[] = [];
  @Input() themeList: any[] = [];
  @Input() subThemeName = 'Sub Theme';
  @Input() themeName = 'Theme';
  @Input() enableTheme = true;
  @Input() enableSubtheme = true;
  @Input() enableCompare = false;
  @Input() type: string | undefined = '';
  @Input() selectedTheme: string[] = [];
  @Input() selectedDomain: string[] = [];
  @Input() product: { title: string }[] = [];
  @Input() search = '';
  @Input() clearDisabled = false;
  @Input() isCompare: boolean = false;
  @Input() applyDisabled = false;
  @Input() buttonsApply = true;
  @Input() buttonsClear = true;
  @Input() isInnovative = false;
  @Input() multi = true;
  @Input() compareRight: boolean = true;
  @Input() themeListSingle = '';
  @Input() selectedDomainSingle = '';
  @Input() searchDisable = false;
  @Input() sortValue: any = [];
  @Input() sort: any = [];
  @Input() selectAll: boolean = false;
  @Input() selectAllBox: boolean = false;
  @Input() isSort: boolean = false;
  @Input() boxTypeSearch = false;
  @Input() searchPlaceHolder = 'Search';
  @Input() enableCategory = false;
  @Input() selectedCategory = '';
  @Input() analyticClass: string = '';
  @Input() categoryList = [];
  @Input() domainKey!: string;
  @Input() domainDropdownOptions: any[] = [];
  @Input() selectedDropdownOption: any = null;
  @Input() isReportTab: boolean = false;
  @Output() changeTheme = new EventEmitter();
  @Output() changeDomain = new EventEmitter();
  @Output() applyEvent = new EventEmitter();
  @Output() clearEvent = new EventEmitter();
  @Output() searchData = new EventEmitter();
  @Output() searchChange = new EventEmitter();
  @Output() compareButton = new EventEmitter();
  @Output() sortClicked = new EventEmitter();
  @Output() categoryClicked = new EventEmitter();
  @Output() regularDropdownChange = new EventEmitter();
  @Output() dateRangeChange = new EventEmitter();

  public buttonClass = buttonClass;
  public types = domainTabType;

  // Domain Details Page specific properties
  public isDomainDetailsPage = false;
  public regularDropdownItems: { label: string; value: string }[] = [];
  public selectedRegularOption: any = null;
  public startDateControl = new FormControl(null);
  public endDateControl = new FormControl(null);

  constructor(private router: Router) { }

  ngOnInit() {
    // Check if we're on the domain-details page
    this.isDomainDetailsPage = this.router.url.includes('/domain-exploration/');

    // Use passed dropdown options or fallback to empty array
    this.regularDropdownItems = this.domainDropdownOptions || [];
    this.selectedRegularOption = this.selectedDropdownOption;
  }

  onRegularDropdownChange(event: any) {
    this.selectedRegularOption = event;
    this.regularDropdownChange.emit(event);
  }

  onDateRangeChange(event: { startDate: Date | null; endDate: Date | null }) {
    this.dateRangeChange.emit(event);
  }

  onItemDomain(event: string[]) {
    this.changeDomain.emit(event);
  }

  onItemTheme(event: string[]) {
    this.changeTheme.emit(event);
  }

  apply() {
    this.applyEvent.emit();
  }

  clear() {
    this.clearEvent.emit();
  }

  onSearch(event: string) {
    const searchResult = event.trim();
    this.search = event;
    const searchArray = this.product.filter((prod) => {
      return (prod.title).toLowerCase().includes(searchResult.toLowerCase());
    });
    this.searchChange.emit(searchResult);
    this.searchData.emit(searchArray);
  }

  compare() {
    this.compareButton.emit(true);
  }

  category(event: any) {
    this.categoryClicked.emit(event);
  }


  sortClick(event: any) {
    this.sortClicked.emit(event);
  }

}
