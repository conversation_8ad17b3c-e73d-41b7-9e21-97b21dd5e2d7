import { After<PERSON>iewInit, Component, On<PERSON><PERSON>roy, OnInit, HostListener, Input, Output, ViewChild, ElementRef, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

import WebMap from '@arcgis/core/WebMap.js';
import MapView from '@arcgis/core/views/MapView.js';
import { SubSink } from 'subsink';

import { CensusServiceService } from '../../../census-service.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { BasemapGalleryComponent } from './map-widgets/basemap-gallery/basemap-gallery.component';
import { MapZoomComponent } from './map-widgets/map-zoom/map-zoom.component';
import { MeasurementsComponent } from './map-widgets/measurements/measurements.component';
import { MapSearchComponent } from './map-widgets/map-search/map-search.component';
import { PoiComponent } from './map-widgets/poi/poi.component';
import { CommonService } from 'src/app/scad-insights/geospatial-revamp/common.service';
import Expand from '@arcgis/core/widgets/Expand';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import { geoMapKeys } from '../../../geospatial.contants';
import { TranslateModule } from '@ngx-translate/core';
import StatisticDefinition from '@arcgis/core/rest/support/StatisticDefinition';



@Component({
  selector: 'ifp-census-map',
  standalone: true,
  imports: [
    BasemapGalleryComponent,
    MapZoomComponent,
    MeasurementsComponent,
    MapSearchComponent,
    PoiComponent,
    CommonModule,
    TranslateModule
  ],
  templateUrl: './census-map.component.html',
  styleUrl: './census-map.component.scss'
})
export class CensusMapComponent implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild('selectionLevel') selectionLevel!: ElementRef;
  @ViewChild('mapType') mapType!: ElementRef;
  @ViewChild('PoiComponent') PoiComponent!: ElementRef;
  @Input() filtersData: any = [];
  @Input() isPreviewOpen: boolean = false;
  @Input() isCustomizeOpen: boolean = false;

  @Output() filterChange = new EventEmitter();

  @Output() regionChange = new EventEmitter();
  @Output() regionChangeFromMap = new EventEmitter();

  @Output() districtChange = new EventEmitter();
  @Output() districtChangeFromMap = new EventEmitter();

  @Output() communityChange = new EventEmitter();
  @Output() communityChangeFromMap = new EventEmitter();

  filterObject: any = geoMapKeys.defaultQueryParams;
  subsink: SubSink = new SubSink();

  selectedFeature: any = null;
  view: any;
  poiFeatures: any = [];

  public layerName: string = '';
  public language: string = 'en';
  public isOpen: boolean = false;
  public isMapTypeOpen: boolean = false;
  public selectionLevels: any = geoMapKeys.selectionLevels;
  public currentSelectionLevel: any = this.selectionLevels[0];
  public mapTypes: any = geoMapKeys.mapTypes;
  public currentMapType: any = this.mapTypes[0];
  public mapLocations: any = [];
  public defaultLocation = geoMapKeys.allRegions;
  public selectedDomain: string = geoMapKeys.domains[0].SELECT_EN;
  public chartData: any = [];
  public heatmapLayer!: any;
  public selectionLevelLabel: any = geoMapKeys.selectionLevelLabel;
  public mapTypeLabel: any = geoMapKeys.mapTypeLabel;
  public userAccessPermission: any = {};

  constructor(
    private gisSharedService: CensusServiceService,
    public themeService: ThemeService,
    private commonService: CommonService,
    private downloadService: DownloadService,
  ) { }

  ngOnInit(): void {
    this.subsink.add(
      this.themeService.defaultLang$.subscribe((lang: string) => {
        this.language = lang;
      }),
    );
    this.commonService._isMapUpdated$.subscribe((_isMapUpdated: any) => {
      this.mapLocations = _isMapUpdated;
    });
    this.gisSharedService._geoMapAccessPermission$.subscribe((userAccessPermission: any) => {
      this.userAccessPermission = userAccessPermission;

      // remove the community from selections level if user dont have the permission
      this.selectionLevels = this.selectionLevels.filter((level: any) => {
        if (level.SELECT_CODE === 3) {
          return this.userAccessPermission.communityAccess === true;
        }
        return true;
      });
    });
  }

  ngAfterViewInit(): void {
    this.subsink.add(
      this.gisSharedService._isAuthenticated$.subscribe((auth: boolean) => {
        if (auth) {
          setTimeout(() => {
            this.initMap();
          }, 1000);
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
  }

  initMap(): void {
    const webMap = new WebMap({
      portalItem: {
        // id: geoMapKeys.mapKey,
        id: this.userAccessPermission.communityAccess ? geoMapKeys.mapKey : geoMapKeys.mapKeyRestricted,
      }
    });
    this.view = new MapView({
      map: webMap,
      container: 'viewDivCensus',
      // center: [54.210943, 24.226687],
      // @ts-expect-error
      animate: true,
      zoom: 8,
      ui: {
        components: ['']
      }
    });

    this.gisSharedService.setView(this.view);

    webMap.when((webmap: any) => {

       // Wait for all layers to load
      const layerPromises = webmap.layers.map((layer: any) => layer.when());

      Promise.all(layerPromises).then(() => {
        this.gisSharedService.setIsMapLoadingCompleted(true);
      });

      // Check for tables
      if (webmap.tables && webmap.tables.length > 0) {
        webmap.tables.forEach((table: __esri.Layer) => {
          if (table.title === 'POI') {
            this.processTable(table);
          }
        });
      } else {
        console.log('No tables found in the web map');
      }

      // Create main container
      const customLayerDiv = document.createElement('div');
      customLayerDiv.id = 'customLayersContainer';
      customLayerDiv.className = 'layer-switcher';

      // Create two separate sections for different layer groups
      const adminSection = document.createElement('div');
      adminSection.className = 'layer-group';
      const adminTitle = document.createElement('h3');
      adminTitle.textContent = this.language == 'en' ? 'Administrative Boundaries' : 'الحدود الإدارية';
      adminSection.appendChild(adminTitle);

      const visualSection = document.createElement('div');
      visualSection.className = 'layer-group';
      const visualTitle = document.createElement('h3');
      visualTitle.textContent = this.language == 'en' ? 'Population Distribution' : 'توزيع السكان';
      visualSection.appendChild(visualTitle);

      // Get all layers
      const layers = webmap.layers.toArray();

      // Process regular layers (admin boundaries)
      layers.forEach((layer: any) => {
        if (layer.type !== 'group' && layer.title !== 'DOT_DENSITY') {
          const layerItem = this.createRadioButton(
            layer,
            'adminRadio',
            () => {
              // Only change visibility of the clicked admin layer
              layer.visible = !layer.visible;
              // Hide other admin layers
              layers.forEach((otherLayer: any) => {
                if (otherLayer !== layer && !otherLayer.title.includes('DotDensity') && !otherLayer.title.includes('Heatmap')) {
                  otherLayer.visible = false;
                }
              });
            },
            this.language
          );
          adminSection.appendChild(layerItem);
        }
      });


      // Handle Heatmap layers
      this.heatmapLayer = layers.find((layer: any) => layer.title === geoMapKeys.mapHeatDefaultRealData);
      // Create or show heatmap layer
      const heatmapVis = this.view.map.findLayerById(geoMapKeys.mapHeatDefaultRealData);
      if (!heatmapVis) {
        //const queriedHeatMapLayer = this.createHeatmapLayer(this.heatmapLayer.parsedUrl.path, true);
        this.view.map.add(this.heatmapLayer);
      } else {
        heatmapVis.visible = true;
      }


      // Handle DOT_DENSITY and heatmap layers
      const dotDensityLayer = layers.find((layer: any) => layer.title === 'DOT_DENSITY');
      if (dotDensityLayer) {
        const visualLayersConfig = [
          {
            id: 'population-dots',
            title: this.language == 'en' ? 'Dot Density' : 'كثافة النقاط',
            handler: () => {
              // First hide heatmap if it exists
              const existingHeatmap = this.view.map.findLayerById('population-heatmap');
              if (existingHeatmap) {
                existingHeatmap.visible = false;
              }

              // Create or show dot density layer
              const dotDensityVis = this.view.map.findLayerById('population-dotdensity');
              if (!dotDensityVis) {
                this.gisSharedService.createDotdensityFeatureLayerSeaRemoved(
                  dotDensityLayer.parsedUrl.path,
                  this.view,
                  'population',
                  'Population Distribution',
                  true
                );
              } else {
                dotDensityVis.visible = true;
              }
            }
          },
          {
            id: 'heatmap',
            title: this.language == 'en' ? 'Heatmap' : 'خريطة حرارية',
            handler: () => {
              // First hide dot density if it exists
              const existingDotDensity = this.view.map.findLayerById('population-dotdensity');
              if (existingDotDensity) {
                existingDotDensity.visible = false;
              }

              // Create or show heatmap layer
              const heatmapVis = this.view.map.findLayerById('population-heatmap');
              if (!heatmapVis) {
                this.createHeatmapLayer(dotDensityLayer.parsedUrl.path, true);
              } else {
                heatmapVis.visible = true;
              }
            }
          }
        ];

        visualLayersConfig.forEach(config => {
          const visualItem = this.createRadioButton(
            { id: config.id, title: config.title, visible: false },
            'visualRadio',
            config.handler,
            this.language
          );
          visualSection.appendChild(visualItem);
        });
      }

      // Add sections to main container
      customLayerDiv.appendChild(adminSection);
      customLayerDiv.appendChild(visualSection);

      // Add styling
      const style = document.createElement('style');
      style.textContent = `
        .layer-switcher {
          padding: 10px;
        }
        .layer-group {
          margin-bottom: 20px;
        }
        .layer-group h3 {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: bold;
          color: #323232;
        }
        .layer-item {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
        }
        .layer-item input[type="radio"] {
          margin-right: 8px;
        }
        .layer-item label {
          cursor: pointer;
        }
      `;
      document.head.appendChild(style);

      // Create and add the Expand widget
      const layerSwitcher = new Expand({
        view: this.view,
        content: customLayerDiv,
        expandIconClass: 'esri-icon-feature-layer',
        group: 'top-right',
        expandTooltip: this.language == 'en' ? 'Layer Controls' : 'التحكم في الطبقات',
        collapseTooltip: this.language == 'en' ? 'Collapse' : 'تصغير'
      } as __esri.ExpandProperties);

      // this.view.ui.add(layerSwitcher, 'top-right');

      // Initialize layers with outFields
      webmap.layers.items.forEach((layer: any) => {
        layer.outFields = ['*'];
      });
    });


    this.view.watch('zoom', (newZoom: number) => {
      this.updateLayerVisibility(newZoom);
    });


    this.view.on('click', (event: any) => {

      this.view.hitTest(event).then((response: any) => {
        if (response.results.length) {
          response.results.map((result: any) => {
            const graphic = result.graphic;
            const layer = graphic.layer;
            const layerName = layer.title;
            if (this.selectedFeature && this.selectedFeature === graphic) {
              this.clearSelectedFeature();
            } else {

              if (layerName == geoMapKeys.mapCommunitiesTitle) {
                const attr = graphic.attributes;

                this.highlightFeature(graphic);
                this.selectedFeature = graphic;

                let region_code = Number(attr.scad_r_id);

                this.filterObject.COMMUNITY_CODE = [attr.scad_sc_id];
                this.filterObject.REGION_CODE = [region_code];
                this.filterObject.DISTRICT_CODE = [attr.scad_sd_id];

                const obj = [{
                  'REGION_CODE': region_code,
                  'DISTRICT_CODE': attr.scad_sd_id,
                  'COMMUNITY_CODE': attr.scad_sc_id,
                  'COMMUNITY_AR': attr.sc_name_en,
                  'COMMUNITY_EN': attr.sc_name_ar
                }];
                this.communityChangeFromMap.emit(obj);
                this.filterChange.emit(this.filterObject);

                this.mapLocations = [attr.sc_name_en];

              }

              else if (layerName == geoMapKeys.mapDistrictsTitle) {
                this.highlightFeature(graphic);
                this.selectedFeature = graphic;
                const attr = graphic.attributes;

                this.filterObject.REGION_CODE = [attr.region_id]
                this.filterObject.DISTRICT_CODE = [attr.district_code];
                // remove communities codes
                this.filterObject.COMMUNITY_CODE = [];

                const obj = [{
                  'REGION_CODE': attr.region_id,
                  'DISTRICT_CODE': attr.district_code,
                  'DISTRICT_AR': attr.district_a,
                  'DISTRICT_EN': attr.district_e
                }];
                this.districtChangeFromMap.emit(obj);
                this.filterChange.emit(this.filterObject);

                this.mapLocations = [attr.district_e];

              }

              else if (layerName == geoMapKeys.mapRegionsTitle) {
                this.highlightFeature(graphic);
                this.selectedFeature = graphic;
                const attr = graphic.attributes;

                this.filterObject.REGION_CODE = [attr.region_id];
                // remove districts and communities codes
                this.filterObject.DISTRICT_CODE = [];
                this.filterObject.COMMUNITY_CODE = [];

                const obj = [{
                  'REGION_CODE': attr.region_id,
                  'REGION_AR': attr.r_name_ar,
                  'REGION_EN': attr.r_name_en
                }];

                this.regionChangeFromMap.emit(obj);
                this.filterChange.emit(this.filterObject);

                this.mapLocations = [attr.r_name_en];
              }
            }
          });
        } else {
          this.clearSelectedFeature();
        }
      });
    });
  }

  highlightFeature(graphic: any) {
    const highlightSymbol = {
      type: 'simple-fill',
      color: 'rgba(127, 140, 170, 0.06)',
      outline: {
        color: 'rgb(86, 90, 99)',
        width: 2.5
      }
    };
    graphic.symbol = highlightSymbol;

    this.view.graphics.removeAll();
    this.view.graphics.add(graphic);
    //this.view.goTo(graphic.geometry.extent.expand(1));
  }


  clearSelectedFeature() {
    if (this.selectedFeature) {
      this.selectedFeature.symbol = {
        type: 'simple-fill',
        color: 'rgba(0, 0, 0, 0)',
        outline: {
          color: 'rgba(58, 71, 81, 1)',
          width: 0.53
        }
      };
      this.selectedFeature = null;
    }
  }

  // Function to process the table
  processTable(table: __esri.Layer) {
    if (table.type === 'feature') {
      const featureTable = table as __esri.FeatureLayer;
      featureTable.queryFeatures().then((result) => {
        this.poiFeatures = result.features
          .filter(feature => feature.attributes.category == 'POI')
          .sort((a, b) => a.attributes.parameter.localeCompare(b.attributes.parameter))
          .map(feature => ({
            name: feature.attributes.desc_en,
            name_ar: feature.attributes.desc_ar,
            url: feature.attributes.value,
            icon: feature.attributes.icon
          }));
        this.gisSharedService.setCensusPoiData(this.poiFeatures);

      });
    }
  }


  filterChanged(filter: any) {
    this.filterChange.emit(filter);
  }

  locationChanged(location: string) {
    this.mapLocations = [location];
  }

  districtChanged(districts: any) {
    this.districtChange.emit(districts);
  }

  private async createHeatmapLayer(url: string, show: boolean) {
    const heatmapRenderer = {
      type: "heatmap",
      field: "population",
      colorStops: [
        { color: "rgba(255, 255, 255, 0)", ratio: 0 },
        { color: "rgba(0, 180, 240, 0.50)", ratio: 0.4 },
        { color: "rgba(0, 51, 255, 0.40)", ratio: 0.5 },
        { color: "rgba(255, 0, 0, 0.50)", ratio: 0.6 },
        { color: "rgba(255, 0, 0, 0.40)", ratio: 0.85 },
        { color: "rgba(255, 140, 0, 0.50)", ratio: 0.9 },
        { color: "rgba(245, 225, 0, 0.40)", ratio: 0.95 }
      ],
      maxPixelIntensity: 50,
      minPixelIntensity: 50,
      radius: 20
    };

    const layer = new FeatureLayer({
      id: geoMapKeys.mapHeatDefaultRealData,
      url,
      title: this.language === "en" ? "Population Heatmap" : "خريطة السكان الحرارية",
      visible: show,
      renderer: heatmapRenderer as any,
      popupEnabled: false
    });

    // const sumPopulation = new StatisticDefinition ({
    //   onStatisticField: "population",
    //   outStatisticFieldName: "TotalPop",
    //   statisticType: "sum"
    // });

    const query = layer.createQuery();
    query.where = "1=1";
    query.returnGeometry = true;
    query.outFields = ["*"];
    // query.outStatistics = [sumPopulation];
    // query.groupByFieldsForStatistics = ["scad_sc_id"];

    layer.queryFeatures(query).then((result) => {
      result.features.forEach((f, i) => {
        const { scad_sc_id, TotalPop } = f.attributes;
        //console.log(`Feature #${i}, scad_sc_id=${scad_sc_id}, TotalPop=${TotalPop}`);
      });
    })
      .catch((error) => {
        console.error("Query error:", error);
      });

    return layer;
  }



  private createRadioButton(layer: any, groupName: string, onChange: () => void, language: string) {
    const button = document.createElement('input');
    button.type = 'radio';
    button.name = groupName;
    button.id = layer.id;
    button.checked = layer.visible;

    button.addEventListener('change', () => {
      if (button.checked) {
        onChange();
      }
    });

    const label = document.createElement('label');
    label.setAttribute('for', layer.id);
    label.innerHTML = layer.title || layer.id;

    const layerItem = document.createElement('div');
    layerItem.className = 'layer-item';
    layerItem.appendChild(button);
    layerItem.appendChild(label);

    return layerItem;
  }


  getDownloadData() {
    this.commonService._chartData$.subscribe((chartData: any) => {
      this.chartData = chartData;
    });
  }

  download(type: string) {
    this.getDownloadData();
    if (this.chartData.length > 0) {
      if (type == 'XL') {
        const downloadData: any = [];
        downloadData.push(...this.chartData);
        this.downloadService.exportToExcel(
          downloadData,
          'Geo Spatial Data'
        );
      }
    }
  }

  toggleDropdown() {
    this.isOpen = !this.isOpen;
  }

  toggleMapTypeDropdown() {
    this.isMapTypeOpen = !this.isMapTypeOpen;
  }

  toggleSelection(level: any): void {
    this.currentSelectionLevel = level;
    if (level.SELECT_EN == geoMapKeys.district) {
      this.gisSharedService.layerSelection(geoMapKeys.mapDistrictsTitle);
    } else if (level.SELECT_EN == geoMapKeys.region) {
      this.gisSharedService.layerSelection(geoMapKeys.mapRegionsTitle);
    } else if (level.SELECT_EN == geoMapKeys.community) {
      this.gisSharedService.layerSelection(geoMapKeys.mapCommunitiesTitle);
    }
    this.isOpen = !this.isOpen;
  }

  toggleMapType(type: any) {
    // Hide all layers defined in mapTypes array
    console.log("type", type)
    this.mapTypes.forEach((mapType: any) => {
      const layer = this.view.map.layers.find((layer: any) => layer.title === mapType.LAYER_NAME);
      if (layer) {
        layer.visible = false;
      }
    });
    // Show only the selected layer
    const selectedLayer = this.view.map.layers.find((layer: any) => layer.title === type.LAYER_NAME);
    if (selectedLayer) {
      selectedLayer.visible = true;
    }
    this.isMapTypeOpen = !this.isMapTypeOpen;
    this.currentMapType = type;
  }


  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event): void {
    if (!this.selectionLevel?.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
    if (!this.mapType?.nativeElement.contains(event.target)) {
      this.isMapTypeOpen = false;
    }
  }

  private updateLayerVisibility(zoomLevel: number): void {
    if (zoomLevel <= 9) {
      this.currentSelectionLevel = this.selectionLevels[0];
    } else if (zoomLevel > 9 && zoomLevel <= 11) {
      this.currentSelectionLevel = this.selectionLevels[1];
    } else if (zoomLevel > 11) {
      this.currentSelectionLevel = this.selectionLevels[2];
    }
  }

  resetSelectionLevel(event: any) {
    //this.gisSharedService.layerSelection(geoMapKeys.mapRegionsTitle);
    this.gisSharedService.MapToInitialLevel("fromIcon");
    this.currentSelectionLevel = this.selectionLevels[0];
  }


  updateSelectionLevel(level: string) {
    //this.currentSelectionLevel = level;
    //console.log("this.currentSelectionLevel", this.currentSelectionLevel)
  }



}

