
import { role } from './../../user-onboarding/control-panel/ifp-access-control/ifp-access-control.constants';
import { CommonModule, Location, DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, HostListener, OnInit, Renderer2, ViewChild, ChangeDetectorRef, Inject, OnDestroy, AfterViewInit, NgZone, WritableSignal, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, debounceTime } from 'rxjs';
import { buttonClass, buttonColor } from 'src/app/scad-insights/core/constants/button.constants';
import { NavItems, NotificationList, PlaceHolderValues } from 'src/app/scad-insights/core/interface/header.interface';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { getHeader, getUserPermission, setQuery } from 'src/app/scad-insights/store/header/header.action';
import { selectHeaderResponse, selectPermissionList, selectSearchResponse } from 'src/app/scad-insights/store/header/header.selector';
import { cloneDeep, groupBy } from 'lodash';
import { SearchSuggestionDirective } from 'src/app/scad-insights/core/directives/sugession.directive';
import { ProgressLoaderComponent } from '../progress-loader/progress-loader.component';
import { LoaderService } from 'src/app/scad-insights/core/services/loader.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { selectAllNotificationResponse, selectNotificationResponse } from 'src/app/scad-insights/store/notification/notification.selector';
import { getNotification, getNotificationAll } from 'src/app/scad-insights/store/notification/notification.action';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { IfpImgComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-img/ifp-img.component';
import { NotificationService } from 'src/app/scad-insights/core/services/notification/notification.service';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { DomainsComponent } from '../../domains/domains/domains.component';
import { HeaderService } from 'src/app/scad-insights/core/services/header/header.service';
import { FormsModule } from '@angular/forms';
import { OutsideClickDirective } from 'src/app/scad-insights/core/directives/outsideClick.directive';
import { SubSink } from 'subsink';
import { FormatterService } from 'src/app/scad-insights/core/services/formater/formatter.service';
import { IfpSearchComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { appType } from 'src/app/scad-insights/core/constants/contentType.constants';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { headerSlide, slideDownAnimations } from 'src/app/scad-insights/animation/slideDown.animation';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';
import { IfpUserJourneyDirective } from 'src/app/scad-insights/core/directives/ifp-user-journey.directive';
import { environment } from 'src/environments/environment';
import { UserJourneyService } from 'src/app/scad-insights/core/services/userJourney/user-journey.service';
import { journeyData } from 'src/app/scad-insights/core/constants/journey/journey.constants';
import { selectCategoryResponse } from '../../domains/store/domain.selector';
import { DownloadService } from '../../core/services/download.service';
import { loadCensusData } from '../../store/pages/page.action';
import { selectCensusResponse } from '../../store/pages/page.selector';
import { HttpClient } from '@angular/common/http';
import { IfpModalComponent } from '../../ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { features, powerBIIds, staticKeys } from '../../core/constants/header.constants';
import { AdminService } from '../../core/services/sla/admin.service';
import { dgRoles, roleList } from '../../user-onboarding/control-panel/ifp-access-control/ifp-access-control.constants';
import { UsageDashboardStatusService } from '../../core/services/usage-dashboard-log/usage-dashboard-status.service';
import { ChatBotApiService } from '../../core/services/chat-bot/chat-bot.service';
import { DownLoadService } from '../../core/services/download-service/download.service';
import { ToasterService } from '../../core/services/tooster/ToastrService.service';
import { slaService } from '../../core/services/sla/sla.service';
import { PermissionList } from '../../store/header/header.state';



@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    slideDownAnimations,
    headerSlide
  ],
  imports: [
    CommonModule,
    RouterLink,
    RouterLinkActive,
    TranslateModule,
    IfpButtonComponent,
    NgbNavModule,
    SearchSuggestionDirective,
    ProgressLoaderComponent,
    IfpTooltipDirective,
    IfpImgComponent,
    IfpNoDataComponent,
    DomainsComponent,
    FormsModule,
    OutsideClickDirective,
    IfpSearchComponent,
    IfpUserJourneyDirective
  ]
})


export class HeaderComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild('header') header!: ElementRef;
  @ViewChild(SearchSuggestionDirective) suggestionDirective!: SearchSuggestionDirective;
  @ViewChild('notificationDropdown') notificationDropdown!: ElementRef;
  @ViewChild('tncModal') tncModal!: IfpModalComponent;


  public isMenuExpanded: boolean = false;
  public toggleDropdown: boolean = false;
  public isSticky: boolean = false;
  public selectedLanguage: string = 'en';
  public buttonClass = buttonClass;
  public buttonColor = buttonColor;
  public analyticalClass = analyticsClasses;
  public selectedNav: string = '/home';
  public searchPlaceHoldes: PlaceHolderValues[] = [];
  public tooltip!: HTMLElement | null | any;
  searchResponse$ = this.store.select(selectSearchResponse);
  headerData$ = this.store.select(selectHeaderResponse);
  notificationData$ = this.store.select(selectNotificationResponse);
  searchInput = new Subject<string>();
  public domains: any = [];
  suggestionList: any = [];
  public contentTypes: any = [];
  public hideSearchBar: boolean = false;
  public isDomain: boolean = false;
  selectedTheme: string | null = 'light';
  selectedFontSize: string | null = 'md';
  selectedCursor: string | null = 'type1';
  public height: number = 260;
  public isNavItemActive: number = 0;
  public isMobile: boolean = false;
  public selectedItem: string = '';
  public enableBack!: boolean;
  public isDemo: boolean = false;
  public headerFixedClass = 'ifp-header__fixed';
  public journeyStep!: number;
  public previousUrl!: string;
  public placeHolderText!: string;
  public placeHolderImage!: string;
  public placeholderState: string = '';
  private intervalId: any;
  public isHidePlaceHolder: boolean = false;
  public navItems: NavItems[] = [
    {
      title: 'Home Page',
      route: 'home'
    },
    {
      title: 'My Bookmarks',
      route: 'my-apps'
    },
    {
      title: 'Domain',
      route: 'domains',
      classList: buttonClass.iconRight
    }
  ];

  public notificationList: NotificationList[] = [];
  public colors = ifpColors;
  public searchText: any = '';
  public selectedTabIndex: number = -1;
  public subs = new SubSink();
  public scrollPos: number = 0;
  public searchQuery!: string;
  public totalNotificationCount: number = 0;
  // public unReadNotificationCount: number = this.headerService.unreadNotificationCount();
  public defaultSettings: any = {
    settings: []
  };

  public journeyData = journeyData;
  public animationEvent = false;
  public showSearchBar: boolean = false;
  public resultLength!: number;
  public fontSizeRange: number = 2;
  public cursorRange: number = 1;
  public hamburgerMenu: boolean = false;
  public isHome: boolean = false;
  public isAdmin = this._adminService.hasControlpanelAccess;
  public userRole = this._adminService.userRole;
  public roleList = roleList;
  public dgRoles = dgRoles;
  public userDesignation: string = roleList[this.userRole];
  public isSvAccessible: boolean = false;
  public isGovernance: boolean = false;
  public auditReportAccess: WritableSignal<boolean> = signal(false);
  public checkInsightAccess = signal(false);
  public permissionList: PermissionList[] = []
  public tammDashboard: any;
  public isPowerBiAccess: boolean = false;
  public taamDashboardAccess: boolean = false;

  @ViewChild('toggleButton') toggleButton!: ElementRef;
  @ViewChild('menu') menu!: ElementRef;
  @ViewChild('megamenu') megamenu!: ElementRef;
  @ViewChild('government') government!: ElementRef;
  @ViewChild('hamburger') hamburger!: ElementRef;
  @ViewChild('dropdownMobile') dropdownMobile!: Node;
  @ViewChild('searchBar') searchBar!: ElementRef;
  @ViewChild('fontSizeInput') fontSizeInput!: ElementRef;
  @ViewChild('curserSizeInput') curserSizeInput!: ElementRef;
  @HostListener('window:resize', ['$event'])
  onResize() {
    if (window.matchMedia('(min-width: 1025px)').matches) {
      // this.isMenuExpanded = true;
      this.isMobile = false;
    } else {
      // this.isMenuExpanded = false;
      this.isMobile = true;
    }
  }

  @HostListener('window:load', ['$event'])
  onLoad() {
    if (window.matchMedia('(max-width: 1024px)').matches) {
      // this.isMenuExpanded = false;
      this.isMobile = true;
    }
  }

  @HostListener('window:scroll', ['$event'])
  onScroll() {
    this.scrollPos = window.scrollY;
    if (this.header) {
      if (window.matchMedia('(min-width: 1023.98px)').matches) {
        this.isMenuExpanded = false;
        this.hamburgerMenu = false;
        const body = document.getElementsByTagName('body')[0];
        const currentScroll = window.scrollY;
        if ((currentScroll > this.height + 50) && (body.offsetHeight > 500)) {
          this.renderer.addClass(document.body, this.headerFixedClass);
          this.isSticky = true;
        } else {
          this.renderer.removeClass(document.body, this.headerFixedClass);
          this.isSticky = false;
          this.showSearchBar = false;
        }
        this._cdr.detectChanges();
      }
    }
  }

  animationHeader(event: any) {
    if (event.toState == 'false') {
      this.animationEvent = false;
    } else {
      this.animationEvent = true;
    }
  }



  constructor(public _translate: TranslateService, private store: Store, private renderer: Renderer2, public loaderService: LoaderService, public themeService: ThemeService,
    public router: Router, public _msal: IFPMsalService, private _cdr: ChangeDetectorRef, private notificationService: NotificationService, public headerService: HeaderService,
    public location: Location, private route: ActivatedRoute, @Inject(DOCUMENT) private document: Document, private _formater: FormatterService, public _commonService: CommonApiService,
    private _journeyService: UserJourneyService, private ngZone: NgZone, private _downloadService: DownloadService,
    private http: HttpClient, private _adminService: AdminService, public _usageDashboard: UsageDashboardStatusService, private _apiservice: ChatBotApiService,
    private _internalDownloadService: DownLoadService, private _toaster: ToasterService, private _sla: slaService) {

    this.selectedNav = this.router.url;
    if (environment.env === 'demo') {
      this.isDemo = true;
      this.headerFixedClass = 'ifp-header__fixed-demo';
      this.renderer.addClass(this.document.body, 'ifp-demo');
    }
    this.subs.add(
      this.headerService.hideDomain$.subscribe(resp => {
        if (resp) {
          this.isDomain = false;
          this.selectedNav = '';
        }
      }));
    this.subs.add(
      this.headerService.clearSearch$.subscribe(resp => {
        if (resp) {
          this.searchText = '';
          const data = {
            query: ''
          };
          this.store.dispatch(setQuery({ query: data }));
        }
      }));
    this._translate.use('en');
    this.subs.add(this.searchInput
      .pipe(debounceTime(800))
      .subscribe((value) => {
        const data = {
          query: value
        };
        this.headerService.searchApiLoading$.next(true);
        this.store.dispatch(setQuery({ query: data }));
        if (this.router.url == '/search') {
          this.updateQueryParams(value);
        }
      }));

    this.renderer.listen('window', 'click', (e: Event) => {
      if (this.toggleButton && this.menu && e.target !== this.toggleButton.nativeElement && e.target !== this.menu.nativeElement) {
        this.suggestionList = [];
        this.toggleDropdown = false;
      }
    });

    this.subs.add(this.route.queryParams.subscribe(params => {
      if (params['searchData'] && this.router.url.split('?')[0] == '/search') {
        this.searchText = params['searchData'];
        this.searchInput.next(this.searchText);
      }
    }));
    this.getSearchData();
    this.subs.add(this.router.events.subscribe(() => {
      this.domains = false;
      this.isMenuExpanded = false;
      this.hamburgerMenu = false;
      this.renderer.removeClass(this.document.body, 'ifp-modal-open');
      this.isSticky = false;
      this.showSearchBar = false;
      this.renderer.removeClass(document.body, 'ifp-header__fixed');
      this.isHome = this.router.url === '/home';
      if (!this.isHome) {
        // this._msal.showTour = false;
        this.renderer.addClass(document.body, 'ifp-header__inner');
        this._journeyService.removeBlinker.next(true);
      } else {
        this.renderer.removeClass(document.body, 'ifp-header__inner');
      }
    }));

    this.checkAuditReportApproval();

  }



  ngAfterViewInit(): void {
    // this._shepherdService.start();
    this.selectedTheme = this.themeService.defaultTheme;
    this.selectedFontSize = this.themeService.defaultFontSize;
    this.selectedCursor = this.themeService.defaultCursorStyle;
    this.fontSizeRange = this.setRangeValue(this.selectedFontSize ? this.selectedFontSize : 'md', this.fontSizeInput);
    this.cursorRange = this.setRangeValue(this.selectedCursor ? this.selectedCursor : 'type1', this.curserSizeInput);
    this.themeService.changeCursorStyle(this.selectedCursor);
    this.themeService.changeFontSize(this.selectedFontSize);
    this.themeService.changeTheme(this.selectedTheme);
    this.defaultSettings = {
      settings: [
        {
          name: 'fontSize',
          value: this.selectedFontSize ? this.selectedFontSize : 'md'
        },
        {
          name: 'cursor',
          value: this.selectedCursor ? this.selectedCursor : 'type1'
        },
        {
          name: 'theme',
          value: this.selectedTheme ? this.selectedTheme : 'light'
        },
        {
          name: 'lang',
          value: this.selectedLanguage ? this.selectedLanguage : 'en'
        }
      ]
    };
    this.changePlaceHolder();
    this.assignLogos();
  }

  assignLogos() {
    this.headerData$.subscribe(resp => {
      this._downloadService.insightsLogo = resp.site_logo_light;
      this._downloadService.statisticCenterLogo = resp.site_slogan_light;
    });
  }


  changePlaceHolder() {
    this.searchPlaceHoldes = [];
    this.subs.add(this.store.select(selectCategoryResponse).subscribe((data: any) => {
      if (data?.length > 0) {
        data.forEach(async (element: { name: string; light_icon: string; dark_icon: string; }) => {
          const navData: PlaceHolderValues = {
            name: element.name,
            light_icon: await this.convertToBase64(element.light_icon),
            dark_icon: await this.convertToBase64(element.dark_icon)
          };
          this.searchPlaceHoldes.push(navData);
        });
      }
    }));
    let count = 0;
    this.intervalId = setInterval(() => {
      this.ngZone.run(() => {
        this.placeHolderText = this.searchPlaceHoldes[count]?.name;
        this.placeHolderImage = this.searchPlaceHoldes[count]?.light_icon;
        count = (count + 1) >= this.searchPlaceHoldes.length ? 0 : count + 1;
        this._cdr.detectChanges();
      });
    }, 2000);
  }




  updateQueryParams(searchData: string) {
    const currentUrl = this.location.path();
    const urlTree = this.router.parseUrl(currentUrl);
    const queryParams = { searchData: searchData };
    urlTree.queryParams = queryParams;
    this.location.replaceState(urlTree.toString());
  }



  getSearchData() {
    this.subs.add(this.store.select((selectSearchResponse)).subscribe({
      next: resp => {
        const data = resp.result.contentTypes;
        this.contentTypes = resp.result.contentTypes;
        this.searchQuery = resp?.query;
        this.resultLength = resp?.numberOfResults;
        this.getSugessions(data);
        this.domains = [];
        const totalData: any = [];
        if (data?.length > 0) {
          for (const item of data) {
            if (item.items.length > 0) {
              totalData.push(...item.items);
            }
          }
        }
        if (totalData && totalData.length > 0) {
          this.domains = groupBy(totalData, item => item.topic.name);
        }
      }
    }));
  }

  ngOnInit(): void {
    this.isPowerBiAccess = false;
    this.taamDashboardAccess = false;
    this.subs.add(this._usageDashboard.usageDashboardStatusCheck(false).subscribe());
    this.userDesignation = (dgRoles.includes(this._adminService.userRole) || this.userRole === role.normalUser) ? this._adminService.userDesignation : this.roleList[this._adminService.userRole];
    this.store.dispatch(getHeader());
    this.store.dispatch(getUserPermission());
    if (window.matchMedia('(max-width: 1023px)').matches) {
      this.isMobile = true;
    }
    this.subs.add(
      this.router.events.subscribe(() => {
        this.enableBack = history?.state?.navigationId === 1 ? false : true;
      }),
      this.themeService.defaultLang$.subscribe((lang) => {
        this._translate.use(lang);
        this.selectedLanguage = lang;
      })
    );
    this.store.dispatch(getNotificationAll({ page: 1, status: '' }));
    this.store.select(selectAllNotificationResponse).subscribe(resp => {
      this.totalNotificationCount = resp.totalCount;
      this.headerService.unreadNotificationCount.set(resp.unreadCount ?? 0);
    });
    this.store.dispatch(loadCensusData());
    this.store.select(selectCensusResponse).subscribe(resp => {
      if (resp?.resp) {
        this._commonService.censusData = resp?.resp;
      }
    });

    // to be removed
    const userData = localStorage.getItem('userData');
    if (userData) {
      const groupIds = JSON.parse(userData).account.idTokenClaims.groups;
      this.isSvAccessible = this.headerService.approverGroupIds.some((id: string) => groupIds.includes(id));
    }
    if (environment.env === 'staging' || environment.env === 'dev') {
      this.checkInsight();
    }
    const groupIds = userData ? JSON.parse(userData).idTokenClaims.groups : [];
    this.isPowerBiAccess = powerBIIds.some((id: string) => groupIds.includes(id));
    this.taamDashboardAccess = groupIds.find((x: string) => x == this.headerService.tammDashboardId);
    console.log("this.taamDashboardAccess", this.taamDashboardAccess)
  }

  checkInsight() {
    this.checkInsightAccess.set(true);
    this.subs.add(this._sla.checkInsightAccess().subscribe({
      next: (data: {
        access: boolean
      }) => {
        this.checkInsightAccess.set(data.access);
      },
      error: ((err: {
        error: {
          access: boolean
        }
      }) => {
        this.checkInsightAccess.set(err.error.access);
      })
    }))
  }

  getHeaderData() {
    this.store.dispatch(getHeader());
    this._cdr.detectChanges();
    this.height = this.header?.nativeElement.clientHeight;
  }

  changeTheme(theme: string) {
    // this.selectedTheme = theme;
    // this.themeService.changeTheme(theme);
    this.defaultSettings.settings.forEach((element: any) => {
      if (element.name === 'theme') {
        element.value = theme;
      }
    });

    this.subs.add(
      this._commonService.updateUserSettings(this.defaultSettings).subscribe((res: any) => {
        if (res) {
          this.selectedTheme = theme;
          this.themeService.changeTheme(theme);
        }
      })
    );
  }

  setFontSize(size: string, inputType?: string) {
    this.fontSizeRange = +size;
    let fontSize = size;
    if (size == '1') {
      fontSize = 'sm';
    } else if (size == '2') {
      fontSize = 'md';
    } else {
      fontSize = 'lg';
    }
    if (inputType) {
      this.setRangeStyle(this.fontSizeInput, this.fontSizeRange);
    }
    this.defaultSettings.settings.forEach((element: any) => {
      if (element.name === 'fontSize') {
        element.value = fontSize;
      }
    });

    // this.defaultSettings[0].fontSize = size;
    this.subs.add(
      this._commonService.updateUserSettings(this.defaultSettings).subscribe((res: any) => {
        if (res) {
          this.selectedFontSize = fontSize;
          this.themeService.changeFontSize(fontSize);
        }
      })
    );
  }

  setCursorStyle(type: string, inputType?: any) {
    this.cursorRange = +type;
    type = `type${type}`;
    this.defaultSettings.settings.forEach((element: any) => {
      if (element.name === 'cursor') {
        element.value = type;
      }
    });
    if (inputType) {
      this.setRangeStyle(this.curserSizeInput, this.cursorRange);
    }
    // this.defaultSettings[0].cursor = type;
    this.subs.add(
      this._commonService.updateUserSettings(this.defaultSettings).subscribe((res: any) => {
        if (res) {
          this.selectedCursor = type;
          this.themeService.changeCursorStyle(type);
        }
      })
    );
  }

  setRangeStyle(range: any, value: number) {
    // const min = +range.nativeElement.min;
    // const max = +range.nativeElement.max;
    // const currentVal = +range.nativeElement.value;
    const fillValue = value == 1 ? 0 : (value == 2 ? 50 : 100);
    this.renderer.setStyle(range?.nativeElement, 'background-size', (`${fillValue}% 100%`));
    // range.style.backgroundSize = `${((value - min) / (max - min)) * 100}% 100%`;
  }

  // resetSettings() {
  //   const settings = this.themeService.getDefaultSettings();
  //   this.subs.add(
  //     this._commonService.updateUserSettings(settings).subscribe((res: any) => {
  //       this.themeService.resetSettings(res);
  //     })
  //   );
  // }

  languageChange(lang: string) {
    document.cookie = `lang=${lang}; path=/;`;
    this.defaultSettings.settings[3].value = lang;

    this.subs.add(
      this._commonService.updateUserSettings(this.defaultSettings).subscribe((res: any) => {
        if (res) {
          this._translate.use(lang);
          this.themeService.changeLanguage(lang);
          this.selectedLanguage = lang;
          location.reload();
        }
      })
    );
  }

  toggleMenu(event: any, status: boolean, type: string = 'mobileMenu') {
    event.stopPropagation();
    if (status) {
      if (type === 'mobileMenu') {
        this.isMenuExpanded = !this.isMenuExpanded;
      } else {
        this.hamburgerMenu = !this.hamburgerMenu;
        this.renderer.addClass(this.document.body, 'ifp-modal-open');
        if (this.isDomain) {
          this.selectedNav = '';
          this.isDomain = false;
        }
      }
      if (!this.isMenuExpanded && !this.hamburgerMenu) {
        this.isDomain = false;
        this.selectedItem = '';
        this.selectedNav = this.router.url;
        this.renderer.removeClass(this.document.body, 'ifp-modal-open');
      }
    } else {
      // window.scrollTo(0, pos);
      if (type === 'mobileMenu') {
        this.isMenuExpanded = false;
      } else {
        this.hamburgerMenu = false;
      }
      this.isMenuExpanded = false;
      this.isDomain = false;
      this.selectedItem = '';
      this.showSearchBar = false;
      this.renderer.removeClass(this.document.body, 'ifp-modal-open');
    }
  }

  logout() {
    this._msal.logout();
  }

  replaceSearchText() {
    return this.searchText ? this.searchText.replace(/^\s+/, '') : this.searchText;
  }

  getSearch(e: any) {
    this.searchText = this.searchText ? this.searchText.replace(/^\s+/, '') : this.searchText;
    // this.subs.add(this.store.select(selectSearchResponse).subscribe(() => {
    if (!this.searchText) {
      return;
    }
    this.isHidePlaceHolder = e.target.value?.length > 0 ? true : false;
    this.searchText = e.target.value;
    this.searchInput.next(e.target.value);
  }

  isObjectEmpty(obj: any): boolean {
    return Object.keys(obj).length === 0;
  }

  getSugessions(data: any) {
    this.suggestionList = [];
    if (!this.headerService.isApiLoading) {
      if (data?.length > 0) {
        for (const item of data) {
          if (item.items.length > 0) {
            item.items.forEach((element: { title: string; }) => {
              this.suggestionList.push(element);
            });
          }
        }
      }
    }

  }

  // formatiing header spesific count text ;
  formatData(data: string) {
    const value = data.split(' ');
    let returnValue: string = '';
    for (let item of value) {
      if (item.startsWith('[')) {
        item = item.replace(/^\[([\s\S]*)]$/, '$1');
        item = `<span class="ifp-header__count">${item}</span>`;
      }
      returnValue = `${returnValue}${' '}${item}`;
    }
    return returnValue;
  }

  onNotificationImageError(e: any) {
    e.target.src = '../../../assets/images/bar-arrow.svg';
  }

  removeTooltip(type: string = 'empty') {
    setTimeout(() => {
      const spanElements = document.querySelectorAll('span');
      spanElements.forEach((span) => {
        if (span.classList.contains('ifp-tooltip')) {
          this.renderer.removeClass(span, 'ifp-tooltip--show');
        }
      });
    }, 2000);
    // if (type === 'notif') {
    //   document.body.style.position = 'hidden';
    //   document.body.style.height = '100vh';
    // }

  }

  changeStyle() {
    // document.body.style.overflow = 'auto';
    // document.body.style.height = 'auto';
  }

  checkValue() {
    let isCount = false;
    this.notificationData$.subscribe(resp => {
      if (resp.data.length > 0) {
        isCount = true;
      }
    });
    return isCount;
  }

  goToSearchPage() {
    this.searchText = this.searchText ? this.searchText.replace(/^\s+/, '') : this.searchText;
    // this.subs.add(this.store.select(selectSearchResponse).subscribe(() => {
    if (!this.searchText) {
      return;
    }
    this.suggestionDirective.hideSuggestions();
    const queryParams = { searchData: this.searchText };
    this.router.navigate(['/search'], { queryParams: queryParams });
    // }));
  }

  goToPage() {
    this.router.navigate(['/search']);
  }

  // read notification ;
  readNotification(notification: any) {
    this.subs.add(this.notificationService.readNotification(notification.NOTIFICATION_ID).subscribe({
      next: _next => {
        this.store.dispatch(getNotification({ status: 'UNREAD' }));
        this.goTODetail(notification);
      }
    }));

    this.notificationDropdown.nativeElement.style.pointerEvents = 'none';
    setTimeout(() => {
      this.notificationDropdown.nativeElement.style.pointerEvents = 'auto';
    }, 400);
  }

  goTODetail(item: any) {
    let type = item.CONTENT_TYPE === appType.scad_official_indicator.name ? item.CONTENT_TYPE : item.APP_TYPE;
    type = type ? type.replace(/-/g, '_').toLowerCase() : undefined;
    const isVisa = (item?.CONTENT_TYPE === appType.officialInsights.type) || (item?.CONTENT_TYPE === appType.innovativeInsights.type);
    if (item?.CONTENT_TYPE && item.NODE_ID) {
      const indicatorUrl = this._formater.setIndicatorUrl(type, item.NODE_ID);
      if (item?.CONTENT_TYPE === isVisa || item?.CONTENT_TYPE === appType.scad_official_indicator.name) {
        const queryParams = isVisa ? {
          visa: true, visaView: item.SOURCE_NAME, contentType: item.CONTENT_TYPE, domainId: item.NODE_ID
        } : { visa: false, contentType: item.CONTENT_TYPE, domainId: item.NODE_ID };
        this.router.navigate([indicatorUrl], { queryParams: queryParams });
      } else {
        this.router.navigate([indicatorUrl]);
      }
    }
  }


  checkUrl() {
    return this.router.url;
  }

  // open domain Component ;
  openDomain(event: MouseEvent) {
    event.stopPropagation();
    this.selectedItem = '';
    this.isDomain = !this.isDomain;
    this.selectedNav = this.isDomain ? '/domain' : this.router.url;
    if (this.isDomain) {
      if (!this.isMobile) {
        this.isMenuExpanded = false;
      }
      this.hamburgerMenu = false;
      this.renderer.addClass(this.document.body, 'ifp-modal-open');
    } else {
      this.renderer.removeClass(this.document.body, 'ifp-modal-open');
    }
  }

  assignActive(value: string) {
    this.selectedNav = value;
    this.isDomain = false;
  }

  gotoNotifications(type: string, status: string = '') {
    let params = {};
    if (status !== '') {
      params = { status: status };
    }
    this.router.navigate([type == 'view' ? '/notifications' : 'enabled-notification'], { queryParams: params });
    this.notificationDropdown.nativeElement.style.pointerEvents = 'none';
    setTimeout(() => {
      this.notificationDropdown.nativeElement.style.pointerEvents = 'auto';
    }, 300);
  }

  outsideClick(event: any) {
    if ((window.matchMedia('(min-width: 1024px)').matches) && (!this.megamenu?.nativeElement.contains(event.target)) && this.isDomain) {
      this.isDomain = false;
      this.selectedNav = this.router.url;
      this.renderer.removeClass(this.document.body, 'ifp-modal-open');
      this.headerService.domainMenuRest$.next(true);
    }

    if (!this.hamburger?.nativeElement.contains(event.target) && this.hamburgerMenu) {
      this.hamburgerMenu = false;
      this.renderer.removeClass(this.document.body, 'ifp-modal-open');
    }
    if (this.hamburger?.nativeElement.contains(event.target)) {
      this.selectedNav = '';
    }
    if (!this.megamenu?.nativeElement.contains(event.target)) {
      this.selectedNav = '';
    }
  }

  outsideGovermentClick(event: any) {
    if ((window.matchMedia('(min-width: 1024px)').matches) && (!this.government?.nativeElement.contains(event.target)) && this.isGovernance) {
      this.isGovernance = false;
    }
  }

  //  category filter and route;

  filterResult(cat: any) {
    cat = cloneDeep(cat);
    cat.isSelected = true;
    this.headerService.selectedCatogory = cat;
    this.headerService.filterSearch$.next(true);
    this.router.navigate(['/search']);
  }

  filterDomain(cat: any) {
    this.headerService.selectedDomain = cat.key;
    this.headerService.filterSearchDomain$.next(true);
    this.router.navigate(['/search']);
  }

  getSelectedSetting(item: string) {
    this.selectedItem = this.selectedItem !== item ? item : '';
    this.isDomain = false;
    this.selectedNav = this.router.url;
  }

  checkClassValue(data: any) {
    let classValue = false;
    if (data.result.contentTypes && data.result.contentTypes[0]?.contentType === 'no-data') {
      classValue = true;
    }
    return classValue;
  }

  showSearch() {
    this.showSearchBar = !this.showSearchBar;
    if (this.showSearchBar) {
      this.isMenuExpanded = false;
    }
    setTimeout(() => { // this will make the execution after the above boolean has changed
      this.searchBar?.nativeElement.focus();
    }, 0);
  }

  startJourney() {
    // if (this.themeService.isStartJourny) {
    //   this.themeService.isStartJourny = false;
    //   this._journeyService.removeBlinker.next(true);
    //   return;
    // }
    this._msal.showTour = false;
    const isHome = this.router.url === '/home' ? 0 : 1500;
    this.router.navigate(['home']);
    setTimeout(() => {
      this.subs.add(this._journeyService.getUserJourneyData().subscribe((data: any) => {
        data.status = false;
        data.skipModel = true;
        this.themeService.startJourny$.next({ data: data, status: true });
        this._journeyService.observableActivate.next(true);
      }));
      this.themeService.isStartJourny = true;
    }, isHome);
  }

  hidePlaceHolder() {
    this.isHidePlaceHolder = true;
    this.searchBar.nativeElement.focus();
  }

  onBlurFunction() {
    this.isHidePlaceHolder = !this.searchText ? false : true;
  }


  getKeys(url: string) {
    let key = '';
    if (url == 'store') {
      key = 'myapps';
    }
    if (url == '/glossary') {
      key = 'glossary';
    }
    if (url == 'census') {
      key = 'census';
    }
    return key;
  }

  getRoute(url: string) {
    let route = '';
    if (url == 'store') {
      route = 'store';
    }
    if (url == '/glossary') {
      route = 'glossary';
    }
    if (url?.includes('https')) {
      route = url;
    }
    return route;
  }

  openLink(url: string, item: any) {
    this.isGovernance = false;
    if (item.menu_title == 'census') {
      window.open(this._commonService.censusData?.url?.uri, '_blank');
    } else {
      const fullUrl = new URL(url, window.location.origin);
      const params = new URLSearchParams(fullUrl.search);
      const queryParams = Object.fromEntries(params.entries());
      this.router.navigate([url.split('?')[0]], {
        queryParams: queryParams
      });
    }
  }

  setRangeValue(value: string, rangeElement: any) {
    let currentValue;
    if (value == 'sm' || value == 'type1') {
      currentValue = 1;
    } else if (value == 'md' || value == 'type2') {
      currentValue = 2;
    } else {
      currentValue = 3;
    }
    if (rangeElement) {
      this.setRangeStyle(rangeElement, currentValue);
    }
    return currentValue;
  }

  checkCensus(menuTitle: string) {
    if (menuTitle.toLowerCase() !== 'census' && menuTitle != 'التعداد') {
      return true;
    }
    if (this._commonService.censusData?.length <= 0) {
      this._commonService.censusData = undefined;
    }
    return !!this._commonService.censusData;
  }

  convertToBase64(imageUrl: string): Promise<string> {
    return this.http.get(imageUrl, { responseType: 'blob' })
      .toPromise()
      .then((blob: Blob | undefined) => {
        if (!blob) {
          throw new Error('Failed to fetch image');
        }
        return new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onerror = reject;
          reader.onload = () => {
            if (typeof reader.result === 'string') {
              resolve(reader.result);
            } else {
              reject(new Error('Failed to convert image to Base64'));
            }
          };
          reader.readAsDataURL(blob);
        });
      });
  }

  // ** for terms and cnts //;

  termsResponse(event: any) {
    if (event) {
      this.subs.add(this._commonService.setDownloadTermsStatus(staticKeys.newsLetter).subscribe(_resp => {
        this.tncModal.removeModal();
        window.open(this._commonService.censusData?.url?.uri, '_blank');
      }));
    } else {
      this.tncModal.removeModal();
    }
  }

  openGovernmentDahboards(event: MouseEvent) {
    this.isGovernance = !this.isGovernance;
  }

  onDownloadAuditReport() {
    this.subs.add(
      this._apiservice.downloadAuditReport().subscribe({
        next: (value) => {
          const matches = (value.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
          const nameValue = matches ? matches[1] : 'ai_messages_export';
          this._internalDownloadService.downloadFiles(value.body, nameValue);
        },
        error: (error) => {
          this._toaster.error(error?.error?.message);
        }
      })
    )
  }

  checkAuditReportApproval() {
    this.auditReportAccess.set(this._apiservice.auditReportaApprovals.includes(this._msal?.getLoginData?.account?.username))
  }


  openDashboards(data: any) {
    this.isGovernance = false;
    const queryParams = { type: 'internal', feature: data.feature_name };
    this.router.navigate(['/common-dashboard/01'], { queryParams: queryParams });
  }




  ngOnDestroy(): void {
    clearInterval(this.intervalId);
    this.subs.unsubscribe();
  }
}
