
export const geoMapKeys = {
  selectedRegion: 'abu-dhabi',

  stagingCmsUrl: 'https://bayaan-cms-staging.scad.gov.ae',
  prodCmsUrl: 'https://bayaan-cms-prod-g42.scad.gov.ae',
  
  mapKey: '00825e8edcf644e68ef9a3a58bc2ffa5',
  mapKeyRestricted: '7717eea50434407ca22bda9ed975cadd',

  domains: [
    {SELECT_CODE: 1, SELECT_EN: "Population", SELECT_AR: "السكان"},
    {SELECT_CODE: 2, SELECT_EN: "Labour Force", SELECT_AR: "القوى العاملة"},
    {SELECT_CODE: 3, SELECT_EN: "Real Estate", SELECT_AR: "العقارات"},
  ],

  selectionLevels: [
    {SELECT_CODE: 1, SELECT_EN: 'Region', SELECT_AR: 'المنطقة'},
    {SELECT_CODE: 2, SELECT_EN: 'District', SELECT_AR: 'المنطقة الفرعية'},
    {SELECT_CODE: 3, SELECT_EN: 'Community', SELECT_AR: 'التجمع'}
  ],

  mapTypes: [
    {SELECT_CODE: 1, SELECT_EN: 'Heat Map', SELECT_AR: 'خريطة حرارية', LAYER_NAME: 'HEATMAP_ALL_REAL_DATA'},
    {SELECT_CODE: 2, SELECT_EN: 'Dot Density', SELECT_AR: 'خريطة الكثافة', LAYER_NAME: 'DOTDENSITY_ALL_REAL_DATA'},
    {SELECT_CODE: 3, SELECT_EN: 'Thematic Map', SELECT_AR: 'خريطة موضوعية', LAYER_NAME: 'THEMATIC_DEFAULT_REAL_DATA'}
  ],

  allRegions: {
    REGION_CODE: 0,
    REGION_AR: 'امارة أبوظبي',
    REGION_EN: 'Abu Dhabi Emirate'
  },

  allDistricts: {
    REGION_CODE: 0,
    DISTRICT_CODE: 0,
    DISTRICT_EN: 'Abu Dhabi Emirate',
    DISTRICT_AR: 'امارة أبوظبي'
  },

  mapRegionsTitle: 'REGION',
  mapDistrictsTitle: 'DISTRICTS',
  mapCommunitiesTitle: 'COMMUNITIES',
  mapDotDensityTitle: "DOT_DENSITY",
  mapHeatDefaultRealData: "HEATMAP_ALL_REAL_DATA",
  // mapHeatDefaultRealData: "DISS_BOUNDARY_GEOSPATIAL-V3-LIMITED",

  // mapHeatRealData: "HEATMAP_REAL_DATA",
  //  mapHeatRealDataCitizen: "HEATMAP_CITIZEN_REAL_DATA",
  //  mapHeatRealDataGender: "HEATMAP_GENDER_REAL_DATA",
  //  mapHeatRealDataMartial: "HEATMAP_MARITAL_REAL_DATA",
  //  mapHeatRealDataAttainement: "HEATMAP_EDUCATION_REAL_DATA",
  //  mapHeatRealDataHousehold: "",
  //  mapHeatRealDataDisability: "HEATMAP_DISABILITY_REAL_DATA",   
  //  mapHeatRealDataAll: "HEATMAP_ALL_REAL_DATA",
 
  defaultDomain: 1,


  genderIndicator: {
    maleCode: 1,
    maleColor: '#006FFF',
    femaleCode: 2,
    femaleColor: '#FF16A2'
  },

  citizenshipIndicator: {
    emiratiCode: 1,
    emiratiColor: '#DC6F6F',
    nonEmiratiCode: 2,
    nonEmiratiColor: '#5329E3'
  },

  regionIndicator: {
    abudhabiCode: 101,
    abudhabiColor: '#006FFF',
    alainCode: 102,
    alainColor: '#FF16A2',
    aldhafraCode: 103,
    aldhafraColor: '#DC6F6F'
  },

  defaultQueryParams: {
    'REGION_CODE': [101, 102, 103],
    'DISTRICT_CODE': [],
    'COMMUNITY_CODE': [],
    'CITIZEN_CODE': [],
    'GENDER_CODE': [],
    'MARITAL_CODE': [],
    'ATTAINMENT_CODE': [],
    'HOUSEHOLD_CODE': [], 
    'ENROLLMENT_TYPE_CODE': [],
    'SPECIALIZATION_CODE': [],
    'SCHOOL_ENROLLMENT_LEVEL_CODE': [],

    'BUILDING_TYPE_CODE': [],
    'BUILDING_USE_CODE': [], 

    'UNITS_TYPE_CODE': [],
    'UNITS_USE_CODE': [], 
    
    'YEAR_CODE': [''],
    'QUARTER_CODE': ['']
  },


  defaultRegionCodes: [101, 102, 103],

  domainsMenuId: 'domain',
  regionMenuId: 'region',
  districtMenuId: 'district',
  heatMap: "HEAT_MAP",
  communityMenuId: 'community',
  compareMenuId: 'compare',
  otherMenuId: 'other',

  abudhabiEmirate: 'Abu Dhabi Emirate',
  district: 'District',
  region: 'Region',
  community: 'Community',
  customize: 'Customize',
  preview: 'Preview',
  yearsFilter: 'years',
  citizenshipFilter: 'citizenship',
  genderFilter: 'gender',
  maritalFilter: 'marital',
  attainmentFilter: 'attainment',
  houseHoldFilter: 'houseHold',
  buildingsTypeFilter: 'buildingsType',
  buildingsUseFilter: 'buildingsUse',
  unitsTypeFilter: 'unitsType',
  unitsUseFilter: 'unitsUse',
  mapButtons: ['Customize', 'Preview'],
  mapButtonsAr: ['تخصيص', 'معاينة'],

  regionsFilterLabel: {EN: 'Regions', AR: 'المنطقة', PlaceholderEN: 'Search for Regions', PlaceholderAR: 'ابحث عن المنطقة'},
  districtsFilterLabel: {EN: 'Districts', AR: 'المنطقة الفرعية', PlaceholderEN: 'Search for Districts', PlaceholderAR: 'ابحث عن المنطقة الفرعية'},
  communitiesFilterLabel: {EN: 'Communities', AR: 'التجمع', PlaceholderEN: 'Search for Communities', PlaceholderAR: 'ابحث عن التجمع'},
  compareFilterLabel: {EN: 'Compare', AR: 'مقارنة'},
  otherFilterLabel: {EN: 'Other Filters', AR: 'فلاتر أخرى'},
  selectionLevelLabel: {EN: 'Level', AR: 'المستوى'},
  mapTypeLabel: {EN: ' Type', AR: ' النوع'},
};
