import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import MapView from '@arcgis/core/views/MapView';
import Measurement from '@arcgis/core/widgets/Measurement';
import Expand from '@arcgis/core/widgets/Expand';
import { TranslateService } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import {SubSink} from "subsink";

type MeasurementTool = "area" | "distance" | "direct-line";

@Component({
  selector: 'ifp-measurements',
  standalone: true,
  template: ` <div #expandNode></div> `,
  styles: [
    `
      .measurement-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .measurement-button {
        padding: 8px;
        background-color: #fff;
        border: 1px solid #ccc;
        cursor: pointer;
      }
    `,
  ],
})
export class MeasurementsComponent implements OnInit, On<PERSON><PERSON>roy, OnChanges {
  @Input() view!: MapView;
  private measurement: Measurement | undefined;
  private expand: Expand | undefined;

  subsink: SubSink = new SubSink();
  public language: string = 'en';

  constructor(
    private _translate: TranslateService,
    private themeService: ThemeService
  ) {}

  ngOnInit() {
    this.subsink.add(
      this.themeService.defaultLang$.subscribe((lang: string) => {
        this.language = lang;
      })
    );
    this.initializeWidget();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['view'] && !changes['view'].firstChange) {
      this.initializeWidget();
    }
  }

  private initializeWidget() {
    this.destroyWidgets();

    if (this.view) {
      this.measurement = new Measurement({
        view: this.view,
        linearUnit: 'kilometers',
        areaUnit: 'square-kilometers',
      } as __esri.MeasurementProperties);

      const content = document.createElement('div');
      content.className = 'measurement-actions';

      const distanceButton = this.createButton(
        `<calcite-icon icon="line" text-label="360 degree view" scale="l" title="${this.language == 'en' ? 'Distance Measurement' : 'قياس المسافة'}"></calcite-icon>`,
        () => this.activateTool('distance')
      );
      const areaButton = this.createButton(
        `<calcite-icon icon="measure-area" text-label="360 degree view" scale="l" title="${this.language == 'en' ? 'Area Measurement' : 'قياس المساحة'}"></calcite-icon>`,
        () => this.activateTool('area')
      );
      const deleteButton = this.createButton(
        `<calcite-icon icon="trash" text-label="360 degree view" scale="l" title="${this.language == 'en' ? 'Remove Measurement' : 'إزالة القياس'}"></calcite-icon>`,
        () => this.clearMeasurements()
      );

      content.appendChild(distanceButton);
      content.appendChild(areaButton);
      content.appendChild(deleteButton);

      this.expand = new Expand({
        view: this.view,
        content: content,
        expandIconClass: 'esri-icon-measure',
        group: 'bottom-left',
        expandTooltip: this.language == 'en' ? 'Measurement' : 'القياسات',
        collapseTooltip: this.language == 'en' ? 'Collapse' : 'تصغير'
      } as __esri.ExpandProperties);

      this.view.ui.add(this.expand, 'bottom-left');
    }
  }

  private createButton(text: string, onClick: () => void): HTMLButtonElement {
    const button = document.createElement('button');
    button.innerHTML = text;
    button.className = 'measurement-button';
    button.onclick = onClick;
    return button;
  }

  private activateTool(toolType: MeasurementTool) {
    if (this.measurement) {
      this.measurement.activeTool = toolType;
    }
  }

  private clearMeasurements() {
    if (this.measurement) {
      this.measurement.clear();
      //@ts-ignore
      // this.measurement.activeTool = undefined;
    }
  }

  private destroyWidgets() {
    if (this.measurement) {
      this.measurement.destroy();
      this.measurement = undefined;
    }
    if (this.expand) {
      this.expand.destroy();
      this.expand = undefined;
    }
  }

  ngOnDestroy() {
    this.destroyWidgets();
    this.subsink.unsubscribe();
  }
}
