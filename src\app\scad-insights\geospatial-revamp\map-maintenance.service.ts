                 import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import WebMap from "@arcgis/core/WebMap.js";
import MapView from "@arcgis/core/views/MapView.js";
import { CensusServiceService } from './census-service.service';
import { MapConfigService } from './map-config.service';

@Injectable({
  providedIn: 'root'
})
export class MapMaintenanceService {

  constructor(
    private http: HttpClient,
    private gisSharedService: CensusServiceService,
    private mapConfig: MapConfigService
  ) { 
    this.gisSharedService.setupEsriConfig(
      this.mapConfig._esriPortalUrl,
      this.mapConfig._esriTokenForAuthentication                             
    );
  }

  checkMaintenanceStatus(): Promise<any> {
    const webMap = new WebMap({
      portalItem: {
        // id: 'ea24c7dd995b46d586a6313edcda301a',
        id: "29dcc520ee21449c9657d8e35de57c1e" // development for demo

        // id: "c8ee5c377c6e4e04b7bd17ab31713432" // development for demo
      }
    });

    return new Promise((resolve, reject) => {
      const mapView = new MapView({
        map: webMap,
        // container: 'viewDiv'
      });

      webMap.when((webmap: any) => {
        // Check for tables
        if (webmap.tables && webmap.tables.length > 0) {
          webmap.tables.forEach((table: __esri.Layer) => {
            if (table.title === 'POI') {
              const featureTable = table as __esri.FeatureLayer;
              resolve(featureTable.queryFeatures().then((result) => {
                return result.features
                .filter(feature => feature.attributes.CATEGORY == 'APP')
                .map(feature => ({
                  category: feature.attributes.CATEGORY,
                  active: feature.attributes.ACTIVE,
                  value: feature.attributes.VALUE
                }));
              }));
            } else if(table.title == 'TOTALS_SUMMARY') {
              const featureTable = table as __esri.FeatureLayer;
              resolve(featureTable.queryFeatures().then((result) => {
                localStorage.setItem('realestateTotalSummary', JSON.stringify(result.features));
              }));
            }
            webmap.layers.items.map((layer: any) => {
              layer.outFields = ['*'];
              if (layer.title == 'Districts') {
                if (layer.type === 'feature') {
                  layer.parsedUrl.path;
                  this.gisSharedService.setDistrictsFromService(layer.parsedUrl.path, '1=1');
                }
              } else if(layer.title == 'DOT_DENSITY_WOS') {
                if (layer.type === 'feature') {
                  layer.parsedUrl.path;
                  this.gisSharedService.setCensusPopDistrictsFromService(layer.parsedUrl.path, '1=1');
                }
              } else if(layer.title == 'CHART_MALE_FEMALE') {
                if (layer.type === 'feature') {
                  layer.parsedUrl.path;
                  this.gisSharedService.setCensusGenderPopDistrictsFromService(layer.parsedUrl.path, '1=1');
                }
              }
            });
          });
        } else {
          resolve('No table')
          console.log('No tables found in the web map');
        }
      },(error: any) => {
        console.error('Error loading web map', error);
        reject({ status: false, error: 'Failed to load web map' });
      });
    });
  }
}
