import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import Zoom from '@arcgis/core/widgets/Zoom';
import MapView from '@arcgis/core/views/MapView';
import { TranslateService } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import {SubSink} from "subsink";

@Component({
  selector: 'ifp-map-zoom',
  standalone: true,
  template: '<div #zoomNode></div>',
})
export class MapZoomComponent implements OnInit, OnDestroy, OnChanges {
  @Input() view!: MapView;
  private zoom: Zoom | undefined;

  subsink: SubSink = new SubSink();
  public language: string = 'en';

  constructor(
    private _translate: TranslateService,
    private themeService: ThemeService
  ) {}

  ngOnInit() {
    this.subsink.add(
      this.themeService.defaultLang$.subscribe((lang: string) => {
        this.language = lang;
      })
    );
    this.initializeWidget();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['view'] && !changes['view'].firstChange) {
      this.initializeWidget();
    }
  }

  private initializeWidget() {
    this.destroyWidgets();

    if (this.view) {
      this.zoom = new Zoom({
        view: this.view,
        expandTooltip: 'Zoom to Map',
      } as __esri.ZoomProperties);

      this.view.ui.add(this.zoom, {
        position: 'bottom-left', 
      });

      this.view.when(() => {
        const zoomButtons = document.querySelectorAll('.esri-widget--button');
        
        zoomButtons.forEach(button => {
          const buttonElement = button as HTMLElement;
          const icon = buttonElement.querySelector('.esri-icon');
          const fallbackText = buttonElement.querySelector('.esri-icon-font-fallback-text');

          if (icon && fallbackText) {
            if (icon.classList.contains('esri-icon-plus')) {
              buttonElement.setAttribute("title", this.language == 'en' ? "Zoom in" : 'تكبير');
            } else if (icon.classList.contains('esri-icon-minus')) {
              buttonElement.setAttribute("title", this.language == 'en' ? "Zoom out": 'تصغير');
            }
          }
        });
      });
    }
  }

  private destroyWidgets() {
    if (this.zoom) {
      this.zoom.destroy();
      this.zoom = undefined;
    }
  }

  ngOnDestroy() {
    this.destroyWidgets();
  }
}
