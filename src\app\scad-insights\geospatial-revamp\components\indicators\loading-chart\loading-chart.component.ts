import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  OnChanges,
  OnDestroy,
  OnInit,
  Input,
  SimpleChanges,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';


@Component({
  selector: 'ifp-loading-chart', 
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './loading-chart.component.html',
  styleUrl: './loading-chart.component.scss'
})
export class LoadingChartComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

  @Input() isCustomize: boolean = false;

  ngOnChanges(changes: SimpleChanges) {

  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }

}

