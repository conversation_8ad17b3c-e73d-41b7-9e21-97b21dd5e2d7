<div class="ifp-range" [class]="'ifp-range--'+sliderTheme" [ngClass]="{'ifp-range--steps': step > 1,'ifp-range--disable': disable()}">
  <!-- Min/Max Labels -->
  <div class="ifp-range__labels">
    <span class="ifp-range__label ifp-range__label--min">{{minRangeValue}}{{unit}}</span>
    <span class="ifp-range__label ifp-range__label--max">{{maxRangeValue}}{{unit}}</span>
  </div>
  
  @if (dots.length && step > 0) {
    <div class="ifp-range__dots-wrapper">
      @for (dot of dots; track $index) {
        <span class="ifp-range__dot"></span>
      }
    </div>
  }
  
  <div class="ifp-range__track">
    <input #rangeInput 
           type="range" 
           class="ifp-range__input" 
           [min]="minRangeValue" 
           [max]="maxRangeValue.toString()" 
           [value]="rangeValue ? rangeValue.toString() : minRangeValue.toString()" 
           [step]="step || 1" 
           (input)="onChangeSlider(+rangeInput.value)">
  </div>
  
  @if (enableTooltip) {
    <div #rangeTooltip class="ifp-range__tooltip">
      {{(+rangeInput.value).toFixed(decimals)}}{{unit}}
    </div>
  }
</div>