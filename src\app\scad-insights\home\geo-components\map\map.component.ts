import {Component, OnInit, AfterViewInit, ViewChild, TemplateRef, ElementRef, OnDestroy, Input} from '@angular/core';
import { Router } from '@angular/router';
import ArcGISMap from "@arcgis/core/Map";
import WebMap from "@arcgis/core/WebMap";
import MapView from "@arcgis/core/views/MapView";
import SceneView from "@arcgis/core/views/SceneView";
import VectorTileLayer from "@arcgis/core/layers/VectorTileLayer";
import Basemap from "@arcgis/core/Basemap";
import Fullscreen from "@arcgis/core/widgets/Fullscreen";
import Expand from "@arcgis/core/widgets/Expand";
import Search from "@arcgis/core/widgets/Search";
import Bookmarks from "@arcgis/core/widgets/Bookmarks";
import BasemapGallery from "@arcgis/core/widgets/BasemapGallery";
import LayerList from "@arcgis/core/widgets/LayerList";
import Sketch from '@arcgis/core/widgets/Sketch';
import Measurement from '@arcgis/core/widgets/Measurement';
import Graphic from '@arcgis/core/Graphic';
import { Polygon } from '@arcgis/core/geometry';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import esriConfig from "@arcgis/core/config";
// import * as Query from "@arcgis/core/rest/query";
import Query from "@arcgis/core/rest/support/Query.js";
import uniqueValues from "@arcgis/core/smartMapping/statistics/uniqueValues";
import {SharedService} from "../../geo-services/shared.service";
import HeatmapRenderer from "@arcgis/core/renderers/HeatmapRenderer.js";
import Point from '@arcgis/core/geometry/Point';
import SketchViewModel from "@arcgis/core/widgets/Sketch/SketchViewModel.js";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine.js";
import StatisticDefinition from "@arcgis/core/rest/support/StatisticDefinition.js";
import { NgbModalConfig, NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import {ɵDomSanitizerImpl} from '@angular/platform-browser';
import { headerKeys } from 'src/app/scad-insights/core/constants/header.constants';
import {ThemeService} from "../../../core/services/theme/theme.service";
import {SubSink} from "subsink";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol.js";
import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";
import Widget from "@arcgis/core/widgets/Widget.js";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol.js";
import Portal from "@arcgis/core/portal/Portal.js";
import PortalBasemapsSource from "@arcgis/core/widgets/BasemapGallery/support/PortalBasemapsSource.js";
import GroupLayer from "@arcgis/core/layers/GroupLayer.js";
import DotDensityRenderer from "@arcgis/core/renderers/DotDensityRenderer.js";
import UniqueValueRenderer from "@arcgis/core/renderers/UniqueValueRenderer.js";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol.js";
import SimpleRenderer from "@arcgis/core/renderers/SimpleRenderer.js";
import LocalBasemapsSource from "@arcgis/core/widgets/BasemapGallery/support/LocalBasemapsSource.js";
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {MapServiceService} from "../../geospatial/map-service.service";
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';

declare global {
  interface Window {
    dataLayer: any[]; // Define dataLayer as an array of any type
  }
}

@Component({
    selector: 'app-map',
    templateUrl: './map.component.html',
    styleUrls: ['./map.component.scss'],
    imports: [TranslateModule],
    providers: [NgbModalConfig, NgbModal]
})
export class MapComponent implements AfterViewInit, OnInit, OnDestroy {

  @Input() componentId: string | undefined;
  mainView: any;
  mainMap: any;
  graphicsLayer: any;
  arcgisAccessToken: any;
  featureLayerView: any;
  sketchViewModelMain: any;
  polygonSketchLayer: any;
  polygonDrawGraphicsLayer: any;
  selectionExpand: any;
  searchExpand: any;
  searchExpandForDot: any;
  private modalRef: NgbModalRef | null = null;
  subsink: SubSink = new SubSink();
  @ViewChild('myTemplate', { static: true }) myTemplate!: TemplateRef<any>;
  iframeUrl: any = '';
  currentTheme: any = localStorage.getItem(headerKeys.appearence);
  private selectedFeature: any = null;
  private districtGraphicLayer: any = null;
  drillDownSeries: any = [];
  layerlists: any;
  layerListExpand: any;
  basemapsToDisplay: any;
  allExpands: any[] = [];
  showForOfficialPop: string = this._translate.instant('1 Dot = 100 People');
  sourceAndDate: any = [];
  mapLoadedOnce: boolean = false;
  districtColumnNameLang: string = 'district_eng';
  districtColumnNameLangForHousehold: string = 'district_eng';

  constructor(
    private sharedService: SharedService,
    config: NgbModalConfig,
    private modalService: NgbModal,
    private sanitizer: ɵDomSanitizerImpl,
    private themeService: ThemeService,
    private router:Router,
    private _translate: TranslateService,
    private mapViewService: MapServiceService,
    private log: UsageDashboardLogService
  ) {
    config.backdrop = 'static';
    config.keyboard = false;
    // this.sharedService.geoValue = 3;
    this.showForOfficialPop = this._translate.instant('1 Dot = 100 People');
    this.sharedService.dispalySummaryData(false);
  }

  ngOnDestroy(): void {
    if (this.sharedService.sessionId) {
      this.log.logEnds(this.sharedService.sessionId, this.log.currentTime );
    }
    this.subsink.unsubscribe();
  }

  openModal(url: any) {
    this.modalRef = this.modalService.open(this.myTemplate);
    this.iframeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  ngAfterViewInit(): void {
    if(this.mapLoadedOnce == true) {
      this.createMap();
    }
    // this.createMap();
  }

  ngOnInit(): void {
    this.sharedService.authStatusForServices$.subscribe((value: any) => {
      if(value) {
        this.sharedService.getSingleFeatureWithoutGeom(this.sharedService.mapConfig.sourseAndUpdatedDate[0], "1=1").then((results: any) => {
          let features = results.features;
          features.forEach((feature: any) => {
            this.sourceAndDate.push(feature.attributes);
            if(feature.attributes.DOMAIN_NAME_KEY == 'POP_OFFICIAL_CENSUS') {
              this.sharedService.dataUpdatedDate(feature.attributes);
            }
          });

          // results.features.map((feature: any) => {
          //   this.sourceAndDate.push(feature.attributes);
          //   console.log(this.sourceAndDate)
          //   this.sourceAndDate.forEach((res: any) => {
          //     if(res.domain_name_key == 'POP_OFFICIAL') {
          //       console.log('it is showing');
          //       console.log(res);
          //       this.sharedService.dataUpdatedDate(res);
          //     }
          //   });
          // });
        });
        this.createMap();
        this.mapLoadedOnce = true;
      }
    });
    // this.sharedService.authStatusForServices$.subscribe((value: any) => {
    //   if(value) {
    //     console.log(value);
    //     this.sharedService.getSingleFeatureWithoutGeom(this.sharedService.mapConfig.sourseAndUpdatedDate[0], "1=1").then((results: any) => {
    //       results.features.map((feature: any) => {
    //         this.sourceAndDate.push(feature.attributes);
    //         this.sourceAndDate.forEach((res: any) => {
    //           if(res.domain_name_key == 'POP_OFFICIAL') {
    //             this.sharedService.dataUpdatedDate(res);
    //           }
    //         });
    //       });
    //     });
    //     this.createMap();
    //     this.mapLoadedOnce = true;
    //   }
    // });
    this.sharedService.geoSelectChanged.subscribe((newValue: any) => {
      if(newValue == 1) {
        this.changeMapLayersForJobSeekers();
      }
      else if(newValue == 2) {
        this.changeMapLayersForPopulation();
      }
      else if(newValue == 3) {
        this.changeMapLayersForDotDensity();
      }
      else if(newValue == 4) {
        this.changeMapLayersForPopByRegion();
      }
      else if(newValue == 5) {
        this.changeMapLayersForRealEastateFlatTransactions();
      }
      else if(newValue == 6) {
        this.changeMapLayersForCensusRealEstate();
      }
      else if(newValue == 7) {
        this.changeMapLayersForDotDensityOld();
      }
    });
  }

  setBaseMap(theme: any) {
    if(theme === 'light') {
      this.mainView.map = this.basemapsToDisplay['basemap1'];
      this.clearLayerList();
      this.mainView.map.removeAll();
      this.mainView.when(() => {
        this.createSelectionWidget('top-right', 'remove');
        this.createSearchWidget('top-left', 'remove');
        this.createSearchWidgetForDotDensity('top-left', 'remove');
        if(this.sharedService.geoValue == 1) {
          this.changeMapLayersForJobSeekers();
        }
        else if(this.sharedService.geoValue == 2) {
          this.changeMapLayersForPopulation();
        }
        else if(this.sharedService.geoValue == 3) {
          this.changeMapLayersForDotDensity();
        }
        else if(this.sharedService.geoValue == 4) {
          this.changeMapLayersForPopByRegion();
        }
        else if(this.sharedService.geoValue == 5) {
          this.changeMapLayersForRealEastateFlatTransactions();
        }
        else if(this.sharedService.geoValue == 6) {
          this.changeMapLayersForCensusRealEstate();
        }
        else if(this.sharedService.geoValue == 7) {
          this.changeMapLayersForDotDensityOld();
        }
      });
    } else {
      this.mainView.map = this.basemapsToDisplay['basemap2'];
      this.clearLayerList();
      this.mainView.map.removeAll();
      this.mainView.when(() => {
        this.createSelectionWidget('top-right', 'remove');
        this.createSearchWidget('top-left', 'remove');
        this.createSearchWidgetForDotDensity('top-left', 'remove');
        if(this.sharedService.geoValue == 1) {
          this.changeMapLayersForJobSeekers();
        }
        else if(this.sharedService.geoValue == 2) {
          this.changeMapLayersForPopulation();
        }
        else if(this.sharedService.geoValue == 3) {
          this.changeMapLayersForDotDensity();
        }
        else if(this.sharedService.geoValue == 4) {
          this.changeMapLayersForPopByRegion();
        }
        else if(this.sharedService.geoValue == 5) {
          this.changeMapLayersForRealEastateFlatTransactions();
        }
        else if(this.sharedService.geoValue == 6) {
          this.changeMapLayersForCensusRealEstate();
        }
        else if(this.sharedService.geoValue == 7) {
          this.changeMapLayersForDotDensityOld();
        }
      });
    }
  }


  createMap() {
    this.basemapsToDisplay = {
      basemap1: new WebMap({
        portalItem: {
          id: this.sharedService.mapConfig.basemapLightPortal[0]
        }
      }),
      basemap2: new WebMap({
        portalItem: {
          id: this.sharedService.mapConfig.basemapDarkPortal[0]
        }
      }),
    };
    this.mainMap = this.basemapsToDisplay['basemap1'];
    this.mainView = new MapView({
      map: this.mainMap,
      container: this.componentId,
      // center: [54.110943, 24.416687],
      // center: [54.410943, 24.476687],
      center: [54.410943, 24.476687],
      zoom: 11,
      // @ts-ignore
      animate: true,
      ui: {
        components: [""]
      }
    });
    // Create a new WebMap instance
    this.mainMap.load().then(() => {
      this.mainView.map = this.mainMap;
    });


    this.mainView.when(() => {
      this.createMeasurementWidget('top-right', 'top-left');
      this.createBaseMapGalleryWidget('top-left');
      this.createZoomWidget('bottom-left');
      this.createSelectionWidget('top-right', 'add');
      this.subsink.add(this.themeService.defaultTheme$.subscribe((d: any) => {
        this.setBaseMap(d);
      }));

      this.subsink.add(this.themeService.defaultLang$.subscribe((lang: any) => {
        if(lang == 'ar') {
          this.districtColumnNameLang = 'district_ara';
          this.districtColumnNameLangForHousehold = 'district_ara';
          // this.districtColumnNameLang = 'district_eng';
        } else {
          this.districtColumnNameLang = 'district_eng';
          this.districtColumnNameLangForHousehold = 'district_eng';
        }
      }));

      this.sharedService.view = this.mainView;
    });

    this.mainView.on('click', (event: any) => {
      this.mainView.hitTest(event).then((response: any) => {


        let allResults = response.results;
        if(this.sharedService.geoValue == 1) {
          let foundDistrict = false;
          for(let i = 0; i < allResults.length; i++) {
            if(allResults[i].graphic.layer.title == 'jobseeker2') {
              const feature = allResults[i].graphic;
              if (feature) {
                if(feature.geometry != null) {
                  this.addPolygonToMap(feature.geometry.rings[0]);
                  this.sharedService.calculateResulsMapClickJobSeekers(feature);
                  // this.sharedService.displayDistrictName = "District: " + feature.attributes.DISTRICT_ENG;
                  // this.calculateResults(feature.geometry, this.polygonSketchLayer);
                } else {
                  this.mainView.graphics.removeAll();
                  this.sharedService.calculateResulsMapClickJobSeekers(null);
                  // this.sharedService.displayDistrictName = "District: All";
                  // this.calculateResults(null, this.polygonSketchLayer);
                }
              }
              foundDistrict = true;
              break;
            }
          }
          if(!foundDistrict) {
            this.mainView.graphics.removeAll();
            this.sharedService.calculateResulsMapClickJobSeekers(null);
            // this.sharedService.displayDistrictName = "District: All";
            // this.calculateResults(null, this.polygonSketchLayer);
          }
        }
        else if(this.sharedService.geoValue == 2) {
          // name of the districts layer Districts
          let foundDistrict = false;
          for(let i = 0; i < allResults.length; i++) {
            if(allResults[i].graphic.layer.title == 'Districts') {
              const feature = allResults[i].graphic;
              if (feature) {
                if(feature.geometry != null) {
                  this.sharedService.displayDistrictName = "District: " + feature.attributes[this.districtColumnNameLangForHousehold];
                  this.sharedService.googleAnalyticsMap('2', feature.attributes[this.districtColumnNameLangForHousehold]);
                  this.calculateResults(feature.geometry, this.polygonSketchLayer);
                  this.addPolygonToMap(feature.geometry.rings[0]);
                  this.sharedService.createSummaryForPoi(this.mainView.map, feature.geometry);

                  // // for regional population
                  this.sharedService.setDistrictForRegion(feature.attributes);
                  this.sharedService.SCAD_D_ID_SJ = feature.attributes.scad_district_id;
                  this.sharedService.setNationalityChartDivToDisplay(false);
                } else {
                  this.mainView.graphics.removeAll();
                  this.sharedService.displayDistrictName = "District: All";
                  this.calculateResults(null, this.polygonSketchLayer);
                }
              }
              foundDistrict = true;
              break;
            }
          }
          if(!foundDistrict) {
            this.mainView.graphics.removeAll();
            this.sharedService.displayDistrictName = "District: All";
            this.sharedService.googleAnalyticsMap("2", "Abu Dhabi");
            this.calculateResults(null, this.polygonSketchLayer);
          }
        }
        else if(this.sharedService.geoValue == 3) {
          // let fieldsOffcialGender: any = ['MALE', 'FEMALE'];
          let fieldsOffcialGenderCensus: any = ['male', 'female', 'male_dc', 'female_dc'];
          let districtId: any = 999;

          let foundDistrictOff = false;
          for(let i = 0; i < allResults.length; i++) {
            if(allResults[i].graphic.layer.title == 'Official Population Overview') {
              const feature = allResults[i].graphic;
              if (feature) {
                if(feature.geometry != null) {
                  // this.sharedService.officialFigures = allResults[i];
                  this.addPolygonToMap(allResults[i].graphic.geometry.rings[0]);
                  districtId = allResults[i].graphic.attributes.district_id;
                  // allResults[i].graphic.attributes.chartName = "pieChartByCitizenship";
                  let fields: any = ['total_population', 'total_citizen', 'total_non_citizen'];
                  let whereClause = "DISTRICT_ID = "+districtId+"";
                  this.sharedService.getSingleFeatureWithoutGeom(this.sharedService.mapConfig.dotDensityLayers[0], whereClause).then((response: any) => {
                    response.features.map((result: any) => {
                      result.attributes[this.districtColumnNameLang] = allResults[i].graphic.attributes[this.districtColumnNameLang];
                      result.attributes.chartName = "pieChartByCitizenshipCensus";
                      let graphic = {
                        graphic: result
                      };
                      this.sharedService.officialFiguresCensus = graphic;
                      this.sharedService.setPopByNationalsOriginal(result.attributes);
                      // this.sharedService.setPopByNationalsOriginal(allResults[i].graphic.attributes);
                    });
                  });
                  this.sharedService.getSumIndividual(fieldsOffcialGenderCensus, this.sharedService.mapConfig.dotDensityLayers[1], whereClause, "sum", null).then((response: any) => {
                    response.features.map((result: any) => {
                      const feature = result.attributes;
                      feature.moduleSource = 'census';
                      this.sharedService.setPopByGenderOriginal(feature);
                    });
                  });
                  //for summary
                  this.sharedService.createSummaryForPoi(this.mainView.map, feature.geometry);
                  this.sharedService.googleAnalyticsMap("3", allResults[i].graphic.attributes[this.districtColumnNameLang]);
                } else {
                  this.mainView.graphics.removeAll();
                  districtId = 999;
                  let fields: any = ['total_citizen', 'total_non_citizen', 'total_population'];
                  this.sharedService.getSingleFeatureWithoutGeom(this.sharedService.mapConfig.dotDensityLayers[0], "DISTRICT_ID=999").then((response: any) => {
                    response.features.map((result: any) => {
                      result.attributes.DISTRICTNAMEENG = "Abu Dhabi";
                      result.attributes.chartName = "pieChartByCitizenshipCensus";
                      let graphic = {
                        graphic: result
                      };
                      this.sharedService.officialFiguresCensus = graphic;
                      this.sharedService.setPopByNationalsOriginal(result.attributes);
                    });
                  });
                  // this.sharedService.getSumIndividual(fields, this.sharedService.mapConfig.dotDensityLayers[0], "1=1", "sum", null).then((response: any) => {
                  //   response.features.map((result: any) => {
                  //     result.attributes.DISTRICTNAMEENG = "Abu Dhabi";
                  //     result.attributes.chartName = "pieChartByCitizenship";
                  //     let graphic = {
                  //       graphic: result
                  //     };
                  //     this.sharedService.officialFiguresCensus = graphic;
                  //     this.sharedService.setPopByNationalsOriginal(result.attributes);
                  //   });
                  // });
                  this.sharedService.googleAnalyticsMap("3", "Abu Dhabi");
                }
              }
              foundDistrictOff = true;
              break;
            }
          }
          if(!foundDistrictOff) {
            this.sharedService.googleAnalyticsMap("3", "Abu Dhabi");
            this.mainView.graphics.removeAll();
            this.sharedService.dispalySummaryData(false);
            districtId = 999;
            let fields: any = ['total_citizen', 'total_non_citizen', 'total_population'];
            this.sharedService.getSingleFeatureWithoutGeom(this.sharedService.mapConfig.dotDensityLayers[0], "DISTRICT_ID=999").then((response: any) => {
              response.features.map((result: any) => {
                result.attributes.DISTRICTNAMEENG = "Abu Dhabi";
                result.attributes.chartName = "pieChartByCitizenshipCensus";
                let graphic = {
                  graphic: result
                };
                this.sharedService.officialFiguresCensus = graphic;
                this.sharedService.setPopByNationalsOriginal(result.attributes);
              });
            });
          }
          this.sharedService.getSumIndividual(fieldsOffcialGenderCensus, this.sharedService.mapConfig.dotDensityLayers[1], "district_id="+districtId+"", "sum", null).then((response: any) => {
            response.features.map((result: any) => {
              const features = result.attributes;
              features.moduleSource = 'census';
              this.sharedService.setPopByGenderOriginal(features);
            });
          });
        }
        else if(this.sharedService.geoValue == 7) {
          // let fieldsOffcialGender: any = ['MALE', 'FEMALE'];
          let fieldsOffcialGender: any = ['male', 'female'];
          let districtId: any = 999;

          let foundDistrictOff = false;
          for(let i = 0; i < allResults.length; i++) {
            if(allResults[i].graphic.layer.title == 'Official Population Overview') {
              const feature = allResults[i].graphic;
              if (feature) {
                if(feature.geometry != null) {
                  // this.sharedService.officialFigures = allResults[i];
                  this.addPolygonToMap(allResults[i].graphic.geometry.rings[0]);
                  districtId = allResults[i].graphic.attributes.district_id;
                  // allResults[i].graphic.attributes.chartName = "pieChartByCitizenship";
                  let fields: any = ['total_population', 'total_citizen', 'total_non_citizen'];
                  let whereClause = "district_id = "+districtId+"";
                  this.sharedService.getSumIndividual(fields, this.sharedService.mapConfig.dotDensityLayersOld[0], whereClause, "sum", null).then((response: any) => {
                    response.features.map((result: any) => {
                      result.attributes[this.districtColumnNameLang] = allResults[i].graphic.attributes[this.districtColumnNameLang];
                      result.attributes.chartName = "pieChartByCitizenship";
                      let graphic = {
                        graphic: result
                      };
                      this.sharedService.officialFigures = graphic;
                      this.sharedService.setPopByNationalsOriginal(result.attributes);
                      // this.sharedService.setPopByNationalsOriginal(allResults[i].graphic.attributes);
                    });
                  });
                  this.sharedService.getSumIndividual(fieldsOffcialGender, this.sharedService.mapConfig.dotDensityLayersOld[1], whereClause, "sum", null).then((response: any) => {
                    response.features.map((result: any) => {
                      const feature = result.attributes;
                      feature.moduleSource = 'old_population';
                      this.sharedService.setPopByGenderOriginal(feature);
                    });
                  });
                  //for summary
                  this.sharedService.createSummaryForPoi(this.mainView.map, feature.geometry);
                } else {
                  this.mainView.graphics.removeAll();
                  districtId = 999;
                  let fields: any = ['total_citizen', 'total_non_citizen', 'total_population'];
                  this.sharedService.getSumIndividual(fields, this.sharedService.mapConfig.dotDensityLayersOld[0], "1=1", "sum", null).then((response: any) => {
                    response.features.map((result: any) => {
                      result.attributes.DISTRICTNAMEENG = "Abu Dhabi";
                      result.attributes.chartName = "pieChartByCitizenship";
                      let graphic = {
                        graphic: result
                      };
                      this.sharedService.officialFigures = graphic;
                      this.sharedService.setPopByNationalsOriginal(result.attributes);
                    });
                  });
                }
              }
              foundDistrictOff = true;
              break;
            }
          }
          if(!foundDistrictOff) {
            this.mainView.graphics.removeAll();
            this.sharedService.dispalySummaryData(false);
            districtId = 999;
            let fields: any = ['total_citizen', 'total_non_citizen', 'total_population'];
            this.sharedService.getSumIndividual(fields, this.sharedService.mapConfig.dotDensityLayersOld[0], "1=1", "sum", null).then((response: any) => {
              response.features.map((result: any) => {
                result.attributes.DISTRICTNAMEENG = "Abu Dhabi";
                result.attributes.chartName = "pieChartByCitizenship";
                let graphic = {
                  graphic: result
                };
                this.sharedService.officialFigures = graphic;
                this.sharedService.setPopByNationalsOriginal(result.attributes);
              });
            });
          }
          this.sharedService.getSumIndividual(fieldsOffcialGender, this.sharedService.mapConfig.dotDensityLayersOld[1], "district_id="+districtId+"", "sum", null).then((response: any) => {
            response.features.map((result: any) => {
              const features = result.attributes;
              features.moduleSource = 'old_population';
              this.sharedService.setPopByGenderOriginal(features);
            });
          });
        }
        else if(this.sharedService.geoValue == 4) {
          // name of the districts layer Districts
          let foundDistrict = false;
          for(let i = 0; i < allResults.length; i++) {
            if(allResults[i].graphic.layer.title == 'Districts') {
              const feature = allResults[i].graphic;
              if (feature) {
                if(feature.geometry != null) {
                  this.sharedService.setDistrictForRegion(feature.attributes);
                  this.sharedService.SCAD_D_ID_SJ = feature.attributes.scad_district_id;
                  this.sharedService.setNationalityChartDivToDisplay(false);
                  // this.sharedService.displayDistrictName = "District: " + feature.attributes[this.districtColumnNameLangForHousehold];
                  // this.calculateResults(feature.geometry, this.polygonSketchLayer);
                  this.addPolygonToMap(feature.geometry.rings[0]);
                  // this.sharedService.createSummaryForPoi(this.mainView.map, feature.geometry);
                } else {
                  this.mainView.graphics.removeAll();
                  // this.sharedService.displayDistrictName = "District: All";
                  // this.calculateResults(null, this.polygonSketchLayer);
                }
              }
              foundDistrict = true;
              break;
            }
          }
          if(!foundDistrict) {
            this.mainView.graphics.removeAll();
            this.sharedService.displayDistrictName = "District: All";
            // this.calculateResults(null, this.polygonSketchLayer);
          }
        }
        else if(this.sharedService.geoValue == 5) {
          // name of the districts layer Districts
          let foundDistrict = false;
          for(let i = 0; i < allResults.length; i++) {
            if(allResults[i].graphic.layer.title == 'Districts') {
              const feature = allResults[i].graphic;
              if (feature) {
                if(feature.geometry != null) {
                  this.sharedService.setRealestateDistrict(feature.attributes);
                  // this.sharedService.SCAD_D_ID_SJ = feature.attributes.scad_district_id;
                  // this.sharedService.setNationalityChartDivToDisplay(false);
                  // this.sharedService.displayDistrictName = "District: " + feature.attributes[this.districtColumnNameLangForHousehold];
                  // this.calculateResults(feature.geometry, this.polygonSketchLayer);
                  this.addPolygonToMap(feature.geometry.rings[0]);
                  this.sharedService.createSummaryForPoi(this.mainView.map, feature.geometry);
                } else {
                  this.mainView.graphics.removeAll();
                  // this.sharedService.displayDistrictName = "District: All";
                  // this.calculateResults(null, this.polygonSketchLayer);
                }
              }
              foundDistrict = true;
              break;
            }
          }
          if(!foundDistrict) {
            this.sharedService.setRealestateDistrict({isNotDistrict: true});
            this.mainView.graphics.removeAll();
            this.sharedService.displayDistrictName = "District: All";
            // this.calculateResults(null, this.polygonSketchLayer);
          }
        }
        else if(this.sharedService.geoValue == 6) {
          // name of the districts layer Districts
          let foundDistrict = false;
          for(let i = 0; i < allResults.length; i++) {
            if(allResults[i].graphic.layer.title == 'Districts') {
              const feature = allResults[i].graphic;
              if (feature) {
                if(feature.geometry != null) {
                  this.sharedService.setRealestateCensusDistrict(feature.attributes);
                  this.sharedService.googleAnalyticsMap("3", feature.attributes[this.districtColumnNameLangForHousehold]);
                  // this.sharedService.SCAD_D_ID_SJ = feature.attributes.scad_district_id;
                  // this.sharedService.setNationalityChartDivToDisplay(false);
                  // this.sharedService.displayDistrictName = "District: " + feature.attributes[this.districtColumnNameLangForHousehold];
                  // this.calculateResults(feature.geometry, this.polygonSketchLayer);
                  this.addPolygonToMap(feature.geometry.rings[0]);
                  // this.sharedService.createSummaryForPoi(this.mainView.map, feature.geometry);
                } else {
                  this.mainView.graphics.removeAll();
                  this.sharedService.googleAnalyticsMap("3", "Abu Dhabi");
                  // this.sharedService.displayDistrictName = "District: All";
                  // this.calculateResults(null, this.polygonSketchLayer);
                }
              }
              foundDistrict = true;
              break;
            }
          }
          if(!foundDistrict) {
            this.sharedService.setRealestateCensusDistrict({isNotDistrict: true});
            this.mainView.graphics.removeAll();
            this.sharedService.displayDistrictName = "District: All";
            this.sharedService.googleAnalyticsMap("3", "Abu Dhabi");
            // this.calculateResults(null, this.polygonSketchLayer);
          }
        }
      });
    });

  }

  createGroupLayers() {
    const otherGroupLayer = new GroupLayer({
      title: "Point of Interest",
      // visible: true,
      // visibilityMode: "exclusive",
      layers: [],
      // opacity: 0.75
    });
    // this.mainMap.add(otherGroupLayer);
    this.mainView.map.layers.add(otherGroupLayer);
    this.sharedService.mapConfig.otherLayers.map((obj: any) => {
      this.createFeatureLayerToMapOthers(obj.url, 0.9, obj.name, false, obj.icon, otherGroupLayer);
    });
  }

  getCurrentUrl(): string {
    let url = this.router.url; // Gets the current router URL
    return url;
  }

  changeMapLayersForPopulation() {
    const nonBasemapLayers = this.mainView.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      this.mainView.map.remove(layer);
    });
    this.mainView.graphics.removeAll();
    //this.createSearchWidget('top-left');
    // this.createFeatureLayerToMap(this.sharedService.mapConfig.layers[0], 0.3, 'hosted_districts', true);
    this.createFeatureLayerToMapDistricts(this.sharedService.mapConfig.layers[1], 0.9, 'Districts', true);
    this.createSketchFeatureLayer(this.sharedService.mapConfig.layers[0], 1, 'Population Estimate', true);
    this.zoomToTheMap(54.410943, 24.476687, 11);
    this.createGraphicsLayerToMap();
    this.createSelectionWidget('top-right', 'add');
    this.createSearchWidget('top-left', 'add');
    this.createSearchWidgetForDotDensity('top-left', 'remove');
    this.createGroupLayers();
    this.clearLayerList();
    this.sourceAndDate.forEach((res: any) => {
      if(res.DOMAIN_NAME_KEY == 'HOUSE_HOLD') {
        this.sharedService.dataUpdatedDate(res);
      }
    });
    this.showForOfficialPop = '';
    this.sharedService.dispalySummaryData(false);
    this.createLayerListWidget('bottom-right', 'add');
  }

  changeMapLayersForDotDensity() {
    const nonBasemapLayers = this.mainView.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      this.mainView.map.remove(layer);
    });
    this.mainView.graphics.removeAll();
    //this.createSearchWidgetForDotDensity('top-left');
    // this.zoomToTheMap(54.510943, 24.376687, 9);
    this.zoomToTheMap(54.410943, 24.476687, 11);
    this.createGraphicsLayerToMap();
    this.createDotdensityFeatureLayer();
    this.createDotdensityFeatureLayerSeaRemoved();
    // this.createFeatureLayerToMapLabour(this.sharedService.mapConfig.layers[4], 1, 'LabourLocations', true);
    // this.createFeatureLayerToMapXY(this.sharedService.mapConfig.layers[2], 1, 'Population XY', true);
    this.createSearchWidget('top-left', 'remove');
    this.createSearchWidgetForDotDensity('top-left', 'add');
    this.createSelectionWidget('top-right', 'remove');
    this.createGroupLayers();
    this.clearLayerList();
    this.sourceAndDate.forEach((res: any) => {
      if(res.DOMAIN_NAME_KEY == 'POP_OFFICIAL_CENSUS') {
        this.sharedService.dataUpdatedDate(res);
      }
    });
    this.sharedService.homeClicked += 1;
    this.showForOfficialPop = '1 Dot = 100 People';
    this.createLayerListWidget('bottom-right', 'add');
  }
  changeMapLayersForDotDensityOld() {
    const nonBasemapLayers = this.mainView.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      this.mainView.map.remove(layer);
    });
    this.mainView.graphics.removeAll();
    this.zoomToTheMap(54.410943, 24.476687, 11);
    this.createGraphicsLayerToMap();
    this.createDotdensityFeatureLayerOld();
    this.createDotdensityFeatureLayerSeaRemovedOld();
    this.createSearchWidget('top-left', 'remove');
    this.createSearchWidgetForDotDensity('top-left', 'remove');
    this.createSelectionWidget('top-right', 'remove');
    this.createGroupLayers();
    this.clearLayerList();
    this.sourceAndDate.forEach((res: any) => {
      if(res.DOMAIN_NAME_KEY == 'POP_OFFICIAL_OLD') {
        this.sharedService.dataUpdatedDate(res);
      }
    });
    this.sharedService.homeClicked += 1;
    this.showForOfficialPop = '1 Dot = 100 People';
    this.createLayerListWidget('bottom-right', 'add');
  }

  changeMapLayersForPopByRegion() {
    const nonBasemapLayers = this.mainView.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      this.mainView.map.remove(layer);
    });
    this.mainView.graphics.removeAll();
    this.zoomToTheMap(54.410943, 24.476687, 11);
    this.createFeatureLayerToMapDistricts(this.sharedService.mapConfig.layers[1], 0.9, 'Districts', true);
    this.createGraphicsLayerToMap();
    this.createSearchWidget('top-left', 'remove');
    this.createSearchWidgetForDotDensity('top-left', 'remove');
    this.createSelectionWidget('top-right', 'remove');
    this.createGroupLayers();
    this.clearLayerList();
    this.sourceAndDate.forEach((res: any) => {
      if(res.DOMAIN_NAME_KEY == 'POP_REGION') {
        this.sharedService.dataUpdatedDate(res);
      }
    });
    this.createLayerListWidget('bottom-right', 'add');
  }
  changeMapLayersForRealEastateFlatTransactions() {
    const nonBasemapLayers = this.mainView.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      this.mainView.map.remove(layer);
    });
    this.mainView.graphics.removeAll();
    this.zoomToTheMap(54.410943, 24.476687, 11);
    this.createFeatureLayerToMapDistricts(this.sharedService.mapConfig.realEstateFlatTrans[0], 0.9, 'Districts', true);
    this.createGraphicsLayerToMap();
    this.createSearchWidget('top-left', 'remove');
    this.createSearchWidgetForDotDensity('top-left', 'remove');
    this.createSelectionWidget('top-right', 'remove');
    this.createGroupLayers();
    this.clearLayerList();
    // this.sourceAndDate.forEach((res: any) => {
    //   if(res.domain_name_key == 'POP_REGION') {
    //     this.sharedService.dataUpdatedDate(res);
    //   }
    // });
    let res = {
      definition_ar : "TBC",
      definition_eng : "TBC",
      domain_name_ar : "TBC",
      DOMAIN_NAME_ENG : "Real estate - Flat Transactions",
      DOMAIN_NAME_KEY : "POP_REGION",
      objectid : 5,
      obs_dt : "20220601",
      source_ar : "TBC",
      SOURCE_ENG : "TBC"
    }
    this.sharedService.dataUpdatedDate(res);
    this.createLayerListWidget('bottom-right', 'add');
  }

  changeMapLayersForCensusRealEstate() {
    const nonBasemapLayers = this.mainView.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      this.mainView.map.remove(layer);
    });
    this.mainView.graphics.removeAll();
    this.zoomToTheMap(54.410943, 24.476687, 11);
    this.createFeatureLayerToMapDistricts(this.sharedService.mapConfig.censusrealEstate[0], 0.9, 'Districts', true);
    this.createGraphicsLayerToMap();
    this.createSearchWidget('top-left', 'remove');
    this.createSearchWidgetForDotDensity('top-left', 'remove');
    this.createSelectionWidget('top-right', 'remove');
    this.createDotdensityFeatureLayerForRealestateCensus();
    this.createGroupLayers();
    this.clearLayerList();
    this.sourceAndDate.forEach((res: any) => {
      if(res.DOMAIN_NAME_KEY == 'REAL_ESTATE_CENSUS') {
        this.sharedService.dataUpdatedDate(res);
      }
    });
    this.createLayerListWidget('bottom-right', 'add');
  }

  createDotdensityFeatureLayer() {
    // this.mainView.popup.watch("visible", (visible) => {
    //   if (visible){
    //     view.popup.visible = condition_goes_here;
    //   }
    // });
    const url = this.sharedService.mapConfig.dotDensityLayers[2];
    const dotDensityRenderer = new DotDensityRenderer({
      dotValue: 100,
      dotSize: 1,
      outline: {
        color: [ 255,255,255, 1 ],
        width: 0.5
      },
      backgroundColor: [255, 255, 255, 0],
      // referenceScale: 577790,
      legendOptions: {
        unit: "People"
      }
    });

    dotDensityRenderer.attributes = [
      // @ts-ignore
      { field: "TOTAL_CITIZEN", color: this.sharedService.colors[0].color, label: this.sharedService.colors[0].name },
      // @ts-ignore
      { field: "TOTAL_NON_CITIZEN", color: this.sharedService.colors[1].color, label: this.sharedService.colors[1].name }
    ];

    const layer = new FeatureLayer({
      url: url,
      title: "Official Population Overview",
      popupEnabled: false,
      outFields: ["*"],
      popupTemplate: {
        title: "{DISTRICTNAMEENG}",
        outFields: ["*"],
        content: (info: any) => {
        },
      },
      // renderer: dotDensityRenderer
    });

    const fillSymbol = new SimpleFillSymbol({
      color: [137, 148, 153, 0.2],
      outline: {
        color: [137, 148, 153],
        width: 1,
      },
    });

    layer.renderer = {
      type: 'simple',
      //@ts-ignore
      symbol: fillSymbol,
    };

    // this.mainView.on('click', (event: any) => {
    //   this.mainView.hitTest(event).then((response: any) => {
    //     let allResults = response.results;
    //     allResults.forEach((feature: any) => {
    //       if(feature.graphic.layer.title == 'Official Population Overview') {
    //
    //       } else if(feature.graphic.layer.title == 'Districts') {
    //
    //       }
    //     });
    //   });
    // });
    this.mainView.map.layers.add(layer);
  }

  createDotdensityFeatureLayerOld() {
    const url = this.sharedService.mapConfig.dotDensityLayersOld[2];
    const dotDensityRenderer = new DotDensityRenderer({
      dotValue: 100,
      dotSize: 1,
      outline: {
        color: [ 255,255,255, 1 ],
        width: 0.5
      },
      backgroundColor: [255, 255, 255, 0],
      // referenceScale: 577790,
      legendOptions: {
        unit: "People"
      }
    });

    dotDensityRenderer.attributes = [
      // @ts-ignore
      { field: "TOTAL_CITIZEN", color: this.sharedService.colors[0].color, label: this.sharedService.colors[0].name },
      // @ts-ignore
      { field: "TOTAL_NON_CITIZEN", color: this.sharedService.colors[1].color, label: this.sharedService.colors[1].name }
    ];

    const layer = new FeatureLayer({
      url: url,
      title: "Official Population Overview",
      popupEnabled: false,
      outFields: ["*"],
      popupTemplate: {
        title: "{DISTRICTNAMEENG}",
        outFields: ["*"],
        content: (info: any) => {
        },
      },
      // renderer: dotDensityRenderer
    });

    const fillSymbol = new SimpleFillSymbol({
      color: [137, 148, 153, 0.2],
      outline: {
        color: [137, 148, 153],
        width: 1,
      },
    });

    layer.renderer = {
      type: 'simple',
      //@ts-ignore
      symbol: fillSymbol,
    };

    // this.mainView.on('click', (event: any) => {
    //   this.mainView.hitTest(event).then((response: any) => {
    //     let allResults = response.results;
    //     allResults.forEach((feature: any) => {
    //       if(feature.graphic.layer.title == 'Official Population Overview') {
    //
    //       } else if(feature.graphic.layer.title == 'Districts') {
    //
    //       }
    //     });
    //   });
    // });
    this.mainView.map.layers.add(layer);
  }
  createDotdensityFeatureLayerSeaRemoved() {
    const url = this.sharedService.mapConfig.dotDensityLayers[0];
    const dotDensityRenderer = new DotDensityRenderer({
      dotValue: 100,
      dotSize: 2,
      // @ts-ignore
      dotShape: "circle",
      outline: {
        color: [ 255,255,255, 0 ],
        width: 0.5
      },
      backgroundColor: [255, 255, 255, 0],
      legendOptions: {
        unit: "People"
      }
    });

    dotDensityRenderer.attributes = [
      // @ts-ignore
      // { field: "TOTAL_CITIZEN", color: this.sharedService.colors[0].color, label: this.sharedService.colors[0].name },
      { field: "TOTAL_CITIZEN", color: this.sharedService.colors[0].color, label: 'UAE' },
      // @ts-ignore
      // { field: "TOTAL_NON_CITIZEN", color: this.sharedService.colors[1].color, label: this.sharedService.colors[1].name }
      { field: "TOTAL_NON_CITIZEN", color: this.sharedService.colors[1].color, label: 'NON_UAE' }
    ];

    const layer = new FeatureLayer({
      url: url,
      title: "DotDensitySeaRemoved",
      popupEnabled: false,
      outFields: ["*"],
      popupTemplate: {
        title: "{DISTRICTNAMEENG}",
        outFields: ["*"],
        content: (info: any) => {
        },
      },
      renderer: dotDensityRenderer
    });
    this.sharedService.dotDensityRendererByService = layer.renderer;
    this.mainView.map.layers.add(layer);
  }

  createDotdensityFeatureLayerSeaRemovedOld() {
    const url = this.sharedService.mapConfig.dotDensityLayersOld[0];
    const dotDensityRenderer = new DotDensityRenderer({
      dotValue: 100,
      dotSize: 2,
      // @ts-ignore
      dotShape: "circle",
      outline: {
        color: [ 255,255,255, 0 ],
        width: 0.5
      },
      backgroundColor: [255, 255, 255, 0],
      legendOptions: {
        unit: "People"
      }
    });

    dotDensityRenderer.attributes = [
      // @ts-ignore
      // { field: "TOTAL_CITIZEN", color: this.sharedService.colors[0].color, label: this.sharedService.colors[0].name },
      { field: "TOTAL_CITIZEN", color: this.sharedService.colors[0].color, label: 'UAE' },
      // @ts-ignore
      // { field: "TOTAL_NON_CITIZEN", color: this.sharedService.colors[1].color, label: this.sharedService.colors[1].name }
      { field: "TOTAL_NON_CITIZEN", color: this.sharedService.colors[1].color, label: 'NON_UAE' }
    ];

    const layer = new FeatureLayer({
      url: url,
      title: "DotDensitySeaRemoved",
      popupEnabled: false,
      outFields: ["*"],
      popupTemplate: {
        title: "{DISTRICTNAMEENG}",
        outFields: ["*"],
        content: (info: any) => {
        },
      },
      renderer: dotDensityRenderer
    });
    this.sharedService.dotDensityRendererByService = layer.renderer;
    this.mainView.map.layers.add(layer);
  }

  createDotdensityFeatureLayerForRealestateCensus() {
    const url = this.sharedService.mapConfig.censusrealEstate[8];
    const dotDensityRenderer = new DotDensityRenderer({
      dotValue: 50,
      dotSize: 3,
      // @ts-ignore
      dotShape: "square",
      outline: {
        color: [ 255,255,255, 0 ],
        width: 0.5
      },
      backgroundColor: [255, 255, 255, 0],
      legendOptions: {
        unit: "People"
      }
    });

    dotDensityRenderer.attributes = [
      // @ts-ignore
      // { field: "UNIT_NUMBER", color: this.sharedService.colors[0].color, label: 'UNIT_NUMBER' },
      // @ts-ignore
      { field: "BUILDING_NUMBER", color: 'gray', label: 'BUILDING_NUMBER' }
    ];

    const layer = new FeatureLayer({
      url: url,
      title: "Dot Density Buildings",
      popupEnabled: false,
      outFields: ["*"],
      popupTemplate: {
        title: "{DISTRICTNAMEENG}",
        outFields: ["*"],
        content: (info: any) => {
        },
      },
      renderer: dotDensityRenderer
    });
    this.sharedService.dotDensityRendererByService = layer.renderer;
    this.mainView.map.layers.add(layer);
  }

  selectFeatures(geometry: any) {

    if (this.featureLayerView) {
      const query = {
        geometry,
        outFields: ['*']
      };

      this.featureLayerView
        .queryFeatures(query)
        .then((results: any) => {
          if (results.features.length === 0) {
            // clearSelection();
          } else {
            // @ts-ignore
            this.calculate(results.features);
          }
        })
        .catch((error: any) => {
          //
        });
    }
  }

  calculate(data: any) {
    const allFeatures: any = [];
    data.forEach((item: any) => {
      allFeatures.push(item.attributes.objectid);
    });

    this.featureLayerView.featureEffect = {
      filter: {
        objectIds: allFeatures
      },
      excludedEffect: 'blur(5px) grayscale(90%) opacity(40%)'
    };
  }

  calculateResultsForDrillDown(queryGeometry: any, layer: any, whereClause: any, currentName: any) {
    const query = new Query();
    query.geometry = queryGeometry;
    query.where = whereClause;
    query.returnGeometry = false;
    query.outStatistics = [

      new StatisticDefinition({
        statisticType: 'sum',
        onStatisticField: 'male',
        outStatisticFieldName: 'total_male'
      }),
      new StatisticDefinition({
        statisticType: 'sum',
        onStatisticField: 'female',
        outStatisticFieldName: 'total_female'
      }),
      new StatisticDefinition({
        statisticType: 'sum',
        onStatisticField: 'total_population',
        outStatisticFieldName: 'total_population'
      })
    ];
    layer.queryFeatures(query).then((results: any) => {
      this.sharedService.highChartsArray.forEach((charts: any) => {
        if(charts.name == 'DrillDownChart') {
          let drilldownSeries: any = [];
          let fields: any = ['male', 'female'];
          let colors: any = ["#297DE3", "#FF00CC"];
          let obj = {
            id: currentName,
            name: currentName,
            data: [
              {
                name: fields[0],
                y: results.features[0].attributes.total_male,
                color: colors[0]
              },
              {
                name: fields[1],
                y: results.features[0].attributes.total_female,
                color: colors[1]
              }
            ]
          }
          drilldownSeries.push(obj);
          this.drillDownSeries.push(drilldownSeries[0]);
          // charts.chartOptions.drilldown.series = [];
          // charts.chartOptions.drilldown.series = drilldownSeries[0];
          // charts.mainChart.update(charts.chartOptions);
        }
      });
    });
  }

  calculateResults(queryGeometry: any, layer: any) {
    let colors: any = ["#297DE3", "#FF00CC"];
    this.drillDownSeries = [];
    let dataToPushForGender: any = [];
    let dataToPushForNationals: any = [];
    let o: any = [];
    let seriesDataForDrillDown: any = [];
    let fields: any = ['uae', 'non_uae', 'male', 'female', 'total_population'];
    this.sharedService.getSumIndividual(fields, this.sharedService.mapConfig.layers[0], "1=1", "sum", queryGeometry).then((response: any) => {
      const passAttr = response.features[0].attributes;
      passAttr.chartName = "popByGender";
      this.sharedService.setPopByNationalsOriginal(passAttr);

      response.features.map((result: any) => {
        let dataToPushForMainFigures: any = [];
        const entries = Object.entries(result.attributes);
        entries.forEach(([key, value]) => {
          if(key == 'male' || key == 'female') {

          } else if(key == 'uae' || key == 'non_uae') {
            let obj3 = {
              type: this.sharedService.allCapitalize(key),
              total: value
            };
            dataToPushForMainFigures.push(obj3);
            // @ts-ignore
            this.calculateResultsForDrillDown(queryGeometry, layer, "premise_citizenship = '"+key.toUpperCase()+"'", key.toUpperCase());
          } else if(key == 'total_population') {
            let obj = {
              type: this.sharedService.allCapitalize(key),
              total: value
            };
            dataToPushForMainFigures.push(obj);
          }
          const sortedList = dataToPushForMainFigures.reverse();
          this.sharedService.mainFigures = sortedList;
          // this.sharedService.mainFigures = dataToPushForMainFigures;
        });
      });
    });
  }

  errorCallback(error: any) {
    //
  }

  changeMapLayersForJobSeekers() {
    const nonBasemapLayers = this.mainView.map.layers.filter((layer: any) => !layer.basemap);
    nonBasemapLayers.forEach((layer: any) => {
      this.mainView.map.remove(layer);
    });
    this.mainView.graphics.removeAll();
    this.createFeatureLayerToMap(this.sharedService.mapConfig.jobSeekerLayers[0], 1, 'mismatch', false);
    this.createFeatureLayerToMap(this.sharedService.mapConfig.jobSeekerLayers[1], 1, 'jobseeker1', false);
    this.createFeatureLayerToMap(this.sharedService.mapConfig.jobSeekerLayers[2], 0.9, 'jobseeker2', true); // district wise classification
    this.createFeatureLayerToMapAsHeatMap(this.sharedService.mapConfig.jobSeekerLayers[3], 0.9, 'jobvacancies', true);
    this.zoomToTheMap(54.210943, 24.076687, 7);
    this.createGraphicsLayerToMap();
    this.createSelectionWidget('top-right', 'remove');
    this.createSearchWidget('top-left', 'remove');
    this.createSearchWidgetForDotDensity('top-left', 'remove');
    this.createGroupLayers();
    this.clearLayerList();
    this.sourceAndDate.forEach((res: any) => {
      if(res.DOMAIN_NAME_KEY == 'JOB_SEEKERS') {
        this.sharedService.dataUpdatedDate(res);
      }
    });
    this.showForOfficialPop = '';
    this.sharedService.dispalySummaryData(false);
    this.createLayerListWidget('bottom-right', 'add');
  }

  zoomToTheMap(x: any, y: any, zoom: any) {
    this.mainView.goTo({
      center: [x, y],
      zoom: zoom,
    });
  }

  createVectorBasemap(url: any, theme: string) {
    let baseUrl = url;
    if(theme === 'light') {
      baseUrl = this.sharedService.mapConfig.basemap[1];
    } else {
      baseUrl = this.sharedService.mapConfig.basemapDark[0];
    }
    const vectorTileLayer = new VectorTileLayer({
      url: baseUrl
    });
    const customBasemap = new Basemap({
      baseLayers: [vectorTileLayer]
    });
    this.mainMap.basemap = customBasemap;
  }

  createSketchFeatureLayer(url: string, opacity: any, layerName: any, visible: any) {

    let renderer = new UniqueValueRenderer({
      field: "premise_citizenship",
      uniqueValueInfos: [{
        value: "NON_UAE",
        symbol: new SimpleMarkerSymbol({
          color: this.sharedService.colors[1].color,
          size: "3px",
          outline: {
            width: 0,
            color: "black"
          }
        })
      }, {
        value: "UAE",
        symbol: new SimpleMarkerSymbol({
          color: this.sharedService.colors[0].color,
          size: "3px",
          outline: {
            width: 0,
            color: "black"
          }
        })
      }]
    });

    this.polygonSketchLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName,
      renderer: renderer
    });

    var query = this.polygonSketchLayer.createQuery();
    query.where = "1=1";
    query.num = 1;
    this.polygonSketchLayer.queryFeatures(query).then((response: any) =>{
      if (response.features.length > 0) {
        var firstFeature = response.features[0];

      } else {
        console.error("No features found.");
      }
    });

    this.polygonSketchLayer
      .when(() => {
        this.mainView.whenLayerView(this.polygonSketchLayer).then((layerView: any) => {
          this.featureLayerView = layerView;
        });
      })
      .catch(this.errorCallback);

    this.polygonDrawGraphicsLayer = new GraphicsLayer();
    this.mainView.map.layers.add(this.polygonDrawGraphicsLayer);

    // selection criteria
    this.sketchViewModelMain = new SketchViewModel({
      view: this.mainView,
      layer: this.polygonDrawGraphicsLayer
    });
    this.sketchViewModelMain.on('create', async (event: any) => {
      if (event.state === 'complete') {
        const geometries = this.polygonDrawGraphicsLayer.graphics.map( (
          graphic: any
        ) => {
          return graphic.geometry;
        });
        const queryGeometry = await geometryEngine.union(
          geometries.toArray()
        );
        this.selectFeatures(queryGeometry);
        this.calculateResults(queryGeometry, this.polygonSketchLayer);
      }
    });

    this.mainView.map.layers.add(this.polygonSketchLayer);
    let obj = {
      name: layerName,
      layer: this.polygonSketchLayer
    }
    this.sharedService.allAddedLayers.push(obj);
  }

  createFeatureLayerToMapDistricts(url: string, opacity: any, layerName: any, visible: any) {
    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName
    });

    const fillSymbol = new SimpleFillSymbol({
      color: [137, 148, 153, 0.2],
      outline: {
        color: [137, 148, 153],
        width: 1,
      },
    });

    featureLayer.renderer = {
      type: 'simple',
      //@ts-ignore
      symbol: fillSymbol,
    };

    // Enable clicking on each feature
    // this.mainView.on('click', (event: any) => {
    //   this.mainView.hitTest(event).then((response: any) => {
    //     // name of the districts layer Districts
    //     let allResults = response.results;
    //     let foundDistrict = false;
    //     for(let i = 0; i < allResults.length; i++) {
    //       if(allResults[i].graphic.layer.title == 'Districts') {
    //         const feature = allResults[i].graphic;
    //         if (feature) {
    //           if(feature.geometry != null) {
    //             this.sharedService.displayDistrictName = "District: " + feature.attributes.DISTRICT_ENG;
    //             this.calculateResults(feature.geometry, this.polygonSketchLayer);
    //             this.addPolygonToMap(feature.geometry.rings[0]);
    //           } else {
    //             this.mainView.graphics.removeAll();
    //             this.sharedService.displayDistrictName = "District: All";
    //             this.calculateResults(null, this.polygonSketchLayer);
    //           }
    //         }
    //         foundDistrict = true;
    //         break;
    //       }
    //     }
    //     if(!foundDistrict) {
    //       this.mainView.graphics.removeAll();
    //       this.sharedService.displayDistrictName = "District: All";
    //       this.calculateResults(null, this.polygonSketchLayer);
    //     }
    //   });
    // });

    this.mainView.map.layers.add(featureLayer);

    let obj = {
      name: layerName,
      layer: featureLayer
    }
    this.sharedService.allAddedLayers.push(obj);
  }

  addPolygonToMapDirect(featureRings: any) {
    this.mainView.graphics.removeAll();
    const polygon = new Polygon({
      rings: featureRings
    });
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: {
        //@ts-ignore
        type: 'simple-fill',
        color: [0, 100, 100, 0], // Set the fill color and transparency (red with 50% transparency)
        outline: {
          color: [0, 255, 255],
          width: 3
        }
      }
    });
    this.mainView.graphics.add(polygonGraphic);
    this.mainView.goTo(polygon.extent);
  }

  addPolygonToMap(featureRings: any) {
    this.mainView.graphics.removeAll();
    const convertedCoordinates = featureRings.map((coords: any) => {
      const [x, y] = coords;
      const point = webMercatorUtils.xyToLngLat(x, y);
      return [point[0], point[1]];
    });
    const polygon = new Polygon({
      rings: [convertedCoordinates]
    });
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: {
        //@ts-ignore
        type: 'simple-fill',
        color: [0, 100, 100, 0], // Set the fill color and transparency (red with 50% transparency)
        outline: {
          color: [0, 255, 255],
          width: 3
        }
      }
    });
    this.mainView.graphics.add(polygonGraphic);
    this.mainView.goTo(polygon.extent);
  }

  addPolygonToMapForHousehold(featureRings: any) {
    this.mainView.graphics.removeAll();
    const convertedCoordinates = featureRings.map((coords: any) => {
      const [x, y] = coords;
      const point = webMercatorUtils.xyToLngLat(x, y);
      return [point[0], point[1]];
    });
    const polygon = new Polygon({
      rings: [convertedCoordinates]
    });
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: {
        //@ts-ignore
        type: 'simple-fill',
        color: [0, 100, 100, 0], // Set the fill color and transparency (red with 50% transparency)
        outline: {
          color: [0, 255, 255],
          width: 3
        }
      }
    });
    this.mainView.graphics.add(polygonGraphic);
    this.mainView.goTo(polygon.extent);
  }

  createFeatureLayerToMap(url: string, opacity: any, layerName: any, visible: any) {
    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName
    });
    this.mainView.map.layers.add(featureLayer);

    let obj = {
      name: layerName,
      layer: featureLayer
    }
    this.sharedService.allAddedLayers.push(obj);
  }

  createFeatureLayerToMapXY(url: string, opacity: any, layerName: any, visible: any) {

    let renderer = new UniqueValueRenderer({
      field: "premise_citizenship",
      uniqueValueInfos: [{
        value: "NON_UAE",
        symbol: new SimpleMarkerSymbol({
          color: this.sharedService.colors[1].color,
          size: "4px",
          outline: {
            width: 0,
            color: "black"
          }
        })
      }, {
        value: "UAE",
        symbol: new SimpleMarkerSymbol({
          color: this.sharedService.colors[0].color,
          size: "4px",
          outline: {
            width: 0,
            color: "black"
          }
        })
      }]
    });

    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName,
      renderer: renderer
    });
    this.mainView.map.layers.add(featureLayer);
  }

  createFeatureLayerToMapLabour(url: string, opacity: any, layerName: any, visible: any) {

    let blueFillSymbol = new SimpleFillSymbol({
      color: this.sharedService.colors[1].color,
      outline: {
        width: 0,
        color: "black"
      }
    });
    let redMarkerSymbol = new SimpleMarkerSymbol({
      color: this.sharedService.colors[1].color,
      size: 4,
      outline: {
        width: 0,
        color: "black"
      }
    });
    let renderer = new SimpleRenderer({
      symbol: redMarkerSymbol
    });

    // let renderer = new UniqueValueRenderer({
    //   field: "premise_citizenship",
    //   uniqueValueInfos: [{
    //     value: "NON_UAE",
    //     symbol: new SimpleMarkerSymbol({
    //       color: this.sharedService.colors[1].color,
    //       size: "4px",
    //       outline: {
    //         width: 0,
    //         color: "black"
    //       }
    //     })
    //   }, {
    //     value: "UAE",
    //     symbol: new SimpleMarkerSymbol({
    //       color: this.sharedService.colors[0].color,
    //       size: "4px",
    //       outline: {
    //         width: 0,
    //         color: "black"
    //       }
    //     })
    //   }]
    // });

    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName,
      renderer: renderer
    });
    this.mainView.map.layers.add(featureLayer);
  }

  createFeatureLayerToMapOthers(url: string, opacity: any, layerName: any, visible: any, icon: any, groupLayer: any) {

    const iconSymbol = new PictureMarkerSymbol({
      url: icon,
      width: "12px",
      height: "12px",
    });

    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName,
      renderer: {
        // @ts-ignore
        type: 'simple',
        symbol: iconSymbol,
      },
    });
    groupLayer.layers.push(featureLayer);
    // this.mainView.map.layers.add(featureLayer);

    // let obj = {
    //   name: layerName,
    //   layer: featureLayer
    // }
    // this.sharedService.allAddedLayers.push(obj);
  }

  createFeatureLayerToMapAsHeatMap(url: string, opacity: any, layerName: any, visible: any) {
    const colors = [
      { color: "rgba(255, 255, 0, 1)",  ratio: 0 },
      { color: "rgba(244, 204, 0, 1)", ratio: 0.082 },
      {color: "rgba(233, 153, 0, 1)", ratio: 0.16},
      {color: "rgba(222, 102, 0, 1)", ratio: 0.24},
      {color: "rgba(211, 51, 0, 1)", ratio: 0.33},
      {color: "rgba(200, 0, 0, 1)", ratio:  0.41},
      {color: "rgba(192, 24, 42, 1)", ratio: 0.49},
      {color: "rgba(184, 48, 85, 1)", ratio: 0.57},
      {color: "rgba(175, 73, 128, 1)", ratio: 0.66},
      {color: "rgba(167, 97, 170, 1)", ratio: 0.74},
      {color: "rgba(156, 129, 132, 1)", ratio: 0.82},
      {color: "rgba(144, 161, 190, 1)", ratio: 0.90},
      {color: "rgba(133, 193, 200, 1)", ratio: 0.99}
    ];
    const renderer = {
      // type: "heatmap",
      // field: "crime_count",
      // colorStops: [
      //   { ratio: 0, color: "rgba(255, 255, 255, 0)" },
      //   { ratio: 0.2, color: "rgba(255, 255, 255, 1)" },
      //   { ratio: 0.5, color: "rgba(255, 140, 0, 1)" },
      //   { ratio: 0.8, color: "rgba(255, 140, 0, 1)" },
      //   { ratio: 1, color: "rgba(255, 0, 0, 1)" }
      // ],
      // minDensity: 0,
      // maxDensity: 500,
      // radius: 10
      type: "heatmap",
      colorStops: [
        { color: "rgba(133, 193, 200, 0)", ratio: 0 },
        { color: "#90A1BE", ratio: 0.083 },
        { color: "#9C8184", ratio: 0.166 },
        { color: "#A761AA", ratio: 0.249 },
        { color: "#AF4980", ratio: 0.332 },
        { color: "#AF4980", ratio: 0.415 },
        { color: "#B83055", ratio: 0.498 },
        { color: "#C0182A", ratio: 0.581 },
        { color: "#C80000", ratio: 0.664 },
        { color: "#D33300", ratio: 0.747 },
        { color: "#DE6600", ratio: 0.83 },
        { color: "#E99900", ratio: 0.913 },
        { color: "#FFFF00", ratio: 0.95 }
      ],
      radius: 10,
      maxDensity: 1,
      minDensity: 0,
      blurRadius: 0,
    };
    const layer = new FeatureLayer({
      url: url,
      title: layerName,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      // @ts-ignore
      renderer: renderer
    });
    this.mainView.map.layers.add(layer);
    let obj = {
      name: layerName,
      layer: layer
    }
    this.sharedService.allAddedLayers.push(obj);
  }

  createGraphicsLayerToMap() {
    this.graphicsLayer = new GraphicsLayer({
      title: 'Shapes'
    });
    this.mainView.map.add(this.graphicsLayer);
  }

  createMeasurementWidget(positionMeasurement: any, positionButtons: any) {
    let activeTool: 'line' | 'area' = 'line';
    let measurementWidget = new Measurement({
      view: this.mainView
    });
    this.mainView.ui.add(measurementWidget, positionMeasurement);
    var measureToolDiv = document.createElement('div');
    measureToolDiv.className = "tool-div";
    var lineMeasureDiv = document.createElement('div');
    lineMeasureDiv.innerHTML = `<calcite-icon icon="line" text-label="360 degree view" scale="l" title="${this._translate.instant('Distance Measurement')}"></calcite-icon>`;
    lineMeasureDiv.className = "esri-widget--button esri-widget esri-interactive";
    lineMeasureDiv.addEventListener('click', (evt) => {
      deleteMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      polygonMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      lineMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive active';
      polygonMeasureDiv.removeAttribute("id");
      deleteMeasureDiv.removeAttribute('id');
      lineMeasureDiv.setAttribute('id', 'tool-div-measurement-line');
      activeTool = 'line';
      measurementWidget.activeTool = 'distance';
    });
    measureToolDiv.appendChild(lineMeasureDiv);
    var polygonMeasureDiv = document.createElement('div');
    polygonMeasureDiv.innerHTML = `<calcite-icon icon="measure-area" text-label="360 degree view" scale="l" title="${this._translate.instant('Area Measurement')}"></calcite-icon>`;
    polygonMeasureDiv.className = "esri-widget--button esri-widget esri-interactive";
    polygonMeasureDiv.addEventListener('click', (evt) => {
      deleteMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      lineMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      polygonMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive active';
      lineMeasureDiv.removeAttribute("id");
      deleteMeasureDiv.removeAttribute("id");
      polygonMeasureDiv.setAttribute('id', 'tool-div-measurement-polygon');
      activeTool = 'area';
      measurementWidget.activeTool = 'area';
    });
    measureToolDiv.appendChild(polygonMeasureDiv);
    var deleteMeasureDiv = document.createElement('div');
    deleteMeasureDiv.innerHTML = `<calcite-icon icon="trash" text-label="360 degree view" scale="l" title=""${this._translate.instant('Remove Measurement')}"></calcite-icon>`;
    deleteMeasureDiv.className = "esri-widget--button esri-widget esri-interactive";
    deleteMeasureDiv.addEventListener('click', (evt) => {
      deleteMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive active';
      lineMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      polygonMeasureDiv.className = 'esri-widget--button esri-widget esri-interactive';
      lineMeasureDiv.removeAttribute("id");
      polygonMeasureDiv.removeAttribute("id");
      deleteMeasureDiv.setAttribute('id', 'tool-div-measurement-delete');
      measurementWidget.clear();
      this.closeAllOpenedExpand();
    });
    measureToolDiv.appendChild(deleteMeasureDiv);
    const measurehExpand = new Expand({
      view: this.mainView,
      content: measureToolDiv,
      expanded: false,
      expandIconClass: 'bi bi-bezier fs-5',
      // expandIcon: 'bi bi-bezier fs-5',
      expandTooltip: this._translate.instant('Measurements')
    });
    this.mainView.ui.add(measurehExpand, {
      position: positionButtons
    });
    this.allExpands.push(measurehExpand);
    // this.mainView.ui.add(measureToolDiv, positionButtons);
  }

  escapeSingleQuotes(value: any) {
    return value.replace(/'/g, "''");
  }

  async createSearchWidget(position: any, param: any) {
    if(param == 'add') {
      var searchDistrictDiv = document.createElement('div');
      searchDistrictDiv.className = "search-district-tool-div";
      var selectDistrict = document.createElement('select');
      selectDistrict.className = "selectDistrict";
      const allDistricts  = await this.sharedService.getDistinctDistrictsWithIDs(this.sharedService.mapConfig.layers[1], [this.districtColumnNameLangForHousehold, 'district_id']);
      // const res  = await this.sharedService.getDistinctValues(this.sharedService.mapConfig.layers[1], this.districtColumnNameLang);
      // let infos = res.uniqueValueInfos;
      // const filteredArray = infos.filter(obj => !Object.values(obj).includes(null));
      // filteredArray.forEach((d: any) => {
      //   d.name = this.sharedService.allCapitalize(d.value)
      // });

      const optionElement = document.createElement('option');
      // @ts-ignore
      optionElement.value = 'all';
      // @ts-ignore
      optionElement.innerText = this._translate.instant('ABU DHABI');
      selectDistrict.appendChild(optionElement);

      //@ts-ignore
      // filteredArray.sort((a, b) => a.name.localeCompare(b.name));
      allDistricts.forEach(option => {
        const optionElement = document.createElement('option');
        // @ts-ignore
        optionElement.value = option.district_id;
        // @ts-ignore
        optionElement.innerText = option.districts_name;
        selectDistrict.appendChild(optionElement);
      });
      // Event listener for select box changes
      selectDistrict.addEventListener('change', (event) => {
        // @ts-ignore
        const selectedValue = event.target.value;
        if(selectedValue == 'all') {
          this.mainView.graphics.removeAll();
          this.sharedService.displayDistrictName = "District: All";
          this.calculateResults(null, this.polygonSketchLayer);
        } else {
          const escapedStr = this.escapeSingleQuotes(selectedValue);
          const whrCls = `district_id = ${escapedStr}`;
          this.sharedService.getSingleFeatureWithGeom(this.sharedService.mapConfig.layers[1], whrCls).then((feature: any) => {
            this.sharedService.displayDistrictName = "District: " + feature.features[0].attributes[this.districtColumnNameLangForHousehold];
            this.calculateResults(feature.features[0].geometry, this.polygonSketchLayer);
            // this.addPolygonToMapDirect(feature.features[0].geometry.rings);
            this.addPolygonToMapForHousehold(feature.features[0].geometry.rings[0]);
            //for summary
            this.sharedService.createSummaryForPoi(this.mainView.map, feature.features[0].geometry);
            this.closeAllOpenedExpand();
          });
        }
      });
      searchDistrictDiv.appendChild(selectDistrict);
      // const searchWidget = new Search();
      this.searchExpand = new Expand({
        view: this.mainView,
        content: searchDistrictDiv,
        expanded: false,
        expandIconClass: 'bi bi-search fs-5',
        // expandIcon: 'bi bi-search fs-5',
        expandTooltip: this._translate.instant('Search by district')
      });
      this.mainView.ui.add(this.searchExpand, {
        position: position
      });
      this.allExpands.push(this.searchExpand);
    } else {
      this.mainView.ui.remove(this.searchExpand);
    }
  }

  async createSearchWidgetForDotDensity(position: any, param: any) {
    if(param == 'add') {
      var searchDistrictDiv = document.createElement('div');
      searchDistrictDiv.className = "search-district-tool-div";
      var selectDistrict = document.createElement('select');
      selectDistrict.className = "selectDistrict";
      // const res  = await this.sharedService.getDistinctValues(this.sharedService.mapConfig.dotDensityLayers[0], this.districtColumnNameLang);

      const allDistricts  = await this.sharedService.getDistinctDistrictsWithIDs(this.sharedService.mapConfig.dotDensityLayers[2], [this.districtColumnNameLang, 'district_id']);

      // let infos = res.uniqueValueInfos;
      // const filteredArray = infos.filter(obj => !Object.values(obj).includes(null));
      // filteredArray.forEach((d: any) => {
      //   d.name = this.sharedService.allCapitalize(d.value)
      // });

      const optionElement = document.createElement('option');
      // @ts-ignore
      optionElement.value = 'all';
      // @ts-ignore
      optionElement.innerText = this._translate.instant('ABU DHABI');
      selectDistrict.appendChild(optionElement);

      //@ts-ignore
      // filteredArray.sort((a, b) => a.name.localeCompare(b.name));
      allDistricts.forEach(option => {
        const optionElement = document.createElement('option');
        // @ts-ignore
        optionElement.value = option.district_id;
        // @ts-ignore
        optionElement.innerText = option.districts_name;
        selectDistrict.appendChild(optionElement);
      });
      // Event listener for select box changes
      selectDistrict.addEventListener('change', (event) => {
        // @ts-ignore
        const selectedValue = event.target.value;
        // let fieldsOffcialGender: any = ['MALE', 'FEMALE'];
        // let fieldsOffcialGender: any = ['male', 'female'];
        let fieldsOffcialGenderCensus: any = ['male', 'female', 'male_dc', 'female_dc'];
        let districtId: any = 999;
        if(selectedValue == 'all') {
          this.mainView.graphics.removeAll();
          this.sharedService.dispalySummaryData(false);
          districtId = 999;
          let fields: any = ['total_citizen', 'total_non_citizen', 'total_population'];
          this.sharedService.getSumIndividual(fields, this.sharedService.mapConfig.dotDensityLayers[0], "1=1", "sum", null).then((response: any) => {
            response.features.map((result: any) => {
              result.attributes.DISTRICTNAMEENG = this._translate.instant("Abu Dhabi");
              result.attributes.chartName = "pieChartByCitizenship";
              let graphic = {
                graphic: result
              };
              this.sharedService.officialFigures = graphic;
              this.sharedService.setPopByNationalsOriginal(result.attributes);
            });
          });
          this.sharedService.getSumIndividual(fieldsOffcialGenderCensus, this.sharedService.mapConfig.dotDensityLayers[1], "1=1", "sum", null).then((response: any) => {
            response.features.map((result: any) => {
              const features = result.attributes;
              this.sharedService.setPopByGenderOriginal(features);
            });
          });
          this.closeAllOpenedExpand();
        } else {
          const escapedStr = this.escapeSingleQuotes(selectedValue);
          const whrCls = `district_id = ${escapedStr}`;
          this.sharedService.getSingleFeatureWithGeom(this.sharedService.mapConfig.dotDensityLayers[0], whrCls).then((feature: any) => {
            console.log(feature);

            // this.sharedService.officialFigures = allResults[i];

            // districtId = allResults[i].graphic.attributes.DISTRICT_ID;
            this.sharedService.displayDistrictName = "District: " + feature.features[0].attributes[this.districtColumnNameLang];
            // this.calculateResults(feature.features[0].geometry, this.polygonSketchLayer);
            this.addPolygonToMap(feature.features[0].geometry.rings[0]);
            // this.addPolygonToMapDirect(feature.features[0].geometry.rings);
            this.closeAllOpenedExpand();
            feature.features[0].attributes.chartName = "pieChartByCitizenshipCensus";
            let graphic = {
              graphic: feature
            };
            this.sharedService.officialFiguresCensus = graphic;
            this.sharedService.setPopByNationalsOriginal(feature.features[0].attributes);
            // this.sharedService.officialFigures = feature.features[0];
            const passObj = {
              graphic: {
                attributes: feature.features[0].attributes
              }
            };
            this.sharedService.officialFiguresCensus = passObj;


            let districtId = feature.features[0].attributes.district_id;
            this.sharedService.getSumIndividual(fieldsOffcialGenderCensus, this.sharedService.mapConfig.dotDensityLayers[1], "district_id="+districtId+"", "sum", null).then((response: any) => {
              response.features.map((result: any) => {
                const features = result.attributes;
                features.moduleSource = 'census';
                this.sharedService.setPopByGenderOriginal(features);
              });
            });
            //for summary
            this.sharedService.createSummaryForPoi(this.mainView.map, feature.features[0].geometry);
          });
          this.closeAllOpenedExpand();
        }
      });
      searchDistrictDiv.appendChild(selectDistrict);
      // const searchWidget = new Search();
      this.searchExpandForDot = new Expand({
        view: this.mainView,
        content: searchDistrictDiv,
        expanded: false,
       expandIconClass: 'bi bi-search fs-5',
        // expandIcon: 'bi bi-search fs-5',
        expandTooltip: this._translate.instant('Search by district')
      });
      this.mainView.ui.add(this.searchExpandForDot, {
        position: position
      });
      this.allExpands.push(this.searchExpandForDot);
    } else {
      this.mainView.ui.remove(this.searchExpandForDot);
    }
  }

  createFullScreenWidget(position: any) {
    var fullScreenDiv = document.createElement('div');
    fullScreenDiv.id = 'esri-full-screen-btn';
    const fullscreen = new Fullscreen({
      view: this.mainView,
      container: fullScreenDiv
    });
    this.mainView.ui.add(fullscreen, position);
  }

  createBookmarkWidget(position: any) {
    const bookmarks = new Bookmarks({
      view: this.mainView,
     // editingEnabled: true,
      visibleElements: {
        time: false
      }
    });
    const bkExpand = new Expand({
      view: this.mainView,
      content: bookmarks,
      expanded: false,
      expandTooltip: 'Bookmarks'
    });
    this.mainView.ui.add(bkExpand, position);
    this.allExpands.push(bkExpand);
  }

  createShareMapWidget(position: any) {
    var shareDiv = document.createElement('div');
    shareDiv.innerHTML = '<calcite-icon icon="collaboration-distributed" text-label="360 degree view" scale="l" title="share map"></calcite-icon>';
    shareDiv.className = "esri-widget--button esri-widget esri-interactive";
    shareDiv.id = "standalone-share";
    shareDiv.addEventListener('click', (evt) => {
      // alert("Share the Map!");
    })
    this.mainView.ui.add(shareDiv, position);
  }

  createMetadataWidget(position: any) {
    var metaToolDiv = document.createElement('div');
    metaToolDiv.className = "selection-tool-div";
    var jobseekersMetaDiv = document.createElement('div');
    // jobseekersMetaDiv.innerHTML = '<div id="select-by-rectangle" style="margin-bottom: 5px;" class="esri-widget esri-widget--button esri-widget esri-interactive" title="Select features by rectangle"><span class="esri-icon-checkbox-unchecked"></span></div>';
    jobseekersMetaDiv.innerHTML = '<a href="https://datamart.scad.gov.ae:9443/data/view/id/1026#!tab-data-summary" target="_blank" >Job Seekers</a>';
    // polygonDiv.className = "esri-widget--button esri-widget esri-interactive";
    // jobseekersMetaDiv.addEventListener('click', (evt) => {
    //   this.openModal('https://datamart.scad.gov.ae:9443/data/view/id/1026#!tab-data-summary');
    // });
    metaToolDiv.appendChild(jobseekersMetaDiv);
    var jobVacanciesDiv = document.createElement('div');
    // jobVacanciesDiv.innerHTML = '<div id="select-by-circle" style="margin-bottom: 5px;" class="esri-widget esri-widget--button esri-widget esri-interactive" title="Select features by Circle"><span class="esri-icon-radio-unchecked"></span></div>';
    jobVacanciesDiv.innerHTML = '<a href="https://datamart.scad.gov.ae:9443/data/view/id/1027" target="_blank" >Job Vacancies</a>';
    // circleDiv.className = "esri-widget--button esri-widget esri-interactive";
    // jobVacanciesDiv.addEventListener('click', (evt) => {
    //   alert("Job Vacancies")
    // });
    metaToolDiv.appendChild(jobVacanciesDiv);
    const metaExpand = new Expand({
      view: this.mainView,
      content: metaToolDiv,
      expanded: false,
      expandIconClass: 'bi bi-code-slash fs-5',
      // expandIcon: 'bi bi-code-slash fs-5',
      expandTooltip: 'View metadata list'
    });
    this.mainView.ui.add(metaExpand, {
      position: position
    });
    this.allExpands.push(metaExpand);
  }

  createBaseMapGalleryWidget(position: any) {
    // basemaps: [basemapLight, basemapDark]
    // let basemapLight = new Basemap({ portalItem: { id: "c50de463235e4161b206d000587af18b" }});
    // let basemapDark = new Basemap({portalItem: {id: "358ec1e175ea41c3bf5c68f0da11ae2b"}});
    let basemapLight = new Basemap({
      portalItem: { id: "c50de463235e4161b206d000587af18b" },
      title: "Custom Light Basemap" // set your custom title here
    });

    let basemapDark = new Basemap({
      portalItem: {id: "358ec1e175ea41c3bf5c68f0da11ae2b"},
      title: "Custom Dark Basemap" // set your custom title here
    });
    let basemapImagery = new Basemap({
      portalItem: {id: "28f49811a6974659988fd279de5ce39f"},
      title: "Imagery Hybrid" // set your custom title here
    });
    let basemapsForGallery  = new LocalBasemapsSource({
      basemaps: [basemapLight, basemapDark, basemapImagery]
    });

    const portal = new Portal();
    const source = new PortalBasemapsSource({
      portal,
      query: {
        // id: "bdb9d65e0b5c480c8dcc6916e7f4e099"
        id: "702026e41f6641fb85da88efe79dc166"
      }
    });
    let basemapGallery = new BasemapGallery({
      view: this.mainView,
      source: basemapsForGallery
    });
    const basemapGalleryExpand = new Expand({
      view: this.mainView,
      content: basemapGallery,
      expanded: false,
      expandTooltip: this._translate.instant('Basemap gallery')
    });

    // Listen for the basemap-added event
    basemapGallery.when((event: any) => {
      // const basemaps = event.source.basemaps.items;
      // event.source.basemaps.items.map((evt: any) => {
      //   evt.title = "all new";
      //   event.source.refresh();
      // })
      // basemaps[0].title = 'New Title';
      // event.source.refresh();
      // const basemapToUpdate = basemaps.find((basemap: any) => basemap.id === 'c50de463235e4161b206d000587af18b');
      // if (basemapToUpdate) {
      //   basemapToUpdate.title = 'New Title';
      //   event.source.refresh();
      // }
    });

    basemapGallery.watch('activeBasemap', function() {
    basemapGalleryExpand.expanded = false;
    });

    this.mainView.ui.add(basemapGalleryExpand, position);
    this.allExpands.push(basemapGalleryExpand);

    setTimeout(() => {
      const galleryItems = document.querySelectorAll('.esri-basemap-gallery__item-title');
      galleryItems.forEach((item, index) => {
        if (index === 0) {
          item.innerHTML = this._translate.instant("Light Basemap");
        } else if (index === 1) {
          item.innerHTML = this._translate.instant("Dark Basemap");
        } else if (index === 2) {
          item.innerHTML = this._translate.instant("Satellite Imagery");
        }
      });
    }, 1500);

  }




  createSaveWidget(position: any) {
    var saveDiv = document.createElement('div');
    saveDiv.innerHTML = '<calcite-icon icon="save" text-label="360 degree view" scale="l" title="Save map"></calcite-icon>';
    saveDiv.className = "esri-widget--button esri-widget esri-interactive";
    saveDiv.id = "standalone-save";
    saveDiv.addEventListener('click', (evt) => {
      // alert("Save the details");
    });
    this.mainView.ui.add(saveDiv, position);
  }

  createAddGeomWidget(position: any) {
    var geomToolDiv = document.createElement('div');
    geomToolDiv.className = "geom-tool-div";
    var polygonDiv = document.createElement('div');
    polygonDiv.innerHTML = '<calcite-icon icon="polygon" text-label="360 degree view" scale="l" title="Draw Shape as Freehand Polygon"></calcite-icon>';
    polygonDiv.className = "esri-widget--button esri-widget esri-interactive";
    polygonDiv.addEventListener('click', (evt) => {
      let sketch = new Sketch({
        view: this.mainView,
        layer: this.graphicsLayer, // Replace with your own graphics layer if needed
        availableCreateTools: [], // Enable only the rectangle drawing tool
        creationMode: 'single' // Set the creation mode to 'single' for one-click drawing
      });
      sketch.create('polygon');
    });
    geomToolDiv.appendChild(polygonDiv);
    var circleDiv = document.createElement('div');
    circleDiv.innerHTML = '<calcite-icon icon="circle" text-label="360 degree view" scale="l" title="Draw Shape as Circle"></calcite-icon>';
    circleDiv.className = "esri-widget--button esri-widget esri-interactive";
    circleDiv.addEventListener('click', (evt) => {
      let sketch = new Sketch({
        view: this.mainView,
        layer: this.graphicsLayer, // Replace with your own graphics layer if needed
        availableCreateTools: [], // Enable only the rectangle drawing tool
        creationMode: 'single' // Set the creation mode to 'single' for one-click drawing
      });
      sketch.create('circle');
    });
    geomToolDiv.appendChild(circleDiv);
    var rectangleDiv = document.createElement('div');
    rectangleDiv.innerHTML = '<calcite-icon class="for-tooltip" icon="rectangle" text-label="360 degree view" title="Draw Shape as Rectangle"></calcite-icon>';
    rectangleDiv.className = "esri-widget--button esri-widget esri-interactive";
    rectangleDiv.addEventListener('click', (evt) => {
      let sketch = new Sketch({
        view: this.mainView,
        layer: this.graphicsLayer, // Replace with your own graphics layer if needed
        availableCreateTools: ['rectangle'], // Enable only the rectangle drawing tool
        creationMode: 'single' // Set the creation mode to 'single' for one-click drawing
      });
      sketch.create('rectangle');
    });
    geomToolDiv.appendChild(rectangleDiv);
    var cursorDiv = document.createElement('div');
    cursorDiv.innerHTML = '<calcite-icon icon="cursor" text-label="360 degree view" scale="l" title="Map pan"></calcite-icon>';
    cursorDiv.className = "esri-widget--button esri-widget esri-interactive";
    cursorDiv.addEventListener('click', (evt) => {

    });
    geomToolDiv.appendChild(cursorDiv);
    var moveDiv = document.createElement('div');
    moveDiv.innerHTML = '<calcite-icon icon="move" text-label="360 degree view" scale="l" title="Start moving the map"></calcite-icon>';
    moveDiv.className = "esri-widget--button esri-widget esri-interactive";
    moveDiv.addEventListener('click', (evt) => {
      // this.enableMove();
    });
    geomToolDiv.appendChild(moveDiv);

    this.mainView.ui.add(geomToolDiv, position);
  }

  createSelectionWidget(position: any, params: any) {
    if(params === 'add') {
      var selectionToolDiv = document.createElement('div');
      selectionToolDiv.className = "small-area-selection-tool-div";
      var selectFreehandByPolygonDiv = document.createElement('div');
      selectFreehandByPolygonDiv.innerHTML = '<div id="select-by-rectangle" style="margin-bottom: 5px;" class="esri-widget esri-widget--button esri-widget esri-interactive" title="Select features by rectangle"><span class="esri-icon-polygon"></span></div>';
      // polygonDiv.className = "esri-widget--button esri-widget esri-interactive";
      selectFreehandByPolygonDiv.addEventListener('click', (evt) => {
        this.mainView.popup.close();
        this.sketchViewModelMain.create('polygon');
      });
      selectionToolDiv.appendChild(selectFreehandByPolygonDiv);
      var selectByPolygonDiv = document.createElement('div');
      selectByPolygonDiv.innerHTML = '<div id="select-by-rectangle" style="margin-bottom: 5px;" class="esri-widget esri-widget--button esri-widget esri-interactive" title="Select features by rectangle"><span class="esri-icon-checkbox-unchecked"></span></div>';
      // polygonDiv.className = "esri-widget--button esri-widget esri-interactive";
      selectByPolygonDiv.addEventListener('click', (evt) => {
        this.mainView.popup.close();
        this.sketchViewModelMain.create('rectangle');
      });
      selectionToolDiv.appendChild(selectByPolygonDiv);
      var circleDiv = document.createElement('div');
      circleDiv.innerHTML = '<div id="select-by-circle" style="margin-bottom: 5px;" class="esri-widget esri-widget--button esri-widget esri-interactive" title="Select features by Circle"><span class="esri-icon-radio-unchecked"></span></div>';
      // circleDiv.className = "esri-widget--button esri-widget esri-interactive";
      circleDiv.addEventListener('click', (evt) => {
        this.mainView.popup.close();
        this.sketchViewModelMain.create('circle');
      });
      selectionToolDiv.appendChild(circleDiv);
      var clearDiv = document.createElement('div');
      clearDiv.innerHTML = '<div id="clear-selection" style="margin-bottom: 5px;" class="esri-widget esri-widget--button esri-widget esri-interactive" title="Clear selection" ><span class="esri-icon-erase"></span></div>';
      // clearDiv.className = "esri-widget--button esri-widget esri-interactive";
      clearDiv.addEventListener('click', (evt) => {
        this.selectFeatures(null);
        this.calculateResults(null, this.polygonSketchLayer);
        this.polygonDrawGraphicsLayer.removeAll();
      });
      selectionToolDiv.appendChild(clearDiv);
      this.selectionExpand = new Expand({
        view: this.mainView,
        content: selectionToolDiv,
        expanded: false,
        expandIconClass: 'bi bi-scissors fs-5',
        // expandIcon: 'bi bi-scissors fs-5',
        expandTooltip: 'Small area selection'
      });
      this.mainView.ui.add(this.selectionExpand, {
        position: position
      });
      this.allExpands.push(this.selectionExpand);
    } else if(params === 'remove') {
      this.mainView.ui.remove(this.selectionExpand);
    }
  }

  async clearLayerList() {
    if(this.layerlists) {
      var items = await this.layerlists.operationalItems.items;
      var hiddenLayer = this.sharedService.mapConfig.hideLayersFromLayerList;
      for (var j = 0; j < items.length; j++) {
        if (hiddenLayer.indexOf(items[j].title) >= 0) {
          items[j].layer.listMode = "hide";
        }
      }
    }
  }
  async createLayerListWidget(position: any, params: any) {
    const customContent = document.getElementById('customContent');
    // @ts-ignore
    const clonedContent = customContent.cloneNode(true);
    if (this.layerListExpand) {
      this.mainView.ui.remove(this.layerListExpand);
      this.layerListExpand.destroy && this.layerListExpand.destroy();
    }
    if(params == 'add') {
      var customLayerDiv = document.createElement('div');
      // @ts-ignore
      // customLayerDiv.appendChild(document.getElementById('customContent'));
      customLayerDiv.appendChild(clonedContent);
      customLayerDiv.id = "customLayersContainer";
      const myGroupLayers = this.sharedService.getGrouplayers(this.mainView.map);
      let iconUrl = '';
      let allGroupLayers = myGroupLayers[0].layers._items;
      allGroupLayers.forEach((layer: any) => {
        if(layer.type === "feature" && layer.renderer && layer.renderer.symbol) {
          let symbol = layer.renderer.symbol;
          if(symbol.type === "picture-marker") {
            iconUrl = symbol.url;
          }
        }
        const div = document.createElement('div');
        div.className = 'checkbox-container';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = layer.visible;

        const label = document.createElement('label');
        label.innerHTML = this._translate.instant(layer.title);
        label.style.flex = "2";

        let img = document.createElement('img');
        img.src = iconUrl;
        img.alt = 'Thumbnail';
        img.width = 15;

        checkbox.addEventListener('change', (event) => {
          layer.visible = checkbox.checked;
        });

        div.appendChild(img);
        div.appendChild(label);
        div.appendChild(checkbox);
        customLayerDiv.appendChild(div);
      });
      this.layerListExpand = new Expand({
        view: this.mainView,
        content: customLayerDiv,
        expanded: false,
        expandIconClass: 'bi bi-layers fs-5',
        // expandIcon: 'bi bi-layers fs-5',
        expandTooltip: this._translate.instant('Layer list')
      });
      this.mainView.ui.add(this.layerListExpand, {
        position: position
      });
    }

    //old widget
    //   this.layerlists = new LayerList({
    //   view: this.mainView,
    //   listItemCreatedFunction: function (event) {
    //     const item = event.item;
    //   }
    // });
    // const layerListExpand = new Expand({
    //   view: this.mainView,
    //   content: this.layerlists,
    //   expanded: false,
    //   expandTooltip: 'Layer list'
    // });
    // this.mainView.ui.add(layerListExpand, {
    //   position: position
    // });
    // this.allExpands.push(this.layerListExpand);
  }

  createZoomWidget(position: any) {
    var zoomToolDiv = document.createElement('div');
    zoomToolDiv.className = "zoom-tool-div";
    var zoomInDiv = document.createElement('div');
    // zoomInDiv.innerHTML = '<calcite-icon icon="plus" text-label="360 degree view" scale="l"></calcite-icon>';
    zoomInDiv.innerHTML = `<i class="bi bi-plus" title="${this._translate.instant('Zoom In')}"></i>`;
    zoomInDiv.className = "esri-widget--button esri-widget esri-interactive";
    zoomInDiv.addEventListener('click', (evt) => {
      this.zoomIn();
    });
    zoomToolDiv.appendChild(zoomInDiv);
    var zoomOutDiv = document.createElement('div');
    // zoomOutDiv.innerHTML = '<calcite-icon icon="minus" text-label="360 degree view" scale="l"></calcite-icon>';
    zoomOutDiv.innerHTML = `<p style="font-size: 20px;font-weight: bolder;" title="${this._translate.instant('Zoom Out')}">-</p>`;
    zoomOutDiv.className = "esri-widget--button esri-widget esri-interactive";
    zoomOutDiv.addEventListener('click', (evt) => {
      this.zoomOut();
    });
    zoomToolDiv.appendChild(zoomOutDiv);
    this.mainView.ui.add(zoomToolDiv, position);
  }
  zoomIn() {
    this.mainView.goTo({
      target: this.mainView.center,
      zoom: this.mainView.zoom + 1,
      duration: 1000,
      easing: 'ease-in-out'
    });
    // this.mainView.zoom += 1;
  }
  zoomOut() {
    this.mainView.goTo({
      target: this.mainView.center,
      zoom: this.mainView.zoom - 1,
      duration: 1000,
      easing: 'ease-in-out'
    });
    // this.mainView.zoom -= 1;
  }
  enableMove() {
    this.mainView.navigation.mode = 'pan';
  }

  closeAllOpenedExpand() {
    for (let expand of this.allExpands) {
      expand.collapse();
    }
  }

  // getDistinctValues(layerUrl, fieldName) {
  //   const layer = new FeatureLayer({
  //     url: layerUrl,
  //     outFields: ['*'],
  //   });
  //
  //   uniqueValues({
  //     layer: layer,
  //     field: fieldName
  //   }).then((response: any) => {
  //     let infos = response.uniqueValueInfos;
  //     infos.forEach((info: any) => {
  //     });
  //   });
  //
  // }
}
