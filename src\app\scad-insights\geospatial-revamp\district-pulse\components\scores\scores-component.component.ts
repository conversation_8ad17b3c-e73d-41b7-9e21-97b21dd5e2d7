import { AfterViewInit, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit, HostListener } from '@angular/core';
import { CommonModule, DatePipe, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ScoreGaugeComponent } from './gauges/score-gauge/score-gauge.component';
import { GaugeComponent } from './gauges/gauge.component';
import { IndicatorDetailsComponent } from './indicator-details/indicator-details-component.component';
import { ActivatedRoute } from '@angular/router';

interface IndicatorData {
  id: string;
  title: string;
  score: number;
  isActive: boolean; 
}

@Component({
  selector: 'ifp-district-score',
  imports: [
    CommonModule,
    FormsModule,
    ScoreGaugeComponent,
    IndicatorDetailsComponent,
    GaugeComponent,
    TranslateModule,
    DatePipe
  ],
  templateUrl: './scores-component.component.html',
  styleUrl: './scores-component.component.scss'
})
export class ScoresComponent implements OnInit, AfterViewInit, OnDestroy {

  public showIndicatorDetails: boolean = false;
  public selectedIndicator: string | null = null;
  public isAnimating: boolean = false;
  private animationTimeout: any;

  // Indicators data
  public indicators: IndicatorData[] = [
    {
      id: 'healthcare',
      title: 'Healthcare',
      score: 40.50,
      isActive: true
    },
    {
      id: 'education',
      title: 'Education',
      score: 91.98,
      isActive: false
    },
    {
      id: 'infrastructure',
      title: 'Infrastructure',
      score: 43.48,
      isActive: false
    },
    {
      id: 'environment',
      title: 'Environment',
      score: 91.55,
      isActive: false
    }
  ];

  // Main score data
  public mainScore = {
    value: 61.48,
    label: 'Mid'
  };

  constructor(
    private _cdr: ChangeDetectorRef,
    private _datePipe: DatePipe,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
  }

  ngOnDestroy(): void {
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout);
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.showIndicatorDetails) {
      this.closeIndicatorDetails();
    }
  }

  // @HostListener('document:click', ['$event'])
  // onClickOutside(event: Event): void {
  //   const target = event.target as HTMLElement;
  //   const detailsComponent = document.querySelector('ifp-indicator-details');
  //   const clickedArrow = target.closest('.indicator-expand');
    
  //   // Close if clicking outside and not on an arrow
  //   if (this.showIndicatorDetails && 
  //       detailsComponent && 
  //       !detailsComponent.contains(target) && 
  //       !clickedArrow) {
  //     this.closeIndicatorDetails();
  //   }
  // }

  onIndicatorArrowClick(indicator: IndicatorData, event: Event): void {
    event.stopPropagation(); 
    
    if (this.isAnimating) {
      return; 
    }

    this.setActiveIndicator(indicator.id);

    if (this.selectedIndicator === indicator.id && this.showIndicatorDetails) {
      this.closeIndicatorDetails();
    } else {
      this.showIndicatorDetailsFor(indicator.id);
    }
  }

  private setActiveIndicator(indicatorId: string): void {
    this.indicators.forEach(indicator => {
      indicator.isActive = indicator.id === indicatorId;
    });
  }

  private showIndicatorDetailsFor(indicatorId: string): void {
    this.isAnimating = true;
    this.selectedIndicator = indicatorId;
    
    if (this.showIndicatorDetails) {
      this.showIndicatorDetails = false;

      this.animationTimeout = setTimeout(() => {
        this.showIndicatorDetails = true;
        this.resetAnimationState();
      }, 300);
    } else {
      this.showIndicatorDetails = true;
      this.resetAnimationState();
    }
  }


  public closeIndicatorDetails(): void {
    if (!this.showIndicatorDetails) return;
    
    this.isAnimating = true;
    this.showIndicatorDetails = false;
    
    this.animationTimeout = setTimeout(() => {
      this.selectedIndicator = null;
      this.resetActiveIndicators();
      this.resetAnimationState();
    }, 500);
  }

  private resetAnimationState(): void {
    this.animationTimeout = setTimeout(() => {
      this.isAnimating = false;
    }, 100);
  }

  private resetActiveIndicators(): void {
    this.indicators.forEach(indicator => {
      indicator.isActive = false;
    });
  }


  public get detailsAnimationClass(): string {
    return this.showIndicatorDetails ? 'indicator-details--visible' : 'indicator-details--hidden';
  }

  public isIndicatorActive(indicatorId: string): boolean {
    const indicator = this.indicators.find(ind => ind.id === indicatorId);
    return indicator?.isActive ?? false; 
  }

  public getIndicatorById(indicatorId: string): IndicatorData | undefined {
    return this.indicators.find(indicator => indicator.id === indicatorId);
  }


  public getScoreLevel(score: number): string {
    if (score >= 80) return 'High';
    if (score >= 60) return 'Mid';
    return 'Low';
  }

  onIndicatorCardClick(indicator: IndicatorData): void {
    this.setActiveIndicator(indicator.id);
  }
}