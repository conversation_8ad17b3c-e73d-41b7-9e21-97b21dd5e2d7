.map-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.map-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.map-loading-text {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.4;
}

.map-progress-bar {
  width: 300px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-progress-fill {
  height: 100%;
  background: #2196F3;
  border-radius: 3px;
  animation: mapProgress 2.5s ease-in-out infinite;
}

@keyframes mapProgress {
  0% {
    width: 0%;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 100%;
    opacity: 0.3;
  }
}

// Dark theme variant
.map-loading-overlay.dark {
  .map-loading-text {
    color: #ecf0f1;
  }
  
  .map-progress-bar {
    background: rgba(255, 255, 255, 0.1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .map-progress-bar {
    width: 250px;
    height: 5px;
  }
}