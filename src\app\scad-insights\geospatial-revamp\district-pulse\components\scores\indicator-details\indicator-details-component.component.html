<!-- Wrap only main and child indicators in the indicator-container -->
<div class="indicator-open"></div>

<!-- main indicator  -->
<div class="main-indicator" 
     [@slideAnimation]="getAnimationParams()" 
     [ngClass]="{'active': activeIndicatorIndex === 0}"
     [hidden]="!visibleIndicators[0]">
  <div class="indicator-header">
    <div class="left-icons">
      <em class="ifp-icon ifp-icon-multiple-user"></em>
    </div>
    <div class="right-icons">
      <span *ngIf="!areChildIndicatorsVisible">
        <em class="ifp-icon ifp-icon-dockside-left" (click)="toggleChildIndicators()"></em>
        <span class="ifp-horizontal-icons">
          <em class="ifp-icon ifp-icon-left-arrow" (click)="switchIndicator()"></em>
          <em class="ifp-icon ifp-icon-right-arrow" (click)="switchIndicator()"></em>
        </span>
      </span>
      <em class="ifp-icon ifp-icon-down-arrow-round"></em>
    </div>
  </div>
  <div class="main-indicator-title">
    <span id="district-name">Abu Dhabi Emarite</span>
    <span>Population</span>
    <span>3,822,390</span>
  </div>
  <!-- national indicator split -->
  <div class="main-indicator-national-split">
    <div class="national-chart">
      <img src="../../../assets/icons/geospatial-ravamp/parents.svg" alt="">
      <div class="indicator-values">
        <span class="label">Emirati</span>
        <span class="value">1,250,000</span>
      </div>
      <div class="progress-bar-charts">
        <span>35%</span>
        <div class="progress-bar-line orange" [style.width]="35 + '%'"></div>
      </div>
    </div>
    <div class="national-chart">
      <img src="../../../assets/icons/geospatial-ravamp/parents.svg" alt="">
      <div class="indicator-values">
        <span class="label">Non-Emirati</span>
        <span class="value">1,250,000</span>
      </div>
      <div class="progress-bar-charts">
        <span>65%</span>
        <div class="progress-bar-line green" [style.width]="65 + '%'"></div>
      </div>
    </div>
  </div>
  <!-- gender indicator split -->
  <div class="main-indicator-gender-split">
    <div class="gender-chart">
      <img src="../../../assets/icons/geospatial-ravamp/male.svg" alt="">
      <div class="indicator-values">
        <span class="label">Male</span>
        <span class="value">1,750,000</span>
      </div>
    </div>
    <div class="gender-chart">
      <img src="../../../assets/icons/geospatial-ravamp/female.svg" alt="">
      <div class="indicator-values">
        <span class="label">Female</span>
        <span class="value">1,250,000</span>
      </div>
    </div>
  </div>
  <!-- indicator footer - always on main indicator -->
  <div *ngIf="areChildIndicatorsVisible" class="main-indicator-footer">
    <input type="checkbox" (click)="toggleChildIndicators()"  id="">
    <em class="ifp-icon ifp-icon-horizontal-arrows" 
        [ngClass]="{'disabled': areChildIndicatorsVisible}"
        (click)="!areChildIndicatorsVisible && switchIndicator()"></em>
  </div>
</div>

<!-- child indicator population -->
<div class="child-indicator population" 
     [ngClass]="{'all-visible': areChildIndicatorsVisible, 'active': activeIndicatorIndex === 1}"
     [@slideAnimation]="getAnimationParams()" 
     [hidden]="!visibleIndicators[1] && !areChildIndicatorsVisible">
  <div class="indicator-header">
    <div class="left-icons">
      <img src="../../../assets/icons/geospatial-ravamp/employed_sectors_activities.svg" alt="">
    </div>
    <div class="right-icons">
      <span>Set as default</span>
      <input type="checkbox" name="" id="">
    </div>
  </div>
  <div class="child-indicator-title">
    <span>Employees</span>
    <span>1,857,964</span>
  </div>
  <!-- national indicator split -->
  <div class="main-indicator-national-split">
    <div class="national-chart">
      <img src="../../../assets/icons/geospatial-ravamp/parents.svg" alt="">
      <div class="indicator-values">
        <span class="label">Emirati</span>
        <span class="value">1,250,000</span>
      </div>
      <div class="progress-bar-charts">
        <span>35%</span>
        <div class="progress-bar-line orange" [style.width]="35 + '%'"></div>
      </div>
    </div>
    <div class="national-chart">
      <img src="../../../assets/icons/geospatial-ravamp/parents.svg" alt="">
      <div class="indicator-values">
        <span class="label">Non-Emirati</span>
        <span class="value">1,250,000</span>
      </div>
      <div class="progress-bar-charts">
        <span>65%</span>
        <div class="progress-bar-line green" [style.width]="65 + '%'"></div>
      </div>
    </div>
  </div>
  <!-- gender indicator split -->
  <div class="main-indicator-gender-split">
    <div class="gender-chart">
      <img src="../../../assets/icons/geospatial-ravamp/male.svg" alt="">
      <div class="indicator-values">
        <span class="label">Male</span>
        <span class="value">1,750,000</span>
      </div>
    </div>
    <div class="gender-chart">
      <img src="../../../assets/icons/geospatial-ravamp/female.svg" alt="">
      <div class="indicator-values">
        <span class="label">Female</span>
        <span class="value">1,250,000</span>
      </div>
    </div>
  </div>
  <!-- Add navigation controls when in single indicator mode -->
  <div class="main-indicator-footer" *ngIf="!areChildIndicatorsVisible">
    <em class="ifp-icon ifp-icon-dockside-left" (click)="toggleChildIndicators()"></em>
    <em class="ifp-icon ifp-icon-horizontal-arrows" (click)="switchIndicator()"></em>
  </div>
</div>

<!-- child indicator buildings -->
<div class="child-indicator real-estate" 
     [ngClass]="{'all-visible': areChildIndicatorsVisible, 'active': activeIndicatorIndex === 2}"
     [@slideAnimation]="getAnimationParams()" 
     [hidden]="!visibleIndicators[2] && !areChildIndicatorsVisible">
  <div class="indicator-header">
    <div class="left-icons">
      <img src="../../../assets/icons/geospatial-ravamp/buildings.svg" alt="">
    </div>
    <div class="right-icons">
      <span>Set as default</span>
      <input type="checkbox" name="" id="">
    </div>
  </div>
  <div class="child-indicator-title">
    <span>Real estate</span>
    <span>986,238</span>
  </div>
  <!-- national indicator split -->
  <div class="main-indicator-national-split">
    <div class="national-chart">
      <img src="../../../assets/icons/geospatial-ravamp/business-and-trade_1.svg" alt="">
      <div class="indicator-values">
        <span class="label">Buildings</span>
        <span class="value">1,250,000</span>
      </div>
      <div class="progress-bar-charts">
        <span>24%</span>
        <div class="progress-bar-line orange" [style.width]="24 + '%'"></div>
      </div>
    </div>
    <div class="national-chart">
      <img src="../../../assets/icons/geospatial-ravamp/houseHold_housingUnit.svg" alt="">
      <div class="indicator-values">
        <span class="label">Units</span>
        <span class="value">754,555</span>
      </div>
      <div class="progress-bar-charts">
        <span>75%</span>
        <div class="progress-bar-line green" [style.width]="75 + '%'"></div>
      </div>
    </div>
  </div>
  <!-- Add navigation controls when in single indicator mode -->
  <div class="main-indicator-footer" *ngIf="!areChildIndicatorsVisible">
    <em class="ifp-icon ifp-icon-dockside-left" (click)="toggleChildIndicators()"></em>
    <em class="ifp-icon ifp-icon-horizontal-arrows" (click)="switchIndicator()"></em>
  </div>
</div>

<!-- indicator details -->
<div class="indicator-card-details">
  <div class="indicator-header">
    <div class="left-icons">
      <em class="ifp-icon ifp-icon-health"></em>
    </div>
    <div class="right-icons">
      <em matTooltip="Notification" class="ifp-icon ifp-icon-notification"></em>
      <em class="ifp-icon ifp-icon-down-arrow-round"></em>
    </div>
  </div>
  <div class="indicator-chart">
    <div class="chart-header">
      <span>Accessibility <em class="ifp-icon ifp-icon-exclamation-round"></em></span>
      <span>97.56%</span>
    </div>
    <div class="progress-bar-charts">
      <div class="progress-bar-line total" [style.width]="93 + '%'"></div>
      <div class="progress-bar-line remaining-stats" [style.width]="7 + '%'"></div>
      <span>High</span>
    </div>
  </div>
  <div class="indicator-chart">
    <div class="chart-header">
      <span>Availability <em class="ifp-icon ifp-icon-exclamation-round"></em></span>
      <span>97.56%</span>
    </div>
    <div class="progress-bar-charts">
      <div class="progress-bar-line total" [style.width]="97 + '%'"></div>
      <div class="progress-bar-line remaining-stats" [style.width]="3 + '%'"></div>
      <span>High</span>
    </div>
  </div>
  <div class="statistics-container">
    <div class="indicator-statistics">
      <div class="statistics">
        <img src="../../../assets/icons/geospatial-ravamp/hospital_1.svg" alt="">
        <div class="statistics-values">
          <span>Access to</span>
          <span id="value">15 Hospitals</span>
        </div>
      </div>
    </div>
    <div class="indicator-statistics">
      <div class="statistics">
        <img src="../../../assets/icons/geospatial-ravamp/business-and-trade_1.svg" alt="">
        <div class="statistics-values">
          <span>Access to</span>
          <span id="value">89 Helth centers</span>
        </div>
      </div>
    </div>
    <div class="indicator-statistics">
      <div class="statistics">
        <img src="../../../assets/icons/geospatial-ravamp/buildings.svg" alt="">
        <div class="statistics-values">
          <span>Access to</span>
          <span id="value">578 Clinics</span>
        </div>
      </div>
    </div>
    <div class="indicator-statistics">
      <div class="statistics">
        <img src="../../../assets/icons/geospatial-ravamp/medicine.svg" alt="">
        <div class="statistics-values">
          <span>Access to</span>
          <span id="value">1024 Pharmacies</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Indicator statistics -->
<ifp-district-statistics></ifp-district-statistics> 

<div class="indicator-close"></div>