<div class="hierarchical-multiselect" [ngClass]="{'open': isMenuOpen}">
    <!-- Dropdown trigger --> 
    <div class="select-trigger" (click)="toggleMenu()">
      <span>Districts</span>
      <span> <i class="arrow-down"></i> </span> 
    </div>

    <!-- Dropdown menu -->
    <div class="options-container" *ngIf="isMenuOpen">
      <div class="options-list">
          <label class="district-item active">Al Dana</label>
          <label class="district-item"><PERSON></label>
          <label class="district-item"><PERSON></label>
          <label class="district-item">Al <PERSON></label>
      </div>
    </div>
  </div>