import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IfpAnalysisComponent } from './ifp-analysis/ifp-analysis.component';
import { TranslateModule } from '@ngx-translate/core';
import { HighchartsChartModule } from 'highcharts-angular';
import { IfpWhatsNewComponent } from './ifp-whats-new/ifp-whats-new.component';
import { HomeComponent } from './home/<USER>';
import { HomeRoutingModule } from './home-routing.module';
import { GeoSpatialComponent } from './geo-spatial/geo-spatial.component';
import {  StoreModule } from '@ngrx/store';
import { homeReducer } from './store/home.state';
import { EffectsModule } from '@ngrx/effects';
import { whatsNewEffects } from './store/whatsnew/whatsnew.effects';
import { ComponentLoaderComponent } from '../ifp-widgets/ifp-atoms/component-loader/component-loader.component';
import { AnalyticalAppEffects } from './store/Analytical apps/analyticalApps.effects';
import { IfpCardLoaderComponent } from '../ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpCensusComponent } from "./ifp-census/ifp-census.component";

@NgModule({
    declarations: [HomeComponent],
    imports: [
        CommonModule,
        IfpAnalysisComponent,
        TranslateModule, 
        HighchartsChartModule,
        IfpWhatsNewComponent,
        GeoSpatialComponent,
        HomeRoutingModule,
        ComponentLoaderComponent,
        StoreModule.forFeature('home', homeReducer),
        EffectsModule.forFeature([whatsNewEffects, AnalyticalAppEffects]),
        IfpCardLoaderComponent,
        IfpCensusComponent
    ]
})
export class HomeModule { }
