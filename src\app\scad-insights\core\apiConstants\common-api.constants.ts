export const commonApi = {
  homePage: 'common/home',
  indicator: 'content-type/',
  notificationMappedData: 'content-type/notifications/mappings',
  setNotificationMappedData: 'content-type/notifications/subscribe',
  unsubscribeNotificationMappedData: 'content-type/notifications/unsubscribe',
  notificationRead: 'content-type/notifications/read',
  statisticsInsights: 'content-type/statistics-insights/overview',
  chartFilter: 'content-type/statistics-insights/filter',
  getIndicatorTnC: 'common/indicator-tc',
  setIndicatorTnC: 'common/indicator-tc/set',
  visaData: 'content-type/innovative-insights/',
  compareIndicators: 'content-type/indicator-compare/create',
  compareAddToMyApps: 'content-type/indicator-compare/add-to-myapps',
  getCompareAppsViewEndpoint: 'content-type/indicator-compare',
  setEmailMappedData: 'content-type/notifications/subscribe/email',
  unsubscribeEmailMappedData: 'content-type/notifications/unsubscribe/email',
  userSettings: 'content-type/settings',
  glosseryFilters: 'content-type/glossary/filters',
  getGlossoryList: 'content-type/glossary',
  enabledNotification: 'content-type/notifications/map-list',
  createDashboard: 'content-type/dashboard-builder/create',
  updateDashboard: 'content-type/dashboard-builder/dashboard',
  dashboardList: 'content-type/dashboard-builder/dashboards',
  dashboardDetail: 'content-type/dashboard-builder/dashboard',
  census: 'content-type/census',
  shareDashboard: 'content-type/dashboard-builder/dashboard/share',
  sendDashboardsList: 'content-type/dashboard-builder/dashboard/share/list/sent',
  recieveDashboardList: 'content-type/dashboard-builder/dashboard/share/list/received',
  sendOrRecieveDashboardDetail: 'content-type/dashboard-builder/dashboard/share',
  deleteSendDashboard: 'content-type/dashboard-builder/dashboard/share/delete/sent',
  deleteRecievedDashboard: 'content-type/dashboard-builder/dashboard/share/delete/received',
  uploadCustomData: 'content-type/dashboard-builder/card-data',
  role: 'user-onboarding/role',

  geoRegions: 'content-type/geospatial-revamp/regions',
  geoAllFilters: 'content-type/geospatial-revamp/allFilters',
  getIndicatorsList: 'content-type/analytical-apps/geo-revamp-icons',
  getDomainsPermissions: 'content-type/geospatial-revamp/domains',
  geoSummary: 'content-type/geospatial-revamp/summary',
  geoPopulationSummary: 'content-type/geospatial-revamp/summaryPopulation',
  geoLaborForceSummary: 'content-type/geospatial-revamp/summaryLaborForce',
  geoRealEstateSummary: 'content-type/geospatial-revamp/summaryRealEstate',
  getDataGovernanceChartData: 'data-governance',
  getCatDataCount: 'data-governance/Catcount',
  getDropDownOpt: 'data-governance/dropdown',
  getDataScienceInput: 'data-governance/data-science-input',
  getDataScienceOutput: 'data-governance/data-science-output',
  getComparisonReport: 'data-governance/comparison'
};

export const dataGovernanceApiConstatnts: any = {
  use_case: 'data-governance/use-case',
  data_source: 'data-governance/data-source',
  data_science: 'data-governance/data-science',
  workflow_details: 'data-governance/workflow-details',
  bayaan_svs: 'data-governance/bayaan-svs',
  statistical_indicator: 'data-governance/statistical-indicator',
  sv_calendar: 'data-governance/sv-calender',
  event_detail: 'data-governance/event-detail'
}

export const dataGovernanceDropDownConstants: any = {
  use_case: 'data-governance/use-case-dropdown',
  data_source: 'data-governance/data-source-dropdown',
  data_science: 'data-governance/data-science-dropdown',
  workflow_details: 'data-governance/workflow-details-dropdown',
  bayaan_svs: 'data-governance/bayaan-svs-dropdown',
  statistical_indicator: 'data-governance/statistical-indicator-dropdown',
}
