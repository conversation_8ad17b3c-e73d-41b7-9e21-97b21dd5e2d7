import { AfterViewInit, Component, EventEmitter, OnDestroy, OnInit, Input, Output, ViewChild } from '@angular/core';
import { CensusMapComponent } from './census-map/census-map.component';


@Component({
  selector: 'ifp-map-component',
  standalone: true,
  imports: [
    CensusMapComponent,
  ],
  templateUrl: './map-component.component.html',
  styleUrl: './map-component.component.scss'
})
export class MapComponentComponent implements OnInit, AfterViewInit, OnDestroy {

  @Input() isPreviewOpen: boolean = false;
  @Input() isCustomizeOpen: boolean = false;
  @Input() filtersData: any = [];
  @Output() filterChange = new EventEmitter();  
  @Output() regionChanged = new EventEmitter();
  @Output() districtsChange = new EventEmitter();
  @Output() communitiesChange = new EventEmitter();
  public resetLocation: boolean = false;

  @ViewChild('censusMapCmp') censusMapCmp!: CensusMapComponent


  constructor() {

  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }


  filterChanged(filter: any) {
    this.filterChange.emit(filter);
  }

  resetFilter(reset: boolean) {
    this.resetLocation = reset;
    this.censusMapCmp.resetSelectionLevel(reset);
  }

  updateSelectionLevel(level: string) {
    this.censusMapCmp.updateSelectionLevel(level);
  }

  regionChange(region: any) {
    this.regionChanged.emit(region);
  }

  districtChange(districts: any) {
    this.districtsChange.emit(districts);
  }

  communityChange(communites: any) {
    this.communitiesChange.emit(communites);
  }


}
