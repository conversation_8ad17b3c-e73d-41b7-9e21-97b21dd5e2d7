import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import * as Highcharts from 'highcharts';
import HC_exporting from 'highcharts/modules/exporting';
import { TranslateModule } from '@ngx-translate/core';
import HC_exportData from 'highcharts/modules/export-data';
import HC_accessibility from 'highcharts/modules/accessibility';
import { Options } from 'highcharts';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { HighchartsChartModule } from 'highcharts-angular';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

HC_exporting(Highcharts);
HC_exportData(Highcharts);
HC_accessibility(Highcharts);

@Component({
  selector: 'ifp-score-gauge',
  standalone: true,
  imports: [
    FormsModule, 
    CommonModule, 
    HighchartsChartModule, 
    TranslateModule
  ],
  templateUrl: './score-gauge.component.html',
  styleUrl: './score-gauge.component.scss'
})
export class ScoreGaugeComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

  highcharts: typeof Highcharts = Highcharts;
  @Input() score: number = 50;
  public chartOptions!: Options;

  constructor(
    private _cdr: ChangeDetectorRef,
    private themeService: ThemeService,
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['score'] && !changes['score'].firstChange) {
      this.updateChart();
    }
  }

  ngOnInit(): void {
    this.createGaugeChart();
  }

  ngAfterViewInit(): void {
  }

  ngOnDestroy(): void {
  }

  updateChart() {
    if (this.chartOptions && this.chartOptions.series && Array.isArray(this.chartOptions.series)) {
      const series = this.chartOptions.series[0] as Highcharts.SeriesGaugeOptions;
      if (series && series.data) {
        series.data = [this.score];
        this._cdr.detectChanges();
      }
    }
  }

  createGaugeChart() {
  const customCSS = document.createElement('style');
  customCSS.innerHTML = `
    .no-border {
      stroke: transparent !important;
      stroke-width: 0 !important;
    }
  `;
  document.head.appendChild(customCSS);
    this.chartOptions = {
      chart: {
        type: 'gauge',
        plotBackgroundColor: 'transparent',
        plotBorderWidth: 0,
        plotShadow: false,
        height: '100%',
        style: {position: 'absolute', top: '15px'},
        backgroundColor: 'transparent',
        plotBorderColor: 'transparent' 
      },
      title: {
        text: ''
      },
      subtitle: {
        text: ''
      },
      exporting: {
        enabled: false
      },
      legend: {
        enabled: false
      },
      credits: {
        enabled: false
      },
      pane: {
        startAngle: -90,
        endAngle: 90,
        background: [{
          backgroundColor: 'transparent',
          borderWidth: 0,
          outerRadius: '100%',
          innerRadius: '80%',
          borderColor: 'transparent'
        }],
      },
      yAxis: {
        min: 0,
        max: 100,
        lineWidth: 0,
        minorTickInterval: 0,
        tickAmount: 0,
        tickColor: 'transparent',
        labels: {
          enabled: false
        },
        plotBands: [{
          from: 0,
          to: 100,
          thickness: 6,
          borderWidth: 0,
          borderColor: 'transparent',
          color: {
            linearGradient: { x1: 0, y1: 0, x2: 1, y2: 0 },
            stops: [
              [0, '#DF5353'],   // Red
              [0.33, '#DDDF0D'], // Yellow
              [0.66, '#55BF3B']  // Green
            ]
          }
        }]
      },
      series: [{
        type: 'gauge',
        name: 'Score',
        data: [this.score],
        dataLabels: {
          enabled: false
        },
        dial: {
          radius: '80%',
          backgroundColor: 'black',
          baseWidth: 8,
          baseLength: '0%',
          rearLength: '0%'
        },
        pivot: {
          backgroundColor: 'black',
          radius: 6
        }
      }]
    };
    this._cdr.detectChanges();
  }
}