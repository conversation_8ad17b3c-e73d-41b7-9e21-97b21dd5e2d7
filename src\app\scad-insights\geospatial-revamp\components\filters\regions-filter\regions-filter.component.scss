@use "../../../../../../assets/ifp-styles/abstracts/index" as *;

.filters-container {
  width: 64%;
  margin: 0% 18%;
  top: 42px;
  z-index: 4;
  position: relative;
  display: flex;
  align-items: center;
}

// Responsive design styles
@media screen and (max-width: 1920px) {
  .filters-container {
    width: 60%;
    margin: 0% 20%;
  }
}

@media screen and (max-width: 1400px) {
  .filters-container {
    width: 58%;
    margin: 0% 21%;
  }
}

@media screen and (max-width: 1299px) {
  .filters-container {
    width: 87%;
    margin: 0% 6.5%;
  }
}

.filters-container .filters-menu {
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
  backdrop-filter: blur(8px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left center;
  flex: 1;
  margin-left: 0;

  // Enhanced glassy effect
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);

  // Hover state for enhanced interactivity
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    // transform: translateY(-1px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  // Focus state for accessibility
  &:focus-within {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.4);
    outline: none;
  }

  // Support for older browsers without backdrop-filter
  @supports not (backdrop-filter: blur(8px)) {
    background: rgba(255, 255, 255, 0.3);
  }
}

// Animation states for filters menu - sliding from/to the toggle button
.filters-menu--hidden {
  transform: translateX(-100%) scaleX(0);
  opacity: 0;
  pointer-events: none;
  width: 0;
  min-width: 0;
  margin-left: -20px; // Pull it into the toggle button
  border-radius: 0 15px 15px 0; // Match the toggle button's right side
}

.filters-menu--visible {
  transform: translateX(0) scaleX(1);
  opacity: 1;
  pointer-events: auto;
  width: 100%;
  margin-left: 0;
  border-radius: 15px;
}

// Open filters icon animations
.open-filters-icon {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  position: absolute;
  left: 0;
  z-index: 10;

  &--rotate {
    .ifp-icon {
      transform: rotate(180deg);
      transition: transform 0.3s ease;
    }
  }

  .ifp-icon {
    transition: transform 0.3s ease;
  }
}

.open-filters-icon.visible {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
  position: relative;
}

.open-filters-icon.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
  position: absolute;
}

.select-container {
  flex: 1;
  position: relative;
  height: 33px;
  transition: all 0.3s ease;
  opacity: 1;
}

// Hide individual containers when menu is hidden
.filters-menu--hidden .select-container {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.3s ease 0.1s; // Slight delay for staggered effect
}

.filters-menu--visible .select-container {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.4s ease 0.2s; // Delayed appearance for staggered effect
}

.filter-icons {
  color: #333; // Changed from #fff to dark for better visibility on light glass
  height: 33px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  z-index: 5;

  &:hover {
    background: rgba(255, 255, 255, 0.25);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .ifp-icon {
    transition: transform 0.3s ease;
  }
}

// Special styling for the close button inside the menu
.filters-menu .filter-icons:first-child {
  border-right: 1px solid rgba(255, 255, 255, 0.3); // Updated border to match glassy theme
  height: 33px;
  opacity: 1;
  transition: opacity 0.3s ease 0.3s;
}

.filters-menu--hidden .filter-icons:first-child {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.filter-icons:first-child {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  height: 33px;
}

.border-revearse:first-child {
  border-right: none;
}

.border-revearse:last-child {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
}

.filters-container .open-filters-icon {
  width: 20px;
  padding: 7px 3px 7px 7px;
  border-radius: 15px 0px 0px 15px;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
  backdrop-filter: blur(8px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #333; // Changed from #FFF to dark for better visibility
  cursor: pointer;
  box-sizing: content-box;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);

  &:hover {
    background: rgba(255, 255, 255, 0.25);
    // transform: scale(1.05) translateY(-1px);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  // Support for older browsers without backdrop-filter
  @supports not (backdrop-filter: blur(8px)) {
    background: rgba(255, 255, 255, 0.3);
  }
}

// When menu is visible, make the toggle button blend with the menu
.filters-menu--visible ~ .open-filters-icon,
.open-filters-icon.hidden {
  opacity: 0;
  transform: scale(0.8);
}

.filters-container .selection-level {
  font-size: 1.4rem;
  width: 140px;
  font-weight: bold;
  position: absolute;
  top: 31px;
  left: 15px;
  color: #333; // Changed from #000 for better contrast
  z-index: 3;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8); // Added text shadow for better readability
}

.filter-icon-left {
  .ifp-icon {
    transform: rotate(180deg);
  }
}

.loading-indicator {
  padding: 12px;
  text-align: center;
  font-size: 1.2rem;
  color: #333; // Changed from #FFF to dark
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(4px);
}

.filters-menu--hidden .loading-indicator {
  opacity: 0;
  transform: translateX(-30px);
}

// Enhanced staggered animation for filter items
.filters-menu--visible .select-container:nth-child(2) { transition-delay: 0.1s; }
.filters-menu--visible .select-container:nth-child(3) { transition-delay: 0.15s; }
.filters-menu--visible .select-container:nth-child(4) { transition-delay: 0.2s; }
.filters-menu--visible .select-container:nth-child(5) { transition-delay: 0.25s; }
.filters-menu--visible .select-container:nth-child(6) { transition-delay: 0.3s; }
.filters-menu--visible .select-container:nth-child(7) { transition-delay: 0.35s; }

// Reset button animation
.filters-menu .filter-icons:last-child {
  transition: all 0.3s ease 0.4s;
}

.filters-menu--hidden .filter-icons:last-child {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.2s ease;
}

// RTL Support
:host-context([dir="rtl"]) {
  .filters-container {
    flex-direction: row-reverse;
  }

  .filters-menu--hidden {
    transform: translateX(100%) scaleX(0);
    margin-left: 0;
    margin-right: -20px;
    border-radius: 15px 0 0 15px;
    transform-origin: right center;
  }

  .open-filters-icon {
    left: auto;
    right: 0;
  }

  .filters-container .open-filters-icon {
    border-radius: 0px 15px 15px 0px;
  }

  .filters-menu--hidden .select-container,
  .filters-menu--hidden .filter-icons:last-child {
    transform: translateX(20px);
  }
}

// Additional glassy effects for modern browsers
@supports (backdrop-filter: blur(8px)) {
  .filters-container .filters-menu,
  .filters-container .open-filters-icon {
    // Enhanced glass effect with better saturation
    -webkit-backdrop-filter: blur(12px) saturate(200%);
    backdrop-filter: blur(12px) saturate(200%);
  }

  .filter-icons:hover {
    -webkit-backdrop-filter: blur(6px) saturate(150%);
    backdrop-filter: blur(6px) saturate(150%);
  }
}

// Custom Multiselect Component - Glassy Theme
.custom-multiselect {
  position: relative;
  width: 100%;
  font-family: "Inter", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  border-right: 1px solid rgba(255, 255, 255, 0.3);
}

.select-trigger {
  position: relative;
  padding: 8px 12px;
  color: #333;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  transition: all 0.2s ease;

  &:hover {
    // transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

.arrow-down {
  position: relative;
  top: -3px;
  border: solid #333;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
  transition: transform 0.2s ease;
}

.arrow-up {
  position: relative;
  top: 0px;
  border: solid #333;
  border-width: 0 2px 2px 0;
  display: inline-block;
  transform: rotate(-135deg);
  transition: transform 0.2s ease;
}

.dark-theme {
  .select-trigger , .arrow-down{
    color: #fff!important;
  }
}

// .select-trigger:hover .arrow-down {
//   transform: rotate(45deg) scale(1.1);
// }

.options-container {
  width: 18rem;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  // -webkit-backdrop-filter: blur(12px) saturate(180%);
  // backdrop-filter: blur(12px) saturate(180%);
  @include geo-blur;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(160, 160, 160, 0.377);
    border-radius: 3px;

    &:hover {
      background: rgba(160, 160, 160, 0.377);
    }
  }
}

.search-box {
  padding: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);

  input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    color: #333;
    font-size: 14px;
    transition: all 0.2s ease;

    &::placeholder {
      color: rgba(51, 51, 51, 0.7);
    }

    &:focus {
      outline: none;
      border: 1px solid rgba(255, 255, 255, 0.5);
      background: rgba(255, 255, 255, 0.3);
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
    }

    // Support for older browsers without backdrop-filter
    @supports not (backdrop-filter: blur(4px)) {
      background: rgba(255, 255, 255, 0.4);
    }
  }
}

.select-actions {
  padding: 12px;
  display: flex;
  gap: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.select-actions button {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  color: #333;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.4);
    // transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  // Support for older browsers without backdrop-filter
  @supports not (backdrop-filter: blur(4px)) {
    background: rgba(255, 255, 255, 0.35);
  }
}


.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    // transform: translateX(2px);
  }
}

.region-name {
  margin-left: 8px;
  color: #333;
  font-weight: 500;
}

.dark-theme {
  .region-name {
    color: #fff;
  }
}

.region-code {
  margin-left: 8px;
  color: rgba(51, 51, 51, 0.8);
  font-size: 0.9em;
  font-weight: 400;
}

input[type="checkbox"] {
  width: 15px;
  min-width: 15px!important;
  max-width: 15px!important;
  height: 15px;
  appearance: none;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(151, 151, 151, 0.4);
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(151, 151, 151, 0.6);
    // transform: scale(1.05);
  }
}

input[type="checkbox"]:checked::before {
  content: '\2714';
  position: absolute;
  top: -1px;
  left: 0;
  right: 0;
  bottom: 0;
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 15px;
  font-weight: bold;
}

input[type="checkbox"]:checked {
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(151, 151, 151, 0.6);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}
