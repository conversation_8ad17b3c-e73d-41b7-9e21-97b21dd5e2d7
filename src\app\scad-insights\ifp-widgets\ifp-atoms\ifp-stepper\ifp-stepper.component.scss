@use "../../../../../assets/ifp-styles/abstracts" as *;
.ifp-stepper {
  &__wrapper {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  &__name {
    font-weight: $fw-bold;
    text-align: center;
  }
  &__sub-name {
    font-size: $ifp-fs-2;
    color: $ifp-color-grey-14;
  }
  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;

    @media (max-width: 768px) {
      font-size: $ifp-fs-2;
    }
    &::before,
    &::after {
      content: "";
      border-bottom: 4px solid $ifp-color-grey-8;
      width: 100%;
      position: absolute;
      top: 17px;
      z-index: 2;
    }
    &::before {
      left: -50%;
    }
    &::after {
      left: 50%;
    }
    &:last-child::after {
      content: none;
    }
    &:first-child::before {
      content: none;
    }
  }
  &__counter {
    .ifp-stepper__tick {
      display: none;
    }
    border: 2px solid $ifp-color-grey-8;
    position: relative;
    z-index: 5;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background: $ifp-color-white;
    color: $ifp-color-grey-8;
    margin-bottom: 6px;
  }
  &__icon {
    font-size: $ifp-fs-6;
  }
  &--completed {
    .ifp-stepper__count {
      display: none;
    }
    .ifp-stepper__tick {
      color: $ifp-color-white;
      display: block;
    }
    .ifp-stepper__counter {
      border: none;
      background-color: $ifp-color-green-dark-2;
    }
    &::after {
      border-bottom: 4px solid $ifp-color-green-dark-2;
      z-index: 3;
    }
  }
  &--adv {
    margin: $spacer-0;
    .ifp-stepper {
      &__item {
        &::before,
        &::after {
          content: "";
          border-bottom: 1px solid $ifp-color-grey-8;
        }
        &:last-child::after {
          content: none;
        }
        &:first-child::before {
          content: none;
        }
      }
      &__counter {
        background-color: $ifp-color-grey-6;
        border: none;
        color: $ifp-color-white-global;
      }
      &__name {
        max-width: 140px;
        text-align: center;
      }
      &--completed {
        .ifp-stepper {
          &__counter {
            border: none;
            background-color: $ifp-color-green-dark-2;
          }
          &__icon {
            display: none;
          }
        }
        &::before {
          content: none;
        }
        &::after {
          border-bottom: 4px solid $ifp-color-green-dark-2;
          z-index: 3;
        }
        &.ifp-stepper--optional {
          &::after {
            border-bottom: 4px dashed $ifp-color-green-dark-2;
            z-index: 3;
          }
        }
      }
      &--pending {
        &::before {
          content: none;
        }
        .ifp-stepper {
          &__counter {
            background-color: $ifp-color-orange;
          }
        }
        &.ifp-stepper--completed {
          &::after {
            content: none;
          }
        }
      }
      &__square {
        background-color: $ifp-color-white-global;
        border-radius: 3px;
        width: 12px;
        height: 12px;
      }
    }
  }
  &--gradient {
    .ifp-stepper {
      &__name {
        color: $ifp-color-grey-14;
        font-size: $ifp-fs-3;
        font-weight: $fw-medium;
      }
      &__item {
        &::after {
           left: calc(50% + 17px);
        }
        &::before {
         left: calc(-50% + 17px);
        }
        &::after,&::before {
          border:2px solid rgba($color: $ifp-color-blue-2, $alpha: 0.05);
          width: calc(100% - 34px);
        }
      }
      &__counter {
        border: none;
        font-weight: $fw-bold;
        color: $ifp-color-grey-14;
        background-color: rgba($color: $ifp-color-blue-2, $alpha: 0.1);

      }
      &--completed {
        .ifp-stepper{
          &__counter {
          background: linear-gradient(180deg, #5db14e, #388829);
        }}
         &::after {
          border:2px solid $ifp-color-blue-light-2;
        }
      }
       &--pending {
        .ifp-stepper{
          &__counter {
          background: linear-gradient(180deg, #338FFF, #1765C4);
          border: 3px solid #ADD2FF;
          color: $ifp-color-white;
        }
         &__name {
          color: $ifp-color-secondary-grey;
           font-weight: $fw-bold;
        }
      }

       }
    }
  }
   &--blue {
    .ifp-stepper {
      &__item {
        &::before,
        &::after {
          border-bottom: 4px solid
            rgba($color: $ifp-gen-pills-blue, $alpha: 0.07);
        }
      }
      &__counter {
        color: $ifp-color-grey-14;
        border: 1px solid $ifp-color-blue-light-2;
      }
      &__name {
        color: $ifp-color-grey-14;
        font-weight: $fw-regular;
        font-size: $ifp-fs-4;
      }

      &--completed {
        .ifp-stepper {
          &__counter {
            background-color: $ifp-gen-pills-blue;
            border: none;
          }
          &__count {
            display: block;
            color: $ifp-color-white;
          }
          &__name {
            font-weight: $fw-bold;
            color: $ifp-color-black;
          }
        }
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-stepper {
    &__item {
      &::before {
        right: -50%;
        left: auto;
      }
      &::after {
        right: 50%;
        left: auto;
      }
    }
  }
}
