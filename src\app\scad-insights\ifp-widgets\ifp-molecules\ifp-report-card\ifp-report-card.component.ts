import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { IfpAnalysisCardHeaderComponent } from '../ifp-analysis-card-header/ifp-analysis-card-header.component';
import { setNotificationUpdate, unsubscribeNotificationUpdate } from 'src/app/scad-insights/store/notification/notification.action';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonColor, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { IfpIconTextComponent } from '../ifp-icon-text/ifp-icon-text.component';
import { getIndicator } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { distinctUntilChanged } from 'rxjs';
import { SubSink } from 'subsink';
import { Router } from '@angular/router';
import { IfpCardLoaderComponent } from '../../ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpModalComponent } from '../../ifp-organism/ifp-modal/ifp-modal.component';
import { IfpAlertBoxComponent } from '../ifp-alert-box/ifp-alert-box.component';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { selectGetMappedData } from 'src/app/scad-insights/store/notification/notification.selector';
import { IfpDocumentDownloadComponent } from '../ifp-document-download/ifp-document-download.component';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { QuotRemove } from 'src/app/scad-insights/core/pipes/quotsRemove.pipe';
import { IfpCheckboxComponent } from '../../ifp-atoms/ifp-checkbox/ifp-checkbox.component';
import { IfpUnauthorizedCardComponent } from '../ifp-unauthorized-card/ifp-unauthorized-card.component';
import { NewsLetter } from 'src/app/scad-insights/core/interface/domain.interface';
import { IfpTncModalComponent } from '../../ifp-organism/ifp-tnc-modal/ifp-tnc-modal.component';
import { staticKeys } from 'src/app/scad-insights/core/constants/header.constants';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { Security } from 'src/app/scad-insights/core/interface/indicator.interface';
import { IfpIndicatorCardService } from 'src/app/scad-insights/core/services/indicator-card/ifp-indicator-card.service';
import { IfpTagComponent } from '../../../../ifp-analytics/atom/ifp-tag/ifp-tag.component';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'ifp-report-card',
    templateUrl: './ifp-report-card.component.html',
    styleUrls: ['./ifp-report-card.component.scss'],
    providers: [ShortNumberPipe, DatePipe, IfpButtonComponent, QuotRemove],
    imports: [TranslateModule, CommonModule, IfpAnalysisCardHeaderComponent, IfpButtonComponent, IfpIconTextComponent, IfpCardLoaderComponent, IfpModalComponent, IfpAlertBoxComponent, IfpDocumentDownloadComponent, QuotRemove, IfpCheckboxComponent, IfpUnauthorizedCardComponent, IfpTncModalComponent, IfpTagComponent]
})
export class IfpReportCardComponent implements OnInit, OnDestroy {

  @ViewChild('subscribeNotification') subscribeNotificationModal!: IfpModalComponent;
  @ViewChild('tncModal') tncModal!: IfpModalComponent;
  @Input() small: boolean = true;
  @Input() id!: string;
  @Input() contentType!: string;
  @Input() domains: { icon: string; color: string }[] | any = [];
  @Input() publish!: string | null;
  @Input() source!: string | null;
  @Input() addMyAppsLanding = false;
  @Input() addMyApps = false;
  @Input() isCustomNewsLetter: boolean = false;
  @Input() remove = false;
  @Input() isSelected: boolean = false;
  @Input() isAccessPending: boolean = false;
  @Input() isHideShareCheck: boolean = false;
  @Input() customNewsLetterData!: NewsLetter;
  @Input() dropdownSelection: string = 'news'; // New input for dropdown selection
  @Output() crossClick = new EventEmitter();
  @Output() myApps = new EventEmitter();
  @Output() selectReportIndicator: EventEmitter<{ status: boolean; id: string | number; type: string }> = new EventEmitter<{ status: boolean; id: string | number; type: string }>();

  public notificationSelector !: boolean;
  public type = '';
  public name: string = '';
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public dateFormat = dateFormat;
  public subs: SubSink = new SubSink();
  public data!: any;
  public img!: string;
  public description!: string;
  public url!: string;
  public myAppsStatus = false;
  public removeStatus = false;
  public addStatus = false;
  public queryParams: any;
  public loader: boolean | undefined = true;
  public openInNewTabEnable = false;
  public downloaderPdf: string[] = [];
  public downloaderExcel: string[] = [];
  public isSubscNotifOpen: boolean = false;
  public documentList: { type: string; files: string[] }[] = [];
  public tncState!: boolean;
  public buttonColor = buttonColor;
  public showDocuments: boolean = false;
  public enableNotification: boolean = false;
  public unauthorized: boolean = false;
  private sessionId!: string;
  public security!: Security;

  constructor(private store: Store, private _router: Router, private _cdr: ChangeDetectorRef, public _download: DownLoadService, private _commonApiService: CommonApiService, private _qutoRemove: QuotRemove, private log: UsageDashboardLogService, private _cardService: IfpIndicatorCardService) { }

  // Getter to determine the date label based on dropdown selection
  get dateLabel(): string {
    return this.dropdownSelection === 'publication' ? 'Published' : 'Updated Date';
  }

  // Getter to determine the tag text based on dropdown selection
  get dropdownTagText(): string {
    return this.dropdownSelection === 'publication' ? 'Publication' : 'Report';
  }

  // Getter to check if dropdown selection functionality is active
  get shouldShowDropdownTag(): boolean {
    // Show the tag when we have an explicit dropdown selection (not just the default)
    // We'll add a flag to track when dropdown functionality is active
    return this.isDropdownActive;
  }

  // Property to track if dropdown functionality is active
  @Input() isDropdownActive: boolean = false;

  ngOnInit() {
    if (!this.isCustomNewsLetter) {
      if (this.id) {
        this.store.dispatch(getIndicator({
          id: this.id,
          contentType: this.contentType
        }));
      }
      this.subs.add(this.store.select(selectGetMappedData(this.id)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(data => {
        this.notificationSelector = data[this.id]?.isNotification ? data[this.id].isNotification : false;
        this._cdr.detectChanges();
      }));

      this.subs.add(
        this.store.select(selectIndicatorGetById(this.id))
          .pipe(distinctUntilChanged((prev, curr) => prev.loader === curr.loader))
          .subscribe((data) => {
            if (data?.errorMessage?.error?.access?.toString() === 'false') {
              this.unauthorized = true;
            }
            if (data.status) {
              this.data = data.body;
              this.name = this._qutoRemove.transform(this.data?.component_title || this.data?.title);
              this.description = this.data?.component_subtitle || this.data?.subtitle;
              this.img = data.body?.imgSrc ? data.body?.imgSrc : '/assets/report.png';
              this.type = this.contentType;
              this.openInNewTabEnable = data.body.newsletter_URL ? true : false;
              this.downloaderPdf = data?.body?.publication_attachment ? data?.body?.publication_attachment : [];
              this.downloaderExcel = data?.body?.excel_attachment ? data?.body?.excel_attachment : [];
              this.publish = this.data?.publication_date ? this.data.publication_date : this.data?.updated;
              this.url = `common-dashboard/${this.id}`;
              this.domains = data?.body?.domain ? [data?.body?.domain] : data?.body?.domains;
              this.queryParams = { contentType: this.contentType };
              if (this.downloaderPdf.length) {
                this.documentList.push({
                  type: 'pdf',
                  files: [...this.downloaderPdf]
                });
              }
              if (this.downloaderExcel.length) {
                this.documentList.push({
                  type: 'excel',
                  files: [...this.downloaderExcel]
                });
              }
              if (this.data?.security && environment.env !== 'demo') {
                this.security = this._cardService.setSecurity(this.data.security);
              }
            }
            this.loader = data.loader;
            this._cdr.detectChanges();
          })
      );
      this.subs.add(this._commonApiService.getDownloadTermsStatus(this.id).subscribe((res: any) => {
        if (res) {
          if (res.status) {
            this.tncState = res.status;
          }
        }
      }));
    } else {
      this.img = this.customNewsLetterData?.image ? this.customNewsLetterData.image : '/assets/report.png';
      this.loader = false;
      this.name = this._qutoRemove.transform(this.customNewsLetterData.title);
      this.description = this.customNewsLetterData.subTitle;
      this.publish = this.customNewsLetterData.publicationDate;
    }
  }

  myappsEvent(event: any) {
    this.myApps.emit(event);
  }


  addNotification(current: boolean) {
    if (!current) {
      const data = { id: this.id, contentType: this.contentType, appType: this.type, emailStatus: false };
      this.store.dispatch(setNotificationUpdate(data));
      this.subscribeNotificationModal.createElement();
      this.isSubscNotifOpen = true;
      setTimeout(() => {
        this.subscribeNotificationModal.removeModal();
        this.isSubscNotifOpen = false;
      }, 5000);
    } else {
      this.store.dispatch(unsubscribeNotificationUpdate({ id: this.id }));
    }
  }

  closeModal() {
    this.subscribeNotificationModal.removeModal();
  }

  isTnCagreed(event: boolean) {
    this.tncState = event;
  }


  setEmailNotifStatus(status: boolean) {
    this.store.dispatch(setNotificationUpdate({ id: this.id, contentType: this.contentType, appType: this.type, emailStatus: status }));
    setTimeout(() => {
      this.subscribeNotificationModal.removeModal();
      this.isSubscNotifOpen = false;
    }, 400);
  }

  removeEvent() {
    this.crossClick.emit({ id: this.id, domain: this.domains?.[0].icon ? this.domains?.[0].icon : this.domains?.[0], contentType: this.contentType, title: this._qutoRemove.transform(this.name) });
  }

  selectNode(event: any) {
    this.selectReportIndicator.emit({ status: event.target.checked, id: this.id, type: this.contentType });
  }

  goToDetail() {
    this._router.navigate([this.url]);
  }

  openNewsLetter() {
    if (!this.isCustomNewsLetter) {
      return;
    }
    this.subs.add(this._commonApiService.getDownloadTermsStatus(staticKeys.newsLetter).subscribe((res: any) => {
      if (res.status) {
        if (this.sessionId) {
          this.log.logEnds(this.sessionId, this.log.currentTime );
        }
        this.sessionId = this.log.createUUid;
        this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, +this.customNewsLetterData.id ? +this.customNewsLetterData.id : +this.id);
        window.open(this.customNewsLetterData.publicationAttachment, '_blank');
        return;
      }
      setTimeout(() => {
        this.tncModal.createElement();
        this._cdr.detectChanges();
      }, 400);
    }));
  }


  termsResponse(event: any) {
    if (event) {
      this.subs.add(this._commonApiService.setDownloadTermsStatus(staticKeys.newsLetter).subscribe(_resp => {
        this.tncModal.removeModal();
        if (this.sessionId) {
          this.log.logEnds(this.sessionId, this.log.currentTime );
        }
        if (this.sessionId) {
          this.log.logEnds(this.sessionId, this.log.currentTime );
        }
        this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, +this.customNewsLetterData.id ? +this.customNewsLetterData.id : +this.id );
        window.open(this.customNewsLetterData.publicationAttachment, '_blank');
      }));
    } else {
      this.tncModal.removeModal();
    }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
