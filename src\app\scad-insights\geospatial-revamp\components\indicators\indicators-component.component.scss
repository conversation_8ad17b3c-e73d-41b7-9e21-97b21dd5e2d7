@use "../../../../../assets/ifp-styles/abstracts/index" as *;

.indicators-container {
  margin: 0px 30px;
  padding-right: 40px;
  padding-left: 40px;
}

@media only screen and (min-width: 1920px) {
  .indicators-container {
    margin: 0px;
  }
}

.right-indicators {
  z-index: 2;
}

.prevent-scrolling{
  cursor: not-allowed!important;
}

.right-indicators,
.left-indicators {
  .scroll-up,
  .scroll-down {
    padding: 8px;
  }
}

.left-indicators {
  z-index: 2;
}

.scroll-up,
.scroll-down {
  position: sticky;
  z-index: 999;
  text-align: center;
  cursor: pointer;
  &--disable {
    // pointer-events: none;
    cursor: not-allowed;
    .ifp-icon {
      opacity: 0.3;
    }
  }
}

.scroll-up {
  top: 0;
  // background-color: #ffffff99;
  padding: 5px;
  border-radius: 15px 15px 0 0;
}

.scroll-down {
  bottom: 0;
  @include geo-blur;
  // background-color: #ffffff99;
  margin-top: 3px;
  padding: 8px;
  border-radius: 0px 0px 15px 15px;
}

.geospatial-revamp-box {
  margin-top: 10px;
  z-index: 2;
  border-radius: 15px;
  height: 144px;
  background: #ffffff99;
  transition: height 0.5s ease, background 0.5s ease;
}

.geospatial-revamp-box .progress-bar-container {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
}

.geospatial-revamp-box .stat-section {
  display: flex;
  justify-content: space-between;
}

.geospatial-revamp-box .progress-bar-segment {
  height: 100%;
}

.geospatial-revamp-box .progress-bar-segment.marital {
  background-color: #28A8BA;
}

.geospatial-revamp-box .progress-bar-segment.remaining-marital {
  background-color: #F2F3F6;
}

.geospatial-revamp-box:hover {
  height: 187px;
  background: linear-gradient(129deg, #A6DFE9, #43B6CA);
}

.geospatial-revamp-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 15px;
}

.geospatial-revamp-box-url {
  display: flex;
  justify-content: end;
}

.geospatial-revamp-box-content {
  display: flex;
  justify-content: space-evenly;
}

.geospatial-revamp-box-content p {
  font-size: 18px;
  font-weight: bold;
}

.geospatial-revamp-map {
  height: 100%;
  border-radius: inherit;
  overflow: hidden;
}

.left-panel {
  width: 20%;
  height: calc(100% - 10px);
  position: absolute;
  left: 10px;
  z-index: 1;
  background: linear-gradient(163deg, #149ece, #39b7db);
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: background 0.3s ease;
}

.panel-item {
  color: white;
  padding: 20px 20px 20px 50px;
  margin: 5px 0;
  cursor: pointer;
  font-size: 30px;
  text-align: start;
  position: relative;
  transition: background 0.3s ease, margin 0.3s ease, transform 0.3s ease;
}

.panel-item.selected {
  margin-left: 25px;
  border-radius: 50px 0 0 50px;
  background-color: transparent;
  color: #1e2937;
  transform: scale(1.05);
  z-index: 2;
}

.panel-item.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  z-index: -1;
}

.left-panel.selected {
  background: linear-gradient(163deg, transparent, transparent);
}

.panel-item:not(.selected):hover {
  background: rgba(255, 255, 255, 0.1);
}

.geospatial-revamp-body-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 20%;
  height: calc(100% - 10px);
  z-index: 5000;
}

.main-container-top {
  flex-grow: 1;
  background: linear-gradient(87deg, #149ece, #39b7db);
}

.main-container-bottom {
  flex-grow: 1;
  background: linear-gradient(87deg, #149ece, #39b7db);
}

.panel-item-new {
  display: flex;
}

.panel-item-new span {
  flex-grow: 1;
  background: #149ece;
}

.panel-item-new p {
  flex-grow: 3;
  color: white;
  cursor: pointer;
  font-size: 25px;
  text-align: start;
  position: relative;
  background: linear-gradient(87deg, #149ece, #39b7db);
  padding: 20px;
  margin-left: -40px;
}

.panel-item-new.selected p {
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  padding: 10px;
  color: #1E2937;
  border-radius: 50px;
  margin-left: -40px;
}

.panel-item-new.selected span {
  background: linear-gradient(to right, rgba(20, 158, 206, 1) 70%, rgba(255, 255, 255, 1) 100%);
}

.upper-p-selected {
  border-bottom-right-radius: 50px;
}

.lower-p-selected {
  border-top-right-radius: 50px;
}

.ifp-geo-indicator {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;

  &__head-wrapper {
    @include geo-blur;
    &--customize {
      justify-content: space-between;
      display: flex;
      align-items: center;
    }
  }

  &__head-title,
  &__head-icon-wrapper {
    padding: ($spacer-1 + 2) $spacer-3;
  }

  &__head-title {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
  }

  &__head-icon-wrapper {
    .ifp-icon {
      margin: $spacer-0 $spacer-2;
      font-size: $ifp-fs-5;
    }
  }

  &__customize-header {
    background-color: #ffffff99;
    margin: 4px;
    padding: 5px;
    text-align: center;
    cursor: pointer;
    border-radius: 15px 15px 0px 0px;
  }

  &__customize-block {
    position: relative;
    padding: 0;
    z-index: 3;
    display: none;
    width: 40%;

    &--visible {
      display: block;
    }
  }

  &__cutom-cards {
    margin: $spacer-1;
    width: 100%;
    max-width: 254px;
    position: relative;
    &--move {
      cursor: pointer;
    }
  }

  &__cutom-cards-inner {
    max-height: 76vh;
    overflow-y: auto;
    overflow-x: hidden;
  }

  &__customize-block-header {
    padding: $spacer-2;
    width: 100%;
    max-width: 516px;
    margin: 0 auto;
    border-radius: 15px 15px 0 0;
    text-align: center;
    @include geo-blur;
  }

  &__custom-card-wrapper {
    display: flex;
    justify-content: center;
  }

  &__indicator {
    min-width: 200px;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    &--move {
      cursor: pointer;
    }
    &--delete {
      opacity: 0.5;
    }
  }

  &__indicator-card-wrapper {
    position: relative;
    z-index: 3;
    margin: 0 $spacer-2;
    width: 15%;
    min-width: 200px;
    // background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    box-sizing: content-box;

    &--move {
      max-width: 244px;
    }

    &--active {
      background-color: transparent;
    }
    &--empty {
      min-width: 200px;
    }
  }

  // Loading overlay styles
  &__loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(8px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    pointer-events: all;
    user-select: none;
  }

  &__loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacer-3;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #149ece;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: $ifp-fs-4;
      font-weight: $fw-semi-bold;
      color: #149ece;
      margin: 0;
    }
  }

  &__left-indicator-card-wrapper {
    min-height: 100vh;
    position: relative;
    z-index: 1;
    width: 15%;

    &--move {
      max-width: 244px;
    }

    &--active {
      background-color: transparent;
      margin-inline-end: auto;
    }
    &--delete {
      opacity: 0.5;
    }
  }

  &__right-indicator-card-wrapper {
    min-height: 100vh;
    position: relative;
    z-index: 1;
    width: 20%;
    max-width: 250px;
    &--move {
      max-width: 244px;
    }

    &--active {
      background-color: transparent;
      margin-inline-start: auto;
    }
  }

  &__scroll-down {
    &--delete {
      opacity: 0.5;
    }
  }

  &__add-card {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    margin: 0 auto;
    background-color: $ifp-color-white;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.8);

    .ifp-icon {
      position: relative;
      top: 6px;
    }
  }

  &__footer {
    padding: $spacer-3 80px;
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 3;
  }

  &__footer-wrapper {
    display: flex;
    align-items: flex-end;
    // justify-content: space-between;
     justify-content: space-around;
    margin: $spacer-0 (-$spacer-3);
  }

  &__footer-item {
    margin: $spacer-0 $spacer-3;
    width: 15%;
  }

  &__button {
    &--100 {
      &::ng-deep .ifp-btn {
        display: block;
        width: 100%;
      }
    }
  }

  &__footer-dropdown {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    .ifp-geo-indicator {
      &__dropdown {
        width: 100%;
      }
      &__button {
        margin-inline-start: $spacer-3;
      }
    }
    &::ng-deep {
      .ifp-dropdown {
        max-width: 100%;
        &__list {
          top: auto;
          bottom: 100%;
          margin-bottom: $spacer-2;
          transform-origin: bottom;
        }
        &__selected {
          .ifp-icon {
            margin-inline-start: $spacer-0;
            transition: 0.3s;
          }
        }
        &__title {
          font-size: $ifp-fs-3;
          color: $ifp-color-primary-grey;
          margin-top: $spacer-0;
        }
        &.active {
          .ifp-dropdown__selected .ifp-icon{
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  &__slider-text {
    font-weight: $fw-semi-bold;
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-2;
  }

  &__slider-value {
    margin-inline-start: $spacer-2;
  }

  &__slider {
    display: block;
    padding: $spacer-3;
    border-radius: 5px;
    border: 1px solid $ifp-color-grey-7;
    background-color: $ifp-color-white;
  }

  &__card {
    display: block;
  }
}

// Keyframe animation for spinner
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  background: #ccc;
  border: dotted 3px #999;
  min-height: 60px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.indicators-container .vertical-scroll{
  position: relative;
}

/* For WebKit browsers (Chrome, Safari) */
::-webkit-scrollbar {
  width: 0px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

:host::ng-deep {
  .ifp-geo-indicator {
    &__card {
      .geospatial-revamp-box {
        background: none;
      }
    }
  }
}

// responsive design style
@media screen and (max-width: 1600px) {
  .ifp-geo-indicator {
    &__indicator-card-wrapper {
        min-width: 285px!important;
        &--empty {
          min-width: 285px;
        }
    }
    &__indicator {
      min-width: 285px;
    }
  }
  .indicators-container {
    margin: 60px 30px;
     &__customize{
      margin: 10px 30px!important;
    }
  }
}



.empty-placeholder {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  border: 2px dashed #ddd;
  margin: 5px 0;
}