import {
  Component,
  Input,
  ElementRef,
  Renderer2,
  ChangeDetectorRef,
  AfterViewInit,
  OnInit,
  Output,
  EventEmitter,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  ViewChild
} from '@angular/core';
import { GeoPopulationComponent } from '../geo-population/geo-population.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpSectionComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-section/ifp-section.component';
import { MapComponent } from '../geo-components/map/map.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SubSink } from 'subsink';
import { ThemeService } from '../../core/services/theme/theme.service';
import { SharedService } from '../geo-services/shared.service';
import { IfpTooltipDirective } from '../../core/directives/ifp-tooltip.directive';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { IfpPdfTemplateComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-pdf-template/ifp-pdf-template.component';
import html2canvas from 'html2canvas';
import { SummaryComponent } from '../geo-components/summary/summary.component';
import { GeospatialNodeService } from '../../core/services/geospatial-node.service';
import { UserJourneyService } from 'src/app/scad-insights/core/services/userJourney/user-journey.service';

@Component({
  selector: 'app-geo-spatial',
  templateUrl: './geo-spatial.component.html',
  styleUrls: ['./geo-spatial.component.scss'],
  imports: [
    GeoPopulationComponent,
    TranslateModule,
    IfpSectionComponent,
    MapComponent,
    FormsModule,
    CommonModule,
    IfpTooltipDirective,
    IfpPdfTemplateComponent,
    IfpModalComponent,
    SummaryComponent
  ]
})
export class GeoSpatialComponent implements AfterViewInit, OnInit, OnDestroy {
  @ViewChild('modalSla', { static: true }) modalSla!: IfpModalComponent;
  @ViewChild('dragButton', { static: true }) dragButton!: ElementRef;
  @Input() addMyAppsLanding!: boolean;
  @Input() heading!: string;
  @Input() id!: string;
  @Input() contentType!: string;
  @Input() isAccessPending: boolean = false;
  @Input() isHideShareCheck: boolean = false;
  @Output() crossClick = new EventEmitter();
  @Output() selectGeoIndicator = new EventEmitter();

  @Input() title!: string;
  @Input() key!: string;

  public subs = new SubSink();
  rangeValue: number = 40;
  isFullscreen = false;
  showMap = false;
  backgroundColor: string = 'transparent';

  widthChange1: string = '40%';
  widthChange2: string = '60%';
  fullScreenClass: string = 'bi bi-arrows-fullscreen';
  fullsectionPadding: string = '';
  mainComponentHeight: string = '';
  // sectionHeight: string = "90%";
  subsink: SubSink = new SubSink();
  checkTheme: string = '';
  private lastKey: string | null = null;
  public previewUrl: any;
  public viewDownload: boolean = false;
  chartName: string = 'Population Overview';
  isDragging: boolean = false;
  leftFlex: number = 0.4;
  rightFlex: number = 0.6;
  // changeWidth() {
  //   this.widthChange = "10%";
  // }

  displaySpatialAnalytics: boolean = true;
  public mapUnderMaintenanace: boolean = false;

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef,
    public themeService: ThemeService,
    private sharedService: SharedService,
    private downloadService: DownloadService,
    private geospatialNode: GeospatialNodeService,
    private _journyService: UserJourneyService
  ) {
    this.sharedService.geoSelectChanged.subscribe((newValue: any) => {
      this.viewDownload = false;
      this.chartName = newValue == 1 ? 'Job Seeker' : (newValue == 2 ? 'Residential Population' : 'Population Overview');
      this.previewUrl = undefined;
      setTimeout(async () => {
        const divElement: any = document.getElementById('print');
        const element: any = await html2canvas(divElement, {
          scale: 1.5
        });
        this.previewUrl = this.processImage(element);
      }, 5000);
    });

    this.downloadService.createChartData$.subscribe(_resp => {
      this.previewUrl = undefined;
      this.createChartBase64Data();
    });

    this.geospatialNode.checkStatus().subscribe({
      next: (event) => {
        this.displaySpatialAnalytics = true;
      },
      error: (err) => {
        this.displaySpatialAnalytics = false;
      }
    });
  }

  @HostListener('document:mousemove', ['$event'])
  doDrag(event: MouseEvent): void {
    if (this.isDragging) {
      const totalWidth = window.innerWidth;
      const buttonWidth = 30;
      const newLeftWidth = event.clientX - buttonWidth / 2;

      let ratio = newLeftWidth / (totalWidth - buttonWidth);
      if (ratio <= 0.35) {
        ratio = 0.35;
      } else if (ratio >= 0.65) {
        ratio = 0.65;
      }
      this.leftFlex = ratio;
      this.rightFlex = 1 - ratio;
    }
  }

  @HostListener('document:mouseup', ['$event'])
  stopDrag(event: MouseEvent): void {
    if (this.isDragging) {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.doDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    }
  }

  ngOnInit(): void {
    this.renderer.addClass(this.elementRef.nativeElement.parentNode, 'ifp-geo-card');
    this.subsink.add(
      this.themeService.defaultTheme$.subscribe((d: any) => {
        this.checkTheme = d;
      }),
      this._journyService.exploreClicked.subscribe((resp: string | null) => {
        if (resp == 'fullscreen') {
          this.toggleFullScreen();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
  }

  removeEvent() {
    this.crossClick.emit({ id: this.id, contentType: this.contentType, title: this.heading });
  }

  selectNodeEvent() {
    this.selectGeoIndicator.emit({ id: this.id, contentType: this.contentType, title: this.heading, type: this.contentType });
  }


  // eslint-disable-next-line require-await
  async ngAfterViewInit(): Promise<void> {
    setTimeout(() => {
      this.showMap = true;
      this.cdr.detectChanges();
    }, 2000);
    this.createChartBase64Data();
  }


  createChartBase64Data() {
    setTimeout(async () => {
      const divElement: any = document.getElementById('print');
      const element: any = await html2canvas(divElement, { scale: 1.5 });
      this.previewUrl = this.processImage(element);
      this.downloadService.mapBase64url = this.previewUrl;
    }, 6000);
  }

  @HostListener('document:fullscreenchange', ['$event']) onKeydownHandler(
    event: KeyboardEvent
  ) {
    // has exited
    if (!document.fullscreenElement) {
      this.fullScreenClass = 'bi bi-arrows-fullscreen';
      this.fullsectionPadding = '0';
      this.mainComponentHeight = '100%';
      this.backgroundColor = 'transparent';
      this.isFullscreen = false;
    }
  }

  toggleFullScreen() {
    const elem = this.elementRef.nativeElement;

    if (!document.fullscreenElement) {
      this.isFullscreen = true;
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) { /* Firefox */
        elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) { /* Chrome, Safari, Opera */
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) { /* IE/Edge */
        elem.msRequestFullscreen();
      }
      // this.renderer.setStyle(elem, 'height', '100vh');
      // this.sectionHeight = "200%";
      this.fullScreenClass = 'bi bi-fullscreen-exit';
      this.fullsectionPadding = '20px';
      this.mainComponentHeight = '80%';
      if (this.checkTheme === 'dark') {
        this.backgroundColor = '#020202';
      } else {
        this.backgroundColor = '#FFFFFF';
      }
    } else {
      this.isFullscreen = false;
      if (document.exitFullscreen) {
        document.exitFullscreen();

      } else if (document.mozCancelFullScreen) { /* Firefox */

        document.mozCancelFullScreen();
        // @ts-expect-error
      } else if (document.webkitExitFullscreen) { /* Chrome, Safari, Opera */
        // @ts-expect-error
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) { /* IE/Edge */

        document.msExitFullscreen();
      }
      // this.renderer.removeStyle(elem, 'height');
      // this.sectionHeight = "90%";
      this.fullScreenClass = 'bi bi-arrows-fullscreen';
      this.fullsectionPadding = '0';
      this.mainComponentHeight = '100%';
      this.backgroundColor = 'transparent';
    }
  }

  onSliderChange(event: Event) {
    const newValue = (event.target as HTMLInputElement).valueAsNumber;
    // // this.renderer.setStyle(document.querySelector('.ifp-section__left'), 'width', newValue + "% !important");
    // const ifpSectionLeft = document.querySelector('.ifp-section__left');
    // // @ts-ignore
    // // @ts-ignore
    // ifpSectionLeft.style.width = newValue + '%';
    this.widthChange1 = `${newValue}%`;
    const nextValue = 100 - newValue;
    this.widthChange2 = `${nextValue}%`;
  }

  processImage(image: HTMLImageElement) {
    const canvas = document.createElement('canvas');
    canvas.width = image.width;
    canvas.height = image.height;

    const ctx = canvas.getContext('2d');
    ctx?.drawImage(image, 0, 0);

    // Convert canvas data to base64
    const base64Data = canvas.toDataURL('image/jpeg'); // or 'image/png' for PNG format

    return base64Data;
  }


  // eslint-disable-next-line require-await
  async downloadClick(type: any) {
    if (type != 'xl') {
      this.modalSla.createElement();
      setTimeout(() => {
        // this.modalSla.removeModal();
      }, 1000);
    }


    if (type == 'pdf') {
      setTimeout(() => {
        const offsetHeight = document.getElementById('downloadPrint')?.offsetHeight;
        const offsetWidth = document.getElementById('downloadPrint')?.offsetWidth;
        this.downloadService.downloadPdf(document.getElementById('downloadPrint'), this.chartName, offsetHeight, offsetWidth).then(_resp => {
          this.modalSla.removeModal();
        });

      }, 1000);
    }


    if (type == 'xl') {
      // const downloadData: any = [];

      let respOne: any = [];
      let respTwo: any = [];

      this.subsink.add(
        this.sharedService._popByGenderOriginal$.subscribe((data_one) => {
          if (data_one) {
            respOne = data_one;
          }
        }),
        this.sharedService._popByNationalsOriginal$.subscribe((data: any) => {
          if (data) {
            respTwo = data;
          }
        })
      );
      const data: any = { ...respOne, ...respTwo };
      this.downloadService.exportToExcel([data], this.chartName);
    }

    if (type == 'png') {
      setTimeout(() => {
        this.downloadService.downloadElementAsPNG(document.getElementById('downloadPrint'), this.chartName);
        this.modalSla.removeModal();
      }, 1000);
    }
  }

  startDrag(event: MouseEvent): void {
    this.isDragging = true;
    document.addEventListener('mousemove', this.doDrag);
    document.addEventListener('mouseup', this.stopDrag);
  }

}
