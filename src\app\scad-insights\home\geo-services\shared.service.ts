/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/prefer-ts-expect-error */
import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import * as Highcharts from 'highcharts';
import HighchartsMore from 'highcharts/highcharts-more';
import HighchartsExporting from 'highcharts/modules/exporting';
import HighchartsData from 'highcharts/modules/data';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import uniqueValues from '@arcgis/core/smartMapping/statistics/uniqueValues';
HighchartsMore(Highcharts);
HighchartsExporting(Highcharts);
HighchartsData(Highcharts);
import { Subject, BehaviorSubject } from 'rxjs';
import {environment} from '../../../../environments/environment';
import MapView from '@arcgis/core/views/MapView';
import Expand from '@arcgis/core/widgets/Expand';
import GroupLayer from '@arcgis/core/layers/GroupLayer';
import SimpleFillSymbol from '@arcgis/core/symbols/SimpleFillSymbol';
import { TranslateService } from '@ngx-translate/core';
import {GeospatialNodeService} from '../../core/services/geospatial-node.service';
import esriConfig from '@arcgis/core/config';
import Query from '@arcgis/core/rest/support/Query.js';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { UsageDashboardLogService } from '../../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../../core/services/usage-dashboard-log/usage-dashboard.constants';

@Injectable({
  providedIn: 'root'
})
export class SharedService {
  isRealEstate: boolean = false;
  apiHost: any = `${environment.baseUrl}/api`;
  mapConfig: any = {};
  cmsConfig: any = {};

  public sessionId!: string;

  public bn = 'BUILDING_NUMBER';
  public unFigure = 'UNIT_NUMBER';
  public un = 'NUMBER_UNIT';
  public bndc = 'BUILDING_NUMBER_DC';
  public undcFigure = 'UNIT_NUMBER_DC';
  public undc = 'NUMBER_UNIT_DC';

  public total_citizen = 'total_citizen';
  public total_citizen_dc = 'total_citizen_dc';
  public total_non_citizen = 'total_non_citizen';
  public total_non_citizen_dc = 'total_non_citizen_dc';
  public total_population = 'total_population';
  public total_population_dc = 'total_population_dc';
  public censusMale_dc = 'male_dc';
  public censusMale = 'male';
  public censusFemale_dc = 'female_dc';
  public censusFemale = 'female';

  public homeClicked: number = 0;

  private _view: MapView | undefined;
  set view(view: MapView) {
    this._view = view;
  }

  get view(): MapView {
    return this._view!;
  }

  public colors: any = [
    { name: 'EMIRATI', color: '#d75d5d' },
    { name: 'Non-EMIRATI', color: '#584cde' }
  ];

  public fields = ['uae', 'non_uae', 'total_population'];

  public nationalNames: any = {
    database: {
      nonUae: 'NON_UAE',
      uae: 'UAE'
    },
    toReplace: {
      nonUae: 'Non-EMIRATI',
      uae: 'EMIRATI'
    }
  };

  public SCAD_D_ID_SJ: any = '999';

  public dotDensityRendererByService: any;

  // @ts-ignore
  public _dataUpdatedDate = new BehaviorSubject<any>();
  _dataUpdatedDate$ = this._dataUpdatedDate.asObservable();
  dataUpdatedDate(dataUpdatedDate: any) {
    this._dataUpdatedDate.next(dataUpdatedDate);
  }

  // @ts-ignore
  public _summaryData = new BehaviorSubject<any>();
  _summaryData$ = this._summaryData.asObservable();
  summaryData(summaryData: any) {
    this._summaryData.next(summaryData);
  }

  // @ts-ignore
  public _dispalySummaryData = new BehaviorSubject<any>();
  _dispalySummaryData$ = this._dispalySummaryData.asObservable();
  dispalySummaryData(dispalySummaryData: any) {
    this._dispalySummaryData.next(dispalySummaryData);
  }

  public _methodolgyUrl: any[] = [];

  // @ts-ignore
  public _popByGenderOriginal = new BehaviorSubject<any>();
  _popByGenderOriginal$ = this._popByGenderOriginal.asObservable();
  setPopByGenderOriginal(popByGender: any) {
    this._popByGenderOriginal.next(popByGender);
  }

  // @ts-ignore
  public _districtForRegion = new BehaviorSubject<any>();
  _districtForRegion$ = this._districtForRegion.asObservable();
  setDistrictForRegion(districtForRegion: any) {
    this._districtForRegion.next(districtForRegion);
  }

  // @ts-ignore
  public _realestateDistrict = new BehaviorSubject<any>();
  _realestateDistrict$ = this._realestateDistrict.asObservable();
  setRealestateDistrict(realestateDistrict: any) {
    this._realestateDistrict.next(realestateDistrict);
  }

  // @ts-ignore
  public _realestateCensusDistrict = new BehaviorSubject<any>();
  _realestateCensusDistrict$ = this._realestateCensusDistrict.asObservable();
  setRealestateCensusDistrict(realestateCensusDistrict: any) {
    this._realestateCensusDistrict.next(realestateCensusDistrict);
  }

  // @ts-ignore
  public _regionsForNationality = new BehaviorSubject<any>();
  _regionsForNationality$ = this._regionsForNationality.asObservable();
  setRegionsForNationality(regionsForNationality: any) {
    this._regionsForNationality.next(regionsForNationality);
  }


  public _nationalityChartDivToDisplay = new BehaviorSubject<boolean>(false);
  _nationalityChartDivToDisplay$ = this._nationalityChartDivToDisplay.asObservable();
  setNationalityChartDivToDisplay(nationalityChartDivToDisplay: any) {
    this._nationalityChartDivToDisplay.next(nationalityChartDivToDisplay);
  }

  // @ts-ignore
  public _popByNationalsOriginal = new BehaviorSubject<any>();
  _popByNationalsOriginal$ = this._popByNationalsOriginal.asObservable();
  setPopByNationalsOriginal(popByGender: any) {
    this._popByNationalsOriginal.next(popByGender);
  }

  // @ts-ignore
  public _mainCheckboxes = new BehaviorSubject<any>();
  _mainCheckboxes$ = this._mainCheckboxes.asObservable();
  setMainCheckboxes(mainCheckboxes: any) {
    this._mainCheckboxes.next(mainCheckboxes);
  }

  private authStatusForServices = new BehaviorSubject<boolean>(false);
  authStatusForServices$ = this.authStatusForServices.asObservable();
  setAuthenticatedauthStatusForServices(authenticated: boolean) {
    this.authStatusForServices.next(authenticated);
  }

  /**
   * For Job seekers Subject Variable start
   **/
  // @ts-ignore
  public _JobHighDegreeSpec = new BehaviorSubject<any>();
  _JobHighDegreeSpec$ = this._JobHighDegreeSpec.asObservable();
  setJobHighDegreeSpec(jobHighDegreeSpec: any) {
    this._JobHighDegreeSpec.next(jobHighDegreeSpec);
  }

  // @ts-ignore
  public _VacHighDegreeSpec = new BehaviorSubject<any>();
  _VacHighDegreeSpec$ = this._VacHighDegreeSpec.asObservable();
  setVacHighDegreeSpec(vacHighDegreeSpec: any) {
    this._JobHighDegreeSpec.next(vacHighDegreeSpec);
  }

  // @ts-ignore
  public _JobYearsOfExperience = new BehaviorSubject<any>();
  _JobYearsOfExperience$ = this._JobYearsOfExperience.asObservable();
  setJobYearsOfExperience(jobYearsOfExperience: any) {
    this._JobYearsOfExperience.next(jobYearsOfExperience);
  }

  // @ts-ignore
  public _JobVsVac = new BehaviorSubject<any>();
  _JobVsVac$ = this._JobVsVac.asObservable();
  setJobVsVac(jobVsVac: any) {
    this._JobVsVac.next(jobVsVac);
  }

  /**
   * For Job seekers Subject Variable end
   **/

  private dataSubject = new Subject<any[]>();
  public loadedChart$ = new Subject<boolean>();
  public data$ = this.dataSubject.asObservable();

  public allAddedLayers: any = [];

  geoSelectChanged = new EventEmitter<any>();
  private _geoValue: any = '3';

  get geoValue() {
    return this._geoValue;
  }

  set geoValue(newGeoValue: any) {
    this._geoValue = newGeoValue;
    this.geoSelectChanged.emit(newGeoValue);
  }

  displayDistrictNameChanged = new EventEmitter<any>();
  private _displayDistrictName: any = 'All';
  get displayDistrictName() {
    return this.geoSelectChanged;
  }

  set displayDistrictName(newdisplayDistrictName: any) {
    this._displayDistrictName = newdisplayDistrictName;
    this.displayDistrictNameChanged.emit(newdisplayDistrictName);
  }

  mainFiguresChanged = new EventEmitter<any>();
  private _mainFigures: any;
  get mainFigures() {
    return this._mainFigures;
  }

  set mainFigures(newMainFigures: any) {
    this._mainFigures = newMainFigures;
    this.mainFiguresChanged.emit(newMainFigures);
  }

  officialFiguresChanged = new EventEmitter<any>();
  private _officialFigures: any;
  get officialFigures() {
    return this._officialFigures;
  }

  set officialFigures(newOfficialFigures: any) {
    this._officialFigures = newOfficialFigures;
    this.officialFiguresChanged.emit(newOfficialFigures);
  }

  officialFiguresCensusChanged = new EventEmitter<any>();
  private _officialFiguresCensus: any;
  get officialFiguresCensus() {
    return this._officialFiguresCensus;
  }

  set officialFiguresCensus(newOfficialFiguresCensus: any) {
    this._officialFiguresCensus = newOfficialFiguresCensus;
    this.officialFiguresCensusChanged.emit(newOfficialFiguresCensus);
  }

  updateData(data: any[]) {
    this.dataSubject.next(data);
  }

  public mainHighcharts: any;
  public specializationHighcharts: any;
  public chartOptions: Highcharts.Options | undefined;
  public popGenderPieChart: any;
  public popNationalityPieChart: any;
  public highChartsArray: any  = [];
  public showJobSeekers: boolean = true;
  public showHousehold: boolean = true;

  constructor(
    private http: HttpClient,
    private _translate: TranslateService,
    private geospatialNode: GeospatialNodeService,
    public _msal: IFPMsalService,
    private log: UsageDashboardLogService
  ) {
    this.getServicesFromCMS();
    this.isRealEstate = environment.isEnableRealEstate;

    if(environment.baseUrl == 'https://ifp-dev.scad.gov.ae' || environment.baseUrl == 'https://ifp-demo.scad.gov.ae') {
      this.bn = 'building_number';
      this.unFigure = 'unit_number';
      this.un = 'number_unit';
      this.bndc = 'building_number_dc';
      this.undcFigure = 'unit_number_dc';
      this.undc = 'number_unit_dc';
    } else {
      this.bn = 'BUILDING_NUMBER';
      this.unFigure = 'UNIT_NUMBER';
      this.un = 'NUMBER_UNIT';
      this.bndc = 'BUILDING_NUMBER_DC';
      this.undcFigure = 'UNIT_NUMBER_DC';
      this.undc = 'NUMBER_UNIT_DC';
    }

  }

  getServicesFromCMS() {
    this.geospatialNode.getNodeData().subscribe((nodes: any) => {
      const householdLayers: any = [];
      const JobSeekersLayers: any = [];
      const populationLayers: any = [];
      const populationLayersOld: any = [];
      const popByRegion: any = [];
      const realEstateFlatTrans: any = [];
      const censusrealEstate: any = [];
      const otherLayers: any = [];
      const cmsNode = nodes[0];
      const modules = cmsNode.modules;

      modules.map((module: any) => {
        const layers = module.layers;
        if (module.key == 2) {
          layers.map((layer: any) => {
            householdLayers.push(layer.endpoint);
          });
        } else if (module.key == 3) {
          layers.map((layer: any) => {
            populationLayers.push(layer.endpoint);
          });
        } else if (module.key == 1) {
          layers.map((layer: any) => {
            JobSeekersLayers.push(layer.endpoint);
          });
        } else if (module.key == 4) {
          layers.map((layer: any) => {
            popByRegion.push(layer.endpoint);
          });
        } else if (module.key == 5) {
          layers.map((layer: any) => {
            realEstateFlatTrans.push(layer.endpoint);
          });
        } else if (module.key == 6) {
          layers.map((layer: any) => {
            censusrealEstate.push(layer.endpoint);
          });
        } else if (module.key == 7) {
          layers.map((layer: any) => {
            populationLayersOld.push(layer.endpoint);
          });
        }
        const obj = {
          name: module.name,
          pdf: module.attachment_pdf,
          key: module.key,
          order: module.configuration.order,
          display: module.configuration.display,
          indicator: module.configuration.indicator,
          displayCheckboxes: module.configuration.displayCheckboxes,
          security: module.configuration.security
        };
        this._methodolgyUrl.push(obj);
        if (module.configuration.order == 1) {
          this.geoValue = module.key;
        }
        // if(obj.key != "4") {
        //
        // }
        // for initially loading the module
        // if(module.configuration.order == 1) {
        //   this.geoValue = module.key;
        // }
      });
      const commonLayers = cmsNode.commonLayers;
      commonLayers.map((commonLayer: any) => {
        otherLayers.push({
          name: commonLayer.name,
          url: commonLayer.endpoint,
          icon: commonLayer.light_icon,
          darkIcon: commonLayer.dark_icon
        });
      });
      const customLayers = cmsNode.customLayers;
      const obj = {
        basemapLightPortal: [
          cmsNode.baseMapLightPortal
        ],
        basemapDarkPortal: [
          cmsNode.baseMapDarkPortal
        ],
        layers: householdLayers,
        jobSeekerLayers: JobSeekersLayers,
        dotDensityLayers: populationLayers,
        dotDensityLayersOld: populationLayersOld,
        popByRegion: popByRegion,
        realEstateFlatTrans: realEstateFlatTrans,
        censusrealEstate: censusrealEstate,
        otherLayers: otherLayers,
        sourseAndUpdatedDate: [customLayers[0].endpoint],
        hideLayersFromLayerList: [
          'Shapes', '', 'LabourLocations', 'Population XY', 'LabourLocations', 'Official Population Overview', 'DotDensitySeaRemoved', 'mismatch', 'jobseeker1', 'jobseeker2', 'jobvacancies'
        ]
      };
      if (environment.baseUrl == 'https://ifp-demo.scad.gov.ae') {
        this.mapConfig = {};
        this.mapConfig = obj;
        this.showJobSeekers = false;
        this.showHousehold = true;
      } else if (environment.baseUrl == 'https://ifp-beta.scad.gov.ae' || environment.baseUrl == 'https://ifp.scad.gov.ae') {
        this.mapConfig = {};
        this.mapConfig = obj;
        this.showJobSeekers = false;
        this.showHousehold = false;
      } else {
        this.mapConfig = {};
        this.mapConfig = obj;
        this.showJobSeekers = true;
      }
    });
  }

  setDarkChart() {
    // @ts-ignore
    const darkMode = {
      // colors: ['#90CAF9', '#64B5F6', '#42A5F5', '#2196F3', '#1E88E5', '#1976D2', '#1565C0', '#0D47A1'], // Update with your desired colors
      chart: {
        backgroundColor: '#212121', // Update with your desired background color
        style: {
          fontFamily: '\'Roboto\', sans-serif' // Update with your desired font
        }
      },
      legend: {
        itemStyle: {
          color: '#FFFFFF' // Update with your desired legend text color
        }
      },
      xAxis: {
        gridLineColor: '#757575', // Update with your desired grid line color
        labels: {
          style: {
            color: '#FFFFFF' // Update with your desired label color
          }
        }
      },
      yAxis: {
        gridLineColor: '#757575', // Update with your desired grid line color
        labels: {
          style: {
            color: '#FFFFFF' // Update with your desired label color
          }
        }
      },
      plotOptions: {
        series: {
          borderColor: '#FFFFFF' // Update with your desired series border color
        }
      }
    };
    // @ts-ignore
    this.mainHighcharts.update(darkMode);
  }

  setLightChart() {
    // @ts-ignore
    const darkMode = {
      chart: {
        backgroundColor: '#FFFFFF',
        style: {
          fontFamily: '\'Roboto\', sans-serif'
        }
      },
      legend: {
        itemStyle: {
          color: '#212121'
        }
      },
      xAxis: {
        gridLineColor: '#DEE2E6',
        labels: {
          style: {
            color: '#212121'
          }
        }
      },
      yAxis: {
        gridLineColor: '#DEE2E6',
        labels: {
          style: {
            color: '#212121'
          }
        }
      },
      plotOptions: {
        series: {
          borderColor: '#212121'
        }
      }
    };
    // @ts-ignore
    this.mainHighcharts.update(darkMode);
  }

  // @ts-ignore
  getDistinctValues(layerUrl, fieldName) {
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });

    // @ts-ignore
    return uniqueValues({
      layer: layer,
      field: fieldName
    });

  }

  async getDistinctDistrictsWithIDs(layerUrl: any, outFields: any) {
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: outFields
    });

    const query = new Query();
    query.returnGeometry = false;
    query.outFields = outFields;
    query.where = '1=1';
    query.orderByFields = [`${outFields[0]} ASC`];

    try {
      const results = await layer.queryFeatures(query);

      const uniqueDistricts = {};

      results.features.forEach((feature) => {
        const districtName = feature.attributes[outFields[0]];
        const districtID = feature.attributes[outFields[1]];

        // Use the district name as a key to ensure uniqueness and store the ID
        // @ts-ignore
        if (!uniqueDistricts[districtName]) {
          // @ts-ignore
          uniqueDistricts[districtName] = districtID;
        }
      });

      // Convert the uniqueDistricts object into the desired array format
      const uniqueArray = Object.keys(uniqueDistricts).map(districtName => ({
        districts_name: districtName,
        // @ts-ignore
        district_id: uniqueDistricts[districtName]
      }));

      return uniqueArray;
    } catch (error) {
      console.error('An error occurred while querying features: ', error);
      throw error; // Rethrowing error to be handled by the calling function
    }
  }

  getSingleFeatureWithGeom(layerUrl: any, whereClause: any) {
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });
    const query = layer.createQuery();
    query.where = whereClause;
    query.returnGeometry = true;
    return layer.queryFeatures(query);
  }

  getSingleFeatureWithoutGeom(layerUrl: any, whereClause: any) {
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });
    const query = layer.createQuery();
    query.where = whereClause;
    query.returnGeometry = false;
    return layer.queryFeatures(query);
  }

  getSumOfField(field: any, groupByField: any, layerUrl: any, whereClause: any) {

    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });

    const query = layer.createQuery();
    query.where = whereClause;
    query.outFields = [field, groupByField];
    query.groupByFieldsForStatistics = [groupByField];
    query.returnGeometry = false;
    const sumPopulation = {
      onStatisticField: field,
      outStatisticFieldName: `total_${field}`,
      statisticType: 'sum'
    };

    // query.groupByFieldsForStatistics = ["age"];
    // @ts-ignore
    query.outStatistics = [sumPopulation];
    return layer.queryFeatures(query);
  }

  getAvgOfField(field: any, groupByField: any, layerUrl: any, whereClause: any) {

    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });

    const query = layer.createQuery();
    query.where = whereClause;
    query.outFields = [field, groupByField];
    query.groupByFieldsForStatistics = [groupByField];
    query.returnGeometry = false;
    const sumPopulation = {
      onStatisticField: field,
      outStatisticFieldName: `total_${field}`,
      statisticType: 'avg'
    };

    // query.groupByFieldsForStatistics = ["age"];
    // @ts-ignore
    query.outStatistics = [sumPopulation];
    return layer.queryFeatures(query);
  }

  getSumIndividual(field: any[], layerUrl: any, whereClause: any, statType: any, queryGeometry: any) {
    const objArray: any = [];
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });

    const query = layer.createQuery();
    query.where = whereClause;
    query.geometry = queryGeometry;
    query.returnGeometry = false;
    query.outFields = field;
    field.forEach((d: any) => {
      const sumPopulation = {
        onStatisticField: d,
        outStatisticFieldName: d,
        statisticType: statType
      };
      objArray.push(sumPopulation);
    });

    query.outStatistics = objArray;
    return layer.queryFeatures(query);
  }

  getSumIndividualWithGroupBy(field: any[], groupBy:any, layerUrl: any, whereClause: any, statType: any, queryGeometry: any) {
    const objArray: any = [];
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });

    const query = layer.createQuery();
    query.where = whereClause;
    query.geometry = queryGeometry;
    query.returnGeometry = false;
    query.outFields = field;
    query.groupByFieldsForStatistics = groupBy;
    field.forEach((d: any) => {
      const sumPopulation = {
        onStatisticField: d,
        outStatisticFieldName: d,
        statisticType: statType
      };
      objArray.push(sumPopulation);
    });

    query.outStatistics = objArray;
    return layer.queryFeatures(query);
  }

  createJobSeekersByYearsOfExperience(whereClause: any) {
    const allObjs: any = [];
    const categories: any = [];
    const catData: any = [];
    this.getCountField('moe_he_grad_eucation_isced', 'moe_he_grad_eucation_isced', this.mapConfig.jobSeekerLayers[1], whereClause).then((response: any) => {
      response.features.map((result: any) => {
        if (result.attributes.moe_he_grad_eucation_isced === null) {

        } else {
          const obj = {
            name: result.attributes.moe_he_grad_eucation_isced,
            y: result.attributes.total_moe_he_grad_eucation_isced
          };
          allObjs.push(obj);
        }
      });
      allObjs.sort((a: any, b: any) => b.y - a.y);
      const top10 = allObjs.slice(0, 10);
      top10.forEach((d: any) => {
        categories.push(d.name);
        catData.push(parseFloat(d.y));
        // catData.push(this.getRandomInt(1, 1000));
      });
      this.highChartsArray.forEach((charts: any) => {
        if (charts.name == 'SPECIALIZATION') {
          charts.chartOptions.xAxis.categories = categories;
          charts.chartOptions.series[0].data = catData;
          charts.mainChart.update(charts.chartOptions);
        }
      });
    });
  }

  getCountField(field: any, groupByField: any, layerUrl: any, whereClause: any) {
    const layer = new FeatureLayer({
      url: layerUrl,
      outFields: ['*']
    });

    const query = layer.createQuery();
    query.where = whereClause;
    query.outFields = [field, groupByField];
    query.groupByFieldsForStatistics = [groupByField];
    query.returnGeometry = false;
    const sumPopulation = {
      onStatisticField: field,
      outStatisticFieldName: `total_${field}`,
      statisticType: 'count'
    };

    // query.groupByFieldsForStatistics = ["age"];
    // @ts-ignore
    query.outStatistics = [sumPopulation];
    return layer.queryFeatures(query);
  }

  generateTokenByBackend() {
    const currentLang = localStorage.getItem('LanguagePreference');
    const token = localStorage.getItem('userData');
    // @ts-ignore
    const parseToken = JSON.parse(token);
    const headers = { 'Authorization': `Bearer ${parseToken.idToken}`, 'Accept-Language': 'en' };
    // @ts-ignore
    return this.http.get<any>(`${this.apiHost}/arc-gis-auth`, { headers: headers });
  }

  getRandomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  humanize(str: any) {
    const frags = str.split('_');
    for (let i=0; i<frags.length; i++) {
      frags[i] = frags[i].charAt(0).toUpperCase() + frags[i].slice(1);
    }
    return frags.join(' ');
  }

  capitalizeFirstLetter(text: any) {
    const lowerLetters = text.toLowerCase();
    return this.humanize(lowerLetters.charAt(0).toUpperCase() + lowerLetters.slice(1));
  }

  allCapitalize(text: any) {
    const lowerLetters = text.toLowerCase();
    const tomakeUpper = this.humanize(lowerLetters.charAt(0).toUpperCase() + lowerLetters.slice(1));
    return tomakeUpper.toUpperCase();
  }

  createCustomLayerlistWidget(map: any, position: any) {
    const customLayerDiv = document.createElement('div');
    // @ts-ignore
    customLayerDiv.appendChild(document.getElementById('customContent'));
    // customLayerDiv.appendChild(titleDiv);
    // customLayerDiv.appendChild(nationalityDiv);
    customLayerDiv.id = 'customLayersContainer';
    const myGroupLayers = this.getGrouplayers(map);
    let iconUrl = '';
    const allGroupLayers = myGroupLayers[0].layers._items;
    allGroupLayers.forEach((layer: any) => {
      if (layer.type === 'feature' && layer.renderer && layer.renderer.symbol) {
        const symbol = layer.renderer.symbol;
        if (symbol.type === 'picture-marker') {
          iconUrl = symbol.url;
        }
      }
      const div = document.createElement('div');
      div.className = 'checkbox-container';
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = layer.visible;

      const label = document.createElement('label');
      label.innerHTML = layer.title;
      label.style.flex = '2';

      const img = document.createElement('img');
      img.src = iconUrl;
      img.alt = 'Thumbnail';
      img.width = 15;

      checkbox.addEventListener('change', (event) => {
        layer.visible = checkbox.checked;
      });

      div.appendChild(img);
      div.appendChild(label);
      div.appendChild(checkbox);
      customLayerDiv.appendChild(div);
    });
    const layerListExpand = new Expand({
      view: this._view,
      content: customLayerDiv,
      expanded: false,
      expandIconClass: 'bi bi-layers fs-5',
      // expandIcon: 'bi bi-layers fs-5',
      expandTooltip: this._translate.instant('Layer list')
    });
    // @ts-ignore
    this._view.ui.add(layerListExpand, {
      position: position
    });
  }

  getGrouplayers(map: any) {
    const groupLayers: any = [];
    map.layers.forEach((layer: any) => {
      if (layer instanceof GroupLayer) {
        groupLayers.push(layer);
      }
    });
    return groupLayers;
  }

  createSummaryForPoi(map: any, queryGeometry: any) {
    const overallSummary: any = [];
    map.layers.forEach((groupLayer: any) => {
      if (groupLayer instanceof GroupLayer) {
        // @ts-ignore
        groupLayer.layers.items.forEach((layer: any) => {
          const query = layer.createQuery();
          query.where = '1=1';
          query.outFields = ['*'];
          query.spatialRelationship = 'intersects';
          query.geometry = queryGeometry;
          query.returnGeometry = false;
          const outSummary = {
            onStatisticField: 'objectid',
            // onStatisticField: 'OBJECTID',
            outStatisticFieldName: 'total',
            statisticType: 'count'
          };
          // @ts-ignore
          query.outStatistics = [outSummary];
          // layer.queryFeatures(query).then((results: any) => {
          //   let obj = {
          //     name: layer.title,
          //     total: results.features[0].attributes.total
          //   };
          //   overallSummary.push(obj);
          // });
          const queryResults = layer.queryFeatures(query);
          const obj = {
            name: layer.title,
            layer: queryResults,
            icon: layer.renderer.symbol.url
          };
          this.summaryData(obj);
        });
        // this.summaryData(overallSummary);
      }
    });
    this.dispalySummaryData(true);
  }

  createFeatureLayerToMapDistricts(url: string, opacity: any, layerName: any, visible: any) {
    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName
    });

    const fillSymbol = new SimpleFillSymbol({
      color: [255, 255, 255, 0.2],
      outline: {
        color: [255, 255, 255],
        width: 1
      }
    });

    featureLayer.renderer = {
      type: 'simple',
      // @ts-ignore
      symbol: fillSymbol
    };

    // @ts-ignore
    this._view.map.layers.add(featureLayer);
  }

  calculateResulsMapClickJobSeekers(feature: any) {
    if (feature == null) {
      this.mapClickJobSeekers('1=1');
    } else {
      const districtName = feature.attributes.d_name_en;
      const whereClause = `d_name_en='${districtName}'`;
      this.mapClickJobSeekers(whereClause);
    }
  }

  mapClickJobSeekers(whereClause: any) {
    // For High Degree Specialization
    this.getCountField('moe_he_grad_eucation_isced', 'moe_he_grad_eucation_isced', this.mapConfig.jobSeekerLayers[1], whereClause).then((response: any) => {
      const allObjs: any = [];
      const categories: any = [];
      const catData: any = [];
      response.features.map((result: any) => {
        const obj = {
          name: result.attributes.moe_he_grad_eucation_isced,
          y: result.attributes.total_moe_he_grad_eucation_isced
        };
        allObjs.push(obj);
      });
      allObjs.sort((a: any, b: any) => b.y - a.y);
      const top10 = allObjs.slice(0, 10);
      top10.forEach((d: any) => {
        categories.push(d.name);
        catData.push(parseFloat(d.y));
      });
      const passObj = {
        categories: categories,
        catData: catData,
        title: 'Job Seekers - High Degree Specialization',
        seriesName: 'Specialization',
        tab: 2,
        chartName: 'SPECIALIZATION'
      };
      this.setJobHighDegreeSpec(passObj);
    });
    // For Job Seekers Vs Job Vacancies
    this.getCountField('moe_he_grad_eucation_isced', 'major_new', this.mapConfig.jobSeekerLayers[1], whereClause).then((response: any) => {
      const categories: any = [];
      const jobSeekers: any = [];
      const jobVacancies: any = [];
      const seriesSeekers: any = [];
      const seriesVacancies: any = [];
      response.features.map((result: any) => {
        const obj = {
          name: result.attributes.major_new,
          seekers: result.attributes.total_moe_he_grad_eucation_isced
        };
        jobSeekers.push(obj);
      });
      this.getSumOfField('openings', 'major_saqib', this.mapConfig.jobSeekerLayers[3], whereClause).then((response: any) => {
        response.features.map((result: any) => {
          if (result.attributes.major_new === null) {

          } else {
            const obj = {
              name: result.attributes.major_saqib,
              vacancies: result.attributes.total_openings
            };
            jobVacancies.push(obj);
          }
        });
        const combinedArray = this.mergeObjects(jobSeekers, jobVacancies);
        combinedArray.forEach((d: any) => {
          categories.push(d.name);
          seriesSeekers.push(d.seekers);
          seriesVacancies.push(d.vacancies);
        });
        const passObj = {
          series: [
            {
              name: 'Seekers',
              data: seriesSeekers
            },
            {
              name: 'Vacancies',
              data: seriesVacancies
            }
          ],
          title: 'Job Seekers vs Job Vacancies',
          seriesName: 'Specialization',
          tab: 1,
          chartName: 'JOB_VS_VACANCIES'
        };
        this.setJobVsVac(passObj);
      });
    });
    // For Job Vacancies - By High Degree Specialization
    this.getSumOfField('openings', 'major_new', this.mapConfig.jobSeekerLayers[3], whereClause).then((response: any) => {
      const allObjs: any = [];
      const categories: any = [];
      const catData: any = [];
      response.features.map((result: any) => {
        if (result.attributes.major_new === null) {

        } else {
          const obj = {
            name: result.attributes.major_new,
            y: result.attributes.total_openings
          };
          allObjs.push(obj);
        }
      });
      allObjs.sort((a: any, b: any) => b.y - a.y);
      const top10 = allObjs.slice(0, 10);
      top10.forEach((d: any) => {
        categories.push(d.name);
        catData.push(parseFloat(d.y));
      });
      const passObj = {
        categories: categories,
        catData: catData,
        title: 'Job Vacancies - By High Degree Specialization',
        seriesName: 'Vacancies',
        tab: 3,
        chartName: 'JOB_VACANCIES'
      };
      this.setVacHighDegreeSpec(passObj);
    });
    // Job Seekers - By Years of Experience
    this.getCountField('years_of_experience_group', 'years_of_experience_group', this.mapConfig.jobSeekerLayers[1], whereClause).then((response: any) => {
      const allObjs: any = [];
      const categories: any = [];
      const catData: any = [];
      response.features.map((result: any) => {
        if (result.attributes.years_of_experience_group === null) {
        } else {
          const obj = {
            name: result.attributes.years_of_experience_group,
            y: result.attributes.total_years_of_experience_group
          };
          allObjs.push(obj);
        }
      });
      allObjs.sort((a: any, b: any) => b.y - a.y);
      const top10 = allObjs.slice(0, 10);
      top10.forEach((d: any) => {
        categories.push(d.name);
        catData.push(parseFloat(d.y));
      });
      const passObj = {
        categories: categories,
        catData: catData,
        title: 'Job Seekers - By Years of Experience',
        seriesName: 'Experience',
        tab: 4,
        chartName: 'YEARS_OF_EXPERIENCE_GROUP'
      };
      this.setJobYearsOfExperience(passObj);
    });
  }

  mergeObjects(arr1: any, arr2: any) {
    const mergedArray = [];

    for (const obj1 of arr1) {
      const trimmedName1 = obj1.name?.replace(/\s+/g, ''); // Remove all spaces from name in array1
      if (trimmedName1 !== '') {
        const matchedObj = arr2.find((obj2: any) => {
          const trimmedName2 = obj2.name?.replace(/\s+/g, ''); // Remove all spaces from name in array2
          return trimmedName2 === trimmedName1;
        });
        if (matchedObj) {
          if (matchedObj.name == null){

          } else {
            mergedArray.push({ ...obj1, ...matchedObj });
          }
        }
      }
    }

    return mergedArray;
  }

  setupEsriConfig(urls: any, token: string) {
    // @ts-ignore
    esriConfig.request.interceptors.push({
      urls: urls,
      before: function(params) {
        params.requestOptions.cacheBust = true;
        params.requestOptions.query = params.requestOptions.query || {};
        params.requestOptions.query.token = token;
      }
    });
    this.setAuthenticatedauthStatusForServices(true);
  }

  googleAnalyticsMap(domain: string, district_name: string) {
    
    const personal_email = this._msal?.getLoginData?.account?.username;
    // let string_to_pass: string =     

    (window as any)?.dataLayer?.push({
      'event': 'geospatial_map',
      'geospatial': 'Domain '+this.getTextFromNumber(parseInt(domain))+' is selected. ' + district_name + ' district is clicked on the map. By the?pid=' + this._msal.convertEmail(personal_email)
    });


    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logMap(this.sessionId, logType.map, this.log.currentTime, district_name, this.getTextFromNumber(parseInt(domain)));
  }

  getTextFromNumber(number: number): string {
    
    const numberToText: { [key: number]: string } = {
      1: "Job Seekers",
      2: "Household Population",
      3: "Population Census 2023",
      5: "Flat Transaction",
      6: "RealEstate Census 2023"
    };

    return numberToText[number] || number.toString();
  }

}
