import { chartConstants, checkAll, externalUrlsDarkDashboard, externalUrlsDashboard } from 'src/app/scad-insights/core/constants/chart.constants';
import { numberType } from './../../../core/constants/numberType.constant';
import { IfpAnalysisCardHeaderComponent } from './../../ifp-molecules/ifp-analysis-card-header/ifp-analysis-card-header.component';
import { Component, Input, Output, EventEmitter, ElementRef, Renderer2, OnChanges, OnInit, OnDestroy, ChangeDetectorRef, ChangeDetectionStrategy, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';
import { IfpCardComponent } from '../../ifp-atoms/ifp-card/ifp-card.component';
import { IfpIconTextComponent } from '../../ifp-molecules/ifp-icon-text/ifp-icon-text.component';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { IfpRatingComponent } from '../../ifp-atoms/ifp-rating/ifp-rating.component';
import { buttonClass, buttonColor, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule } from '@ngx-translate/core';
import { IfpMonthSelectorComponent } from '../../ifp-atoms/ifp-month-selector/ifp-month-selector.component';
import { Store } from '@ngrx/store';
import { getIndicator } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { SubSink } from 'subsink';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { distinctUntilChanged } from 'rxjs';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { appType, indicatorType } from 'src/app/scad-insights/core/constants/contentType.constants';
import { IndicatorListData, Security } from 'src/app/scad-insights/core/interface/indicator.interface';
import { cloneDeep } from 'lodash';
import { IfpCardLoaderComponent } from '../../ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { selectGetMappedData } from 'src/app/scad-insights/store/notification/notification.selector';
import { setEmailUpdate, setNotificationUpdate, unsubscribeNotificationUpdate } from 'src/app/scad-insights/store/notification/notification.action';
import { RouterModule } from '@angular/router';
import { AnalyticalService } from 'src/app/scad-insights/core/services/analytical.service';
import { IfpAnalyticLineChartComponent } from '../../ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { IfpAnalyticTreeChartComponent } from '../../ifp-molecules/ifp-analytic-tree-chart/ifp-analytic-tree-chart.component';
import { FilterService } from 'src/app/scad-insights/core/services/filter/filter.service';
import { resizeMyApps } from 'src/app/scad-insights/store/myApps/myAppsGlobal.action';
import { IfpModalComponent } from '../ifp-modal/ifp-modal.component';
import { IfpAlertBoxComponent } from '../../ifp-molecules/ifp-alert-box/ifp-alert-box.component';
import { notificationMessage } from 'src/app/scad-insights/core/constants/message.constants';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { AlertBoxService } from 'src/app/scad-insights/core/services/alert-box.service';
import { QuotRemove } from 'src/app/scad-insights/core/pipes/quotsRemove.pipe';
import { IfpCheckboxComponent } from '../../ifp-atoms/ifp-checkbox/ifp-checkbox.component';
import { IfpUnauthorizedCardComponent } from '../../ifp-molecules/ifp-unauthorized-card/ifp-unauthorized-card.component';
import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { IfpGeoMapComponent } from '../../ifp-molecules/ifp-geo-map/ifp-geo-map.component';
import { IfpTagComponent } from '../../../../ifp-analytics/atom/ifp-tag/ifp-tag.component';
import { IfpIndicatorCardService } from 'src/app/scad-insights/core/services/indicator-card/ifp-indicator-card.service';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'ifp-analysis-card',
  templateUrl: './ifp-analysis-card.component.html',
  styleUrls: ['./ifp-analysis-card.component.scss'],
  providers: [ShortNumberPipe, DatePipe, QuotRemove, TitleCasePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, IfpAnalysisCardHeaderComponent, IfpCardComponent, IfpIconTextComponent, IfpButtonComponent, IfpRatingComponent, TranslateModule, IfpMonthSelectorComponent, ShortNumberPipe, IfpCardLoaderComponent, IfpTooltipDirective, RouterModule, IfpAnalyticLineChartComponent, IfpAnalyticTreeChartComponent, IfpAlertBoxComponent, QuotRemove, IfpCheckboxComponent, IfpUnauthorizedCardComponent, IfpGeoMapComponent, IfpTagComponent]
})

export class IfpAnalysisCardComponent implements OnChanges, OnInit, OnDestroy {

  @ViewChild('subscribeNotification') subscribeNotificationModal!: IfpModalComponent;
  @ViewChild('analysisCard') analysisCard!: HTMLDivElement;
  @ViewChild('alertBox') alertBox!: ElementRef;
  @Input() heading: string = '';
  @Input() subTitle: string = '';
  @Input() isRedirectTop = false;
  @Input() isNotificationRemove: boolean = false;
  @Input() appTypeData: null | string = null;
  @Input() currencyData: CurrencyData = {
    currency: null,
    currencyWords: '',
    currencySubTitle: '',
    color: ''
  };

  @Input() metaData: any = {};

  @Input() publish!: string | null;
  @Input() source!: string | null;
  @Input() small: boolean = true;
  @Input() id!: string;
  @Input() contentType!: string;
  @Input() delay = 0;
  @Input() firstLoader = true;
  @Input() secondLevel = false;
  @Input() tagName!: string | null;
  @Input() domains: { icon: string; color: string }[] = [];
  @Input() remove: boolean = false;
  @Input() addMyAppsLanding = false;
  @Input() addMyApps = false;
  @Input() isSelected: boolean = false;
  @Input() isAccessPending: boolean = false;
  @Input() isHideShareCheck: boolean = false;
  @Output() crossClick = new EventEmitter();
  @Output() resizedEvent = new EventEmitter();
  @Output() removeNotification = new EventEmitter();
  @Output() selectAnalyticIndicator: EventEmitter<{ status: boolean; id: string | number; type: string }> = new EventEmitter<{ status: boolean; id: string | number; type: string }>();
  public subs = new SubSink();
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public dateFormat = dateFormat;
  public numberType = numberType.shotKeyFormat;
  public chart = false;
  public isDataLoad = false;
  public data: IndicatorListData | any = {};
  public loader: undefined | boolean = true;
  public indicatorType = indicatorType;
  public comparison!: string;
  public format!: string;
  public analyticalData: any = [];
  public baseData: any = [];
  public periodFilters: any = [];
  public chartData: any = [];
  public defaultVisualData: any = [];
  public comparisonList = [];
  public selectedPerid: any;
  public value: string = '';
  public range!: string;
  public timePeriodOptions: any;
  public index!: number;
  public filterKeys: any = [];
  public chartConstants = chartConstants;
  public ratingMeta!: ValueMeta;
  public visualaizationfilterPanel: any = [];
  public notificationSelector !: boolean;
  public emailSelector !: boolean;
  public type = '';
  public seriesMeta: any = [];
  public filterPanel: any = [];
  public chartType: any = 'line-chart';
  public periodFilter: any[] = [];
  public isSubscNotifOpen: boolean = false;
  public textLimit: number = 25;
  public externalUrls: any = externalUrlsDashboard;
  public externalUrlsDark: any = externalUrlsDarkDashboard;
  public appliedFilterTooltip: any;
  public notificationMessage = notificationMessage;
  public liveabilityData: any = [];
  public lang = localStorage.getItem('lang') ? localStorage.getItem('lang') : 'en';
  public unauthorized: boolean = false;
  public url!: string;
  public chartDataCorrelation!: any;
  public xAxis: string[] = [];
  public tooltipData: { title: string; description: string }[] = [];
  public plotLinesY!: Record<string, any>[];
  public xAxisLabelType: string = chartConstants.xAxisCatogory;
  public chartCardData!: any;
  public rating = {
    color: '',
    value: 0,
    percentage: 0,
    invertColor: false,
    invertArrows: false
  };

  public selectedPeriod: any = {
    id: 'Latest-Readings',
    label: 'Recent',
    unit: chartConstants.RECENT_LABEL,
    value: '',
    isSelected: true
  };

  public scenarioDefaultPeriod = {
    id: 'Latest-Readings',
    isSelected: true,
    label: 'Recent',
    unit: 'Recent',
    value: ''
  };

  public name: string = '';
  public comparisonValues: Record<string, { change: string, compare: string }> = {
    yearly: {
      change: 'yearlyChangeValue',
      compare: 'yearlyCompareValue'
    },
    monthly: {
      change: 'monthlyChangeValue',
      compare: 'monthlyCompareValue'
    },
    quarterly: {
      change: 'quarterlyChangeValue',
      compare: 'quarterlyCompareValue'
    }
  };

  public colors = ifpColors;
  public red = ifpColors.red;
  public buttonColor = buttonColor;
  public security!: Security;
  @Output() myApps = new EventEmitter();
  constructor(private _elementRef: ElementRef, private _render: Renderer2, private store: Store, private _cdr: ChangeDetectorRef, private _shortNumber: ShortNumberPipe, private _alertBoxService: AlertBoxService, private datePipe: DatePipe, private analyticalService: AnalyticalService, private _filterService: FilterService, public _themeService: ThemeService, private _quotRemove: QuotRemove,
    private titleCase: TitleCasePipe, private _domainService: DomainsService, private _cardService: IfpIndicatorCardService) {

  }

  myappsEvent(event: any) {
    this.myApps.emit(event);
  }


  ngOnInit(): void {
    if (this.appTypeData != chartConstants.LIVEABILITY) {
      if (this.id) {
        this.store.dispatch(getIndicator({
          id: this.id,
          contentType: this.contentType
        }));
      }
      this.subs.add(
        this.store.select(selectIndicatorGetById(this.id))
          .pipe(distinctUntilChanged((prev, curr) => prev.loader === curr.loader))
          .subscribe((data) => {
            if (data?.errorMessage?.error?.access?.toString() === 'false') {
              this.unauthorized = true;
            }
            this.analyticalData = data;
            const metaValue = data?.body?.indicatorValues?.overviewValuesMeta?.[0];
            this.value = metaValue?.value;
            this.data = data?.body;
            this.type = this.type && this.type !== '' ? this.type : this.data?.type;
            if (this.data) {
              this.tagName = this.data?.tagName;
              this.metaData = this.data;
              this.name = this.data?.component_title;
              this.currencyData = {
                currency: this.value ? this.value : this.getCurrencyValue((this.metaData?.indicatorValues?.overviewValuesMeta?.[0]?.values), this.metaData?.indicatorValues?.overviewValuesMeta?.[0]?.aggregation),
                color: '#FFA200'
              };
              if (this.metaData?.indicatorValues?.overviewValuesMeta && this.metaData?.indicatorValues?.overviewValuesMeta.length > 0) {
                this.ratingMeta = this.metaData?.indicatorValues?.overviewValuesMeta[1];
              }
              this.heading = this._quotRemove.transform(this.data?.component_title);
              this.subTitle = this.data?.component_subtitle;
              this.id = this.data?.id;
              this.contentType = this.contentType ? this.contentType : this.data.indicatorType;
              this.publish = this.data.publication_date ? this.data.publication_date : this.data.updated;
              this.domains.push({
                icon: this.data.domain,
                color: this.data.tagColorCode && this.data.tagColorCode !== '' ? this.data.tagColorCode : ifpColors.purple
              });
              if (this.data?.security && environment.env !== 'demo') {
                this.security = this._cardService.setSecurity(this.data.security);
              }
            }

            this.loader = data.loader;
            if (this.analyticalData?.body?.type == this.chartConstants.SCENARIO_TYPE) {
              this.getScenarioDriver();
            }
            if (this.analyticalData?.body?.type.toLowerCase() == this.chartConstants.INSIGHT_DISCOVERY.toLowerCase()) {
              this.getInsightDiscovery();
            }
            if (this.analyticalData?.body?.type == 'Correlation') {
              this.chartType = chartConstants.correlation;
              this.chartCardData = this.analyticalData?.body?.indicatorVisualizations?.visualizationsMeta[0].seriesMeta[0];
              let data = this.analyticalService.setCorrelationChartData(this.chartCardData);

              this.chartDataCorrelation = [...data.chartData];
              this.chartDataCorrelation[0].pointWidth = undefined;
              this.xAxis = data.xAxis;
              this.tooltipData = data.tooltipData;
              this.plotLinesY = data.plotLinesY;
            }

            this.url = this.getUrl();
            this._cdr.detectChanges();
          })
      );
      this._alertBoxService.isAlertBoxStatus$.subscribe(() => {
        this._alertBoxService.closeAlertBox(this.alertBox);
      });
    } else {
      this.getLiveabilityData();
    }
  }

  getUrl() {
    if (this.type === chartConstants['Tableau-Internal'] || this.appTypeData === chartConstants.tableau_internal) {
      return `dashboard/${this.appTypeData}/${this.id}`;
    } else if (chartConstants.SCENARIO_TYPE === this.type && !this.analyticalData?.body?.multiDrivers) {
      return `/scenario-driver/${this.id}`;
    } else if (this.analyticalData?.body?.multiDrivers) {
      return `/whatif/${this.id}`;
    } else if (this.appTypeData === chartConstants.LIVEABILITY) {
      return `/liveability-dashboard/${this.id}`;
    } else if (this.type?.toLowerCase() === appType.correlation.name) {
      return `${appType.correlation.urlPrefix}/${this.id}`;
    }
    return `/insight-discovery/${this.id}`;
  }

  getScenarioDriver() {
    this.baseData = this.analyticalData?.body?.indicatorVisualizations?.visualizationsMeta.find((x: { id: any; }) => x.id == this.analyticalData?.body?.indicatorVisualizations.visualizationDefault).seriesMeta;
    this.chartType = this.analyticalData?.body?.indicatorVisualizations?.visualizationsMeta.find((x: { id: any; }) => x.id == this.analyticalData?.body?.indicatorVisualizations.visualizationDefault).type;
    // }
    let selectedFilter: any;
    if (this.analyticalData?.body?.indicatorFilters && this.analyticalData?.body?.indicatorFilters[0]?.options?.length > 0) {
      this.periodFilters = cloneDeep(this.analyticalData?.body?.indicatorFilters[0]?.options);
      selectedFilter = this.selectedPerid ? this.selectedPerid : this.periodFilters.find((x: { isSelected: any; }) => x.isSelected);
      if (this.selectedPerid) {
        this.periodFilters.forEach((element: { id: any; isSelected: boolean; }) => {
          if (element.id == this.selectedPerid.id) {
            element.isSelected = true;
          } else {
            element.isSelected = false;
          }
        });
      }
    } else {
      selectedFilter = this.selectedPeriod;
    }
    const isReverse = !!this.filterKeys?.length;
    this.getChartData((selectedFilter && selectedFilter.id !== 'All' ? this.analyticalService.analyticPeriodFilter(cloneDeep(this.baseData), this.scenarioDefaultPeriod, isReverse) : this.baseData), this.chartType);
    this._cdr.detectChanges();
  }

  getInsightDiscovery() {
    this.filterKeys = [];
    const visualData = this.analyticalData?.body?.visualizations.find((x: { id: number; }) => x.id == this.analyticalData?.body?.default_visualisation);
    this.defaultVisualData = visualData.indicatorVisualizations.visualizationsMeta.find((y: { id: any; }) => y?.id == visualData.indicatorVisualizations.visualizationDefault);
    if (visualData.filterPanel?.properties?.length > 0) {
      this.appliedFilterTooltip = '';
      visualData.filterPanel?.properties.forEach((element: any, index: number) => {
        const obj = {
          label: element.label,
          value: [element.default],
          index: index,
          path: element.path,
          isCFD: visualData.filterPanel?.isCFD
        };
        this.filterKeys.push(obj);
        this.appliedFilterTooltip = `${this.appliedFilterTooltip}${this.titleCase.transform(obj.label)}: ${this.titleCase.transform(obj.value[0])}${visualData.filterPanel?.properties.length - 1 != index ? ' | ' : ''}`;
        if (obj.path == chartConstants.TIME_PERIOD) {
          this.timePeriodOptions = element.options;
        }
      });
      this.seriesMeta = cloneDeep(this.defaultVisualData?.seriesMeta?.[0]);
      this.filterData();
    }
  }

  filterData() {
    this.chartData = [];
    if (this.filterKeys?.length > 0) {
      if (this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.map((x: any) => {
          x.checkbox = x.value.length > 1 && x.path != this.chartConstants.TIME_PERIOD ? true : false;
        });
        if (this.filterKeys.every((y: any) => !y.checkbox)) {
          this.filterKeys[this.filterKeys[0].path != chartConstants.TIME_PERIOD ? 0 : 1].checkbox = true;
        }
      }
      if (!this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.forEach((element: any, index: number) => {
          const selectIndex = this.filterKeys[0].path == this.chartConstants.TIME_PERIOD ? 1 : 0;
          element.checkbox = index == selectIndex ? true : false;
        });
      }
      if (this.filterKeys.find((x: { path: string; }) => x.path == chartConstants.TIME_PERIOD)) {
        const timeIndex = this.filterKeys.findIndex((y: { path: string; }) => y.path == chartConstants.TIME_PERIOD);
        if (checkAll(this.filterKeys[timeIndex].value[0])) {
          this.filterKeys[timeIndex].value = this.timePeriodOptions;
        }
      }
    }

    this.seriesMeta.data = this.seriesMeta?.data.filter((x: any) => x[this.defaultVisualData.dbColumn] == this.seriesMeta.dbIndicatorId);
    const chartData = this._filterService.filterIndicatorDetail(this.seriesMeta, this.selectedPeriod, this.filterKeys);

    chartData.forEach((series: Record<string, any>[], index: number) => {
      const newChart = series?.map((element: Record<string, any>) => {

        //console.log("element['OBS_DT']", element['OBS_DT'])

        const splitDate = element['OBS_DT'].split('-');
        return [(Date.UTC(splitDate[0], splitDate[1] - 1, splitDate[2])), element['VALUE']];
      });
      // const chartArray = newChart.slice(0, 12);

      const seriesData = {
        color: this._filterService.getColor(index),
        data: newChart,
        name: this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] ? this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] : '',
        type: 'line'
      };

      console.log("seriesData.data.length", seriesData.data.length)
      if (seriesData.data?.length > 0) {
        this.chartData.push(seriesData);
      }
      if (this.analyticalData?.body?.type == this.chartConstants.INSIGHT_DISCOVERY) {
        this.currencyData.currency = this.chartData[0]?.data[this.chartData[0]?.data.length - 1]?.[1];
      }
    });

    this._cdr.detectChanges();
  }


  convertDateToQuarterWithoutQLabel(date: any) {
    let label: string = '';
    if (
      new Date(date).getMonth() + 1 <= 3 && new Date(date).getMonth() + 1 > 0
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 3 && new Date(date).getMonth() + 1 <= 6
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 6 && new Date(date).getMonth() + 1 <= 9
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 9 && new Date(date).getMonth() + 1 <= 12
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (new Date(date).getMonth() + 1 == 0) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    }
    return label;
  }

  getChartData(data: any, chartType: string) {
    if (data && data.length > 0) {
      this.chartData = chartType == chartConstants.LINECHART ? this.analyticalService.creatingLineChartData(data, this.id, chartConstants.ANALYTICAL_APPS) : this.analyticalService.creatingTreeChartData(data);
    }
  }

  filterBasedOnPeriod(event: any) {
    this.selectedPerid = event;
    const isReverse = !!this.filterKeys?.length;
    this.getChartData((event && event.id !== 'All' ? this.analyticalService.analyticPeriodFilter(this.baseData, event, isReverse) : this.baseData), this.chartType);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.appTypeData != chartConstants.LIVEABILITY) {
      this.currencyData.currency = this.currencyData.currency ? this.currencyData.currency : this.getCurrencyValue((this.metaData?.indicatorValues?.overviewValuesMeta?.[0]?.values), this.metaData?.indicatorValues?.overviewValuesMeta?.[0]?.aggregation);
      if (this.metaData?.indicatorValues?.overviewValuesMeta && this.metaData?.indicatorValues?.overviewValuesMeta.length > 0) {
        this.ratingMeta = this.metaData?.indicatorValues?.overviewValuesMeta[1];
      }
      // this.secondLevel = this.metaData?.showSecondLevel;
      if (changes?.['metaData']) {
        const meta: any = changes?.['metaData'];
        this.secondLevel = meta?.currentValue?.['showSecondLevel'];
      }
      // if (changes?.['small'] && !this.small){
      //   setTimeout(() => {
      //     this.chart = true;
      //     this._cdr.detectChanges();
      //   }, this.delay);
      //   this._render.addClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
      // }
      // else
      if (changes['small']) {
        if (!changes['small'].currentValue) {
          setTimeout(() => {
            this.chart = true;
            this._cdr.detectChanges();
          }, this.delay);
          this._render.addClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
        }
      }

      if (changes?.['small'] && this.small) {
        setTimeout(() => {
          this.chart = false;
          this._cdr.detectChanges();
        }, this.delay);
        this._render.removeClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
      }

      this.subs.add(this.store.select(selectGetMappedData(this.id)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(data => {
        this.notificationSelector = data[this.id]?.isNotification ? data[this.id].isNotification : false;
      }));
    }
  }


  getCurrencyValue(value: any | [], aggregation: string) {
    const filterIndex = value?.findIndex((data: { type: string }) => data.type === 'ABSOLUTE');
    if (filterIndex !== -1) {
      this.numberType = numberType.shotKeyFormat;
      return +value?.[filterIndex].value;
    }
    const filterPercentage = value?.findIndex((data: { type: string, aggregation: string }) => data.type === 'PERCENTAGE' && data?.aggregation === aggregation);
    if (filterPercentage !== -1) {
      this.numberType = numberType.percentage;
      return +value?.[filterPercentage].value;
    }

    return '';
  }

  // resized(value: boolean) {
  //   if (value) {
  //     this._render.removeClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
  //   } else {
  //     this._render.addClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
  //   }
  //   this.small = value;
  //   this.resizedEvent.emit(value);
  // }

  resized(value: boolean) {
    if (this.addMyAppsLanding) {
      this.store.dispatch(resizeMyApps({ id: this.id }));
    }
    if (value) {
      setTimeout(() => {
        this.chart = false;
        this._cdr.detectChanges();
      }, this.delay);
      this._render.removeClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
      this.resizedEvent.emit(null);
    } else {
      setTimeout(() => {
        this.chart = true;
        this._cdr.detectChanges();
      }, this.delay);
      this._render.addClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
      this.resizedEvent.emit(this.index);
    }
  }



  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this._alertBoxService.closeAlertBox(this.alertBox);
  }

  removeEvent() {
    this.crossClick.emit({ id: this.id, domain: this.domains[0].icon ? this.domains[0].icon : this.domains[0], contentType: this.contentType, title: this.heading });
  }

  selectNode(event: any) {
    this.selectAnalyticIndicator.emit({ status: event.target.checked, id: this.id, type: this.contentType });
  }

  // select period ;



  // filter analytic daat ;


  getDomains() {
    let icons: { icon: string; color: string }[] = [];
    if (this.domains.length > 0) {
      icons = this.domains;
    }
    return icons;
  }


  getECIOptimizationUrl(url: string) {
    const splitUrl = (url: string): [string, string] => [url.replace(/\/$/, "").slice(0, url.replace(/\/$/, "").lastIndexOf("/")), url.replace(/\/$/, "").split("/").pop() || ""];
    const optimizationUrl = splitUrl(url);
    return `${optimizationUrl[0]}/${this.lang}/${optimizationUrl[1]}`;
  }

  // addNotification(current: boolean) {
  //   if (!current) {
  //     this.subscribeNotificationModal.createElement();
  //   } else {
  //     this.store.dispatch(unsubscribeNotificationUpdate({ id: this.id }));
  //   }
  // }

  // setEmailNotifStatus(status: boolean) {
  //   this.store.dispatch(setNotificationUpdate({id: this.id, contentType: this.contentType, appType: this.type, emailStatus: status}));
  //   this.subscribeNotificationModal.removeModal();
  // }
  addNotification(current: boolean, parentNode: HTMLDivElement) {
    if (!current) {
      const notificationData = {
        id: this.id,
        contentType: this.contentType,
        appType: this.type,
        emailStatus: false
      };
      this.store.dispatch(setNotificationUpdate(notificationData));
      // this.subscribeNotificationModal.createElement();
      this.isSubscNotifOpen = true;
      this._alertBoxService.openAlertBox(this.alertBox, parentNode);
      setTimeout(() => {
        // this.subscribeNotificationModal.removeModal();
        this._alertBoxService.closeAlertBox(this.alertBox);
        this.isSubscNotifOpen = false;
      }, 5000);
    } else {
      this.store.dispatch(unsubscribeNotificationUpdate({ id: this.id }));
      if (this.isNotificationRemove) {
        this.removeNotification.emit(true);
      }
    }
  }

  setEmailNotifStatus(status: boolean) {
    if (status) {
      this.store.dispatch(setEmailUpdate({ id: this.id }));
      setTimeout(() => {
        // this.subscribeNotificationModal.removeModal();
        this.isSubscNotifOpen = false;
        this._alertBoxService.closeAlertBox(this.alertBox);
      }, 400);
    } else {
      // this.subscribeNotificationModal.removeModal();
      this._alertBoxService.closeAlertBox(this.alertBox);
    }
  }

  scrollTop(_event: any) {
    if (this.isRedirectTop) {
      window.scroll(0, 0);
    }
  }

  closeModal() {
    // this.subscribeNotificationModal.removeModal();
    this._alertBoxService.closeAlertBox(this.alertBox);
  }

  getLiveabilityData() {
    const payLoad: any = {
      id: this.id
    };
    this._domainService.getLiveabilityDashboard(payLoad).subscribe(resp => {
      this.liveabilityData = resp;
      this.loader = false;
      this.domains.push({
        icon: resp.domain,
        color: resp.tagColorCode
      });
      this.publish = resp.updated;
      this.heading = resp.title;
      this.subTitle = resp.subTitle;
      this.url = `/liveability-dashboard/${this.id}`;
      this._cdr.detectChanges();
    });
  }
}
export interface CurrencyData {
  currency?: number | string | null,
  currencyWords?: string,
  currencySubTitle?: string,
  color?: string;
}

export interface ChartData {
  color: string;
  type: string;
  data: []
}

export interface ValueMeta {
  color?: string;
  dimension?: [];
  id?: string;
  period?: number;
  templateFormat?: string;
  title?: string;
  type?: string;
  unit?: string;
  value?: any;
  valueFormat?: string;
  viewName?: string;
}



