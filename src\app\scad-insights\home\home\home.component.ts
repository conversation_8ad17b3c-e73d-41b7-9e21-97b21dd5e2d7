
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { SubSink } from 'subsink';
import { loadPage } from 'src/app/scad-insights/store/pages/page.action';
import { Title } from '@angular/platform-browser';
import { selectHomeSectionState } from '../store/home.selector';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { DashboardService } from '../../core/services/create-dashboard/dashboard.service';
import { title } from '../../core/constants/header.constants';
import { UsageDashboardLogService } from '../../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../../core/services/usage-dashboard-log/usage-dashboard.constants';

@Component({
    selector: 'app-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class HomeComponent implements OnInit, OnDestroy {

  private subs = new SubSink();
  public loader = true;
  public homePage: Record<string, any> = {
    'census': {
      class: 'ifp-census',
      component: () => import('src/app/scad-insights/home/<USER>/ifp-census.component').then(mod => mod.IfpCensusComponent)
    },
    'what_is_new': {
      class: 'ifp-whats-new-container',
      component: () => import('src/app/scad-insights/home/<USER>/ifp-whats-new.component').then(mod => mod.IfpWhatsNewComponent)
    },
    'newsletter': {
      class: 'ifp-whats-new-highlight',
      component: () => import('src/app/scad-insights/ifp-widgets/ifp-organism/whats-new-highlight/whats-new-highlight.component').then(mod => mod.WhatsNewHighlightComponent)
    },
    'spatial_analytics':  {
      class: 'ifp-geo-spatial',
      component: () => import('src/app/scad-insights/geospatial-revamp/spatial-analytics/spatial-analytics.component').then(mod => mod.SpatialAnalyticsComponent)
    },
    'analytical_apps': {
      class: 'ifp-analysis-container',
      component: () => import('src/app/scad-insights/home/<USER>/ifp-analysis.component').then(mod => mod.IfpAnalysisComponent)
    }
  };

  public homePageList = this.store.select(selectHomeSectionState);
  private sessionId!: string;
  constructor(private store: Store, private _titleService: Title, private _cdr: ChangeDetectorRef, private _msalService: IFPMsalService, private _dashboardService: DashboardService, private log: UsageDashboardLogService) {
    localStorage.removeItem(this._dashboardService.selectedCards);
  }

  ngOnInit(): void {
    this._titleService.setTitle(`${title.bayaan} | Home`);

    (window as any)?.dataLayer?.push({
      'event': 'page_load',
      'page_title_var': 'Home',
      'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
    });

    this.store.dispatch(loadPage());
    this.settingLoader();
    // this.subs.add(this._journeyService.getUserJourneyData().subscribe((data: Journey) => {
    //   if (!this._journeyService.isExplore && this._cookie.getCookie('journeyStatus') !== 'later') {
    //     this._themeService.startJourny$.next({ data: data, status: true });
    //   }

    //   if (this._themeService.isStartJourny) {
    //     this.subs.add(this._journeyService.getUserJourneyData().subscribe((data: any) => {
    //       data.status = false;
    //       data.skipModel = true;
    //       this._themeService.startJourny$.next({ data: data, status: true });
    //       this._journeyService.observableActivate.next(true);
    //     }));
    //     this._themeService.isStartJourny = true;
    //   }
    // }));
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.home, this.log.currentTime );
  }

  settingLoader() {
    this.loader = false;
    this._cdr.detectChanges();
  }



  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }

    this.subs.unsubscribe();
  }
}



