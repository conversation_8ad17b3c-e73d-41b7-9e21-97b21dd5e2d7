#viewDivCensus {
  // height: 100vh;
  // width: 100vw;
  // min-height: 120vh;
  height: 100%;
  width: 100%;
  // max-height: calc(100vh);
  border-radius: inherit;
  position: relative;
}

// @media screen and (max-width: 1300px) {
//   #viewDivCensus{
//     max-height: calc(100vh + 10px);
//   }
// }

.arrow-down {
  position: relative;
  top: -2px;
  left: 10px;
  border: solid #333;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
}



// dropdown menu

.custom-multiselect {
  position: relative;
  width: 100%;
  font-family: Arial, sans-serif;
}

.select-trigger {
  position: relative;
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  justify-content: space-between;

  .arrow-down {
    margin-inline-start: 8px !important;
  }
}


.arrow-down {
  position: relative;
  left: -5px;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
}

.options-container {
  width: 156px;
  position: absolute;
  bottom: 25px;
  left: -10px;
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: #333;
  border-radius: 12px;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  &::before {

  }
  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }

  // Enhanced appearance on hover
  &:hover {
    // background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.6);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
}

.options-list {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  &:hover {
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  // transform: translateX(2px);
}
}

.region-name {
  margin-left: 8px;
  color: #000;
   width: 100%;
  height: 100%;
}