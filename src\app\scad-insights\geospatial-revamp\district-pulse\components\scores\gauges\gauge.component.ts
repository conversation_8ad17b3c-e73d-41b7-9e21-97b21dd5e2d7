import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import * as Highcharts from 'highcharts';
import HC_exporting from 'highcharts/modules/exporting';
import { TranslateModule } from '@ngx-translate/core';
import HC_exportData from 'highcharts/modules/export-data';
import HC_accessibility from 'highcharts/modules/accessibility';
import { Options } from 'highcharts';
import { HighchartsChartModule } from 'highcharts-angular';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

HC_exporting(Highcharts);
HC_exportData(Highcharts);
HC_accessibility(Highcharts);

@Component({
  selector: 'ifp-gauge',
  standalone: true,
  imports: [
    FormsModule, 
    CommonModule, 
    HighchartsChartModule, 
    TranslateModule
  ],
  templateUrl: './gauge.component.html',
  styleUrl: './gauge.component.scss'
})
export class GaugeComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

  highcharts: typeof Highcharts = Highcharts;
  @Input() score: number = 50;
  public chartOptions!: Options;

  constructor(
    private _cdr: ChangeDetectorRef,
    private themeService: ThemeService,
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['score'] && !changes['score'].firstChange) {
      this.updateChart();
    }
  }

  ngOnInit(): void {
    this.createGaugeChart();
  }

  ngAfterViewInit(): void {
  }

  ngOnDestroy(): void {
  }

  updateChart() {
    if (this.chartOptions && this.chartOptions.series && Array.isArray(this.chartOptions.series)) {
      const series = this.chartOptions.series[0] as Highcharts.SeriesGaugeOptions;
      if (series && series.data) {
        series.data = [this.score];
        this._cdr.detectChanges();
      }
    }
  }

 createGaugeChart() {
  let gaugeColor;
  if (this.score <= 50) {
    gaugeColor = '#e75468'; 
  } else if (this.score <= 80) {
    gaugeColor = '#c19020'; 
  } else {
    gaugeColor = '#55BF3B';
  }
  
  const customCSS = document.createElement('style');
  customCSS.innerHTML = `
    .no-border {
      stroke: transparent !important;
      stroke-width: 0 !important;
    }
  `;
  document.head.appendChild(customCSS);
  
  this.chartOptions = {
    chart: {
      type: 'gauge',
      plotBackgroundColor: 'transparent',
      plotBorderWidth: 0,
      plotShadow: false,
      height: '100%',
      // style: {position: 'absolute', top: '15px'},
      backgroundColor: 'transparent',
      margin: [0, 0, 0, 0],
      plotBorderColor: 'transparent' 
    },
    title: {
      text: ''
    },
    subtitle: {
      text: ''
    },
    exporting: {
      enabled: false,
    },
    legend: {
      enabled: false
    },
    credits: {
      enabled: false
    },
    pane: {
      startAngle: -90,
      endAngle: 90,
      background: [{
        backgroundColor: 'transparent',
        borderWidth: 0,
        outerRadius: '100%',
        innerRadius: '80%',
        borderColor: 'transparent' 
      }],
      size: '100%',
      center: ['50%', '70%']
    },
    yAxis: {
      min: 0,
      max: 100,
      lineWidth: 0,
      minorTickInterval: 0,
      tickAmount: 0,
      tickWidth: 0,
      tickLength: 0,
      tickColor: 'transparent',
      tickPosition: 'inside',
      labels: {
        enabled: false
      },
      plotBands: [
        {
          from: 0,
          to: 100,
          thickness: 4,
          borderWidth: 0,
          color: '#E0E0E0', // Light gray color
          borderColor: 'transparent',
          className: 'no-border'
        },
        {
          from: 0,
          to: this.score,
          thickness: 4,
          borderWidth: 0,
          color: gaugeColor,
          borderColor: 'transparent',
          className: 'no-border'
        }
      ]
    },
    series: [{
      type: 'gauge',
      name: 'Score',
      data: [this.score],
      dataLabels: {
        enabled: true,
        formatter: function(this: Highcharts.PointLabelObject) {
          const value = this.point?.y ?? 0;

          let levelText = 'Low';
          if (value > 80) {
            levelText = 'High';
          } else if (value > 50) {
            levelText = 'Medium';
          }
          
          return '<div style="text-align:center; position: relative; top: -25px">' + 
                 '<span style="font-size:10px;color:#666;font-weight:normal">' + levelText + '</span><br>' + 
                 '<span style="font-size:14px;font-weight:bold">' + value.toFixed(2) + '%</span>' + 
                 '</div>';
        },
        useHTML: true,
        y: 80,
        borderWidth: 0
      },
      dial: {
        radius: '0%',  
        backgroundColor: 'transparent',
        baseWidth: 0,
        baseLength: '0%',
        rearLength: '0%'
      },
      pivot: {
        backgroundColor: 'transparent',
        radius: 0 
      }
    }]
  };
  this._cdr.detectChanges();
}

}