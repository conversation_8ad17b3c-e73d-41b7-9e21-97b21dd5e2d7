<div class="ifp-main" [class.isPrinting]="downloadService.isPrinting">
  @if(!isGeospatial()) {
    <app-header></app-header>
  }
  <div class="ifp-main__wrapper">
    <router-outlet></router-outlet>
  </div>
  @if(!isGeospatial()) {
    <ifp-footer class="ifp-main__footer"></ifp-footer>
  }
  @if (_slaService.permission().genAi && isGenAi) {
    <ifp-chat-bot></ifp-chat-bot>
     <!-- <ifp-gen-ai-screen-landing></ifp-gen-ai-screen-landing> -->
  }

</div>
@if (downloadService.printGeoSpatial()) {
  <router-outlet name="print"></router-outlet>
}

<app-ifp-modal #modalcertificate [modalClass]="'ifp-modal__template-certificate'">
  <app-survey-modal [surveyResponse]="surveyResponse" (closeModal)="closeModal()"></app-survey-modal>
</app-ifp-modal>

<app-ifp-journey-start (closeModal)="closeJourney($event)" *ngIf="showJourney && !this._journeyService.isExplore" [isShowJourney]="isStart" @fadeInOut></app-ifp-journey-start>
<app-user-journey></app-user-journey>

