import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  Input,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import BasemapGallery from '@arcgis/core/widgets/BasemapGallery';
import Expand from '@arcgis/core/widgets/Expand';
import MapView from '@arcgis/core/views/MapView';
import Basemap from '@arcgis/core/Basemap';
import LocalBasemapsSource from '@arcgis/core/widgets/BasemapGallery/support/LocalBasemapsSource';
import { TranslateService } from '@ngx-translate/core';
import { CensusServiceService } from 'src/app/scad-insights/geospatial-revamp/census-service.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import {SubSink} from "subsink";

@Component({
  selector: 'ifp-basemap-gallery',
  standalone: true,
  template: '<div #expandNode></div>',
})
export class BasemapGalleryComponent implements OnInit, OnD<PERSON>roy {
  @Input() view!: MapView | undefined;
  private basemapGallery: BasemapGallery | undefined;
  private expand: Expand | undefined;

  subsink: SubSink = new SubSink();
  public language: string = 'en';

  constructor(
    private _translate: TranslateService,
    private themeService: ThemeService,
    private gisSharedService: CensusServiceService,
  ) {}

  ngOnInit() {
    this.subsink.add(
      this.themeService.defaultLang$.subscribe((lang: string) => {
        this.language = lang;
      })
    );
   this.initializeWidget();
  }


  ngOnChanges(changes: SimpleChanges) {
    if (changes['view'] && !changes['view'].firstChange && this.view) {
      this.initializeWidget();
    }
  }

 private initializeWidget() {
  if (this.view) {
    this.destroyWidgets();
    var customLayerDiv = document.createElement('div');
    customLayerDiv.id = "customLayersContainerBaseMap";
    
    let basemapLight = new Basemap({
      portalItem: { id: '23c6e7a1d7b543e1abf9bf5c4689a1fb' }, 
      title: 'Custom Light Basemap',
    });
    
    let basemapDark = new Basemap({
      portalItem: { id: 'd34e23873bd0443fb79b596e694a430a' },
      title: 'Custom Dark Basemap',
    });
    
    let basemapImagery = new Basemap({
      portalItem: { id: '13a9ca41fb1a455994d58f60de639552' },
      title: 'Imagery Hybrid',
    });
    
    let basemapsForGallery = new LocalBasemapsSource({
      // basemaps: [basemapLight, basemapDark, basemapImagery],
      basemaps: [basemapLight, basemapImagery],
    });
    
    // Create the BasemapGallery
    this.basemapGallery = new BasemapGallery({ 
      view: this.view,
      source: basemapsForGallery,
    } as __esri.BasemapGalleryProperties);

    // Watch for basemap selection changes
    this.basemapGallery.watch('activeBasemap', (newBasemap) => {
      if (newBasemap && newBasemap.portalItem) {
        const selectedBasemapId = newBasemap.portalItem.id;
        
        // Check if dark basemap is selected
        if (selectedBasemapId === 'd34e23873bd0443fb79b596e694a430a') {
          this.onDarkBasemapSelected();
        } else if (selectedBasemapId === '23c6e7a1d7b543e1abf9bf5c4689a1fb') {
          this.onLightBasemapSelected();
        } else if (selectedBasemapId === '13a9ca41fb1a455994d58f60de639552') {
          this.onImageryBasemapSelected();
        }
      }
    });

 
    // Create the Expand instance
    this.expand = new Expand({
      view: this.view,
      id: "customLayersContainerBaseMap",
      content: this.basemapGallery,
      expandIcon: 'basemap',
      group: 'bottom-left',
      autoCollapse: true,
      expandTooltip: this.language == 'en' ? 'Basemap Gallery' : 'نمط عرض الخريطة',
      collapseTooltip: this.language == 'en' ? 'Collapse' : 'تصغير'
    } as __esri.ExpandProperties);
    
    this.view.ui.add(this.expand, {
      position: 'bottom-left',
      index: 1,
    });
    
    this.expand?.watch('expanded', (isExpanded) => {
      if (isExpanded) {
        this.gisSharedService.registerWidget(this.expand!);
      }
    });
    
    setTimeout(() => {
      const galleryItems = document.querySelectorAll(
        '.esri-basemap-gallery__item-title'
      );
      galleryItems.forEach((item, index) => {
        if (index === 0) {
          item.innerHTML = this.language == 'en' ? 'Light Basemap' : 'خريطة الأساس';
        } else if (index === 1) {
          item.innerHTML = this.language == 'en' ? 'Dark Basemap' : 'خريطة الأساس';
        } else if (index === 2) {
          item.innerHTML = this.language == 'en' ? 'Satellite Imagery' : 'الصور الساتلية';
        }
      });
    }, 1500);
    
    this.subsink.add(
      this.themeService.defaultTheme$.subscribe(val => {
        if (val == 'dark') {
          if(this.view) {
            this.view.map.basemap = basemapDark;
          }
        } else {
          if(this.view) {
            this.view.map.basemap = basemapLight;
          }
        }
      })
    );
  }
}

  // Helper methods to handle basemap selection
  private onDarkBasemapSelected(): void {
    this.gisSharedService.setGeoMapTheme("dark"); 
  }

  private onLightBasemapSelected(): void {
    this.gisSharedService.setGeoMapTheme("light"); 
  }

  private onImageryBasemapSelected(): void {
    this.gisSharedService.setGeoMapTheme("imagery"); 
  }

  private destroyWidgets() {
    if (this.expand) {
      this.view?.ui.remove(this.expand);
      this.expand.destroy();
      this.expand = undefined;
    }
    if (this.basemapGallery) {
      this.basemapGallery.destroy();
      this.basemapGallery = undefined;
    }
  }

  ngOnDestroy() {
    this.destroyWidgets();
  }

}
