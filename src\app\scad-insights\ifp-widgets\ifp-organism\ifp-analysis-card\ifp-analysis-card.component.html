<div #analysisCard class="ifp-analysis__outer">
  @if(unauthorized && addMyAppsLanding) {
  <ifp-unauthorized-card [nodeId]="id" [contentType]="contentType"
    [isPending]="isAccessPending"></ifp-unauthorized-card>
  } @else {
  <ifp-card *ngIf="(firstLoader ? !loader : true) ; else load">
    <ifp-analysis-card-header [addMyApps]="addMyApps" [id]="this.id" [title]="this.heading"
      [link]="url"
      [addMyAppsLanding]="addMyAppsLanding" [contentType]="'analytical-apps'" [notification]="notificationSelector"
      [enableNotification]="tagName !== 'Dashboard'"
      [externalLink]="type === chartConstants['ECIInsights'] || type === chartConstants['basketInsights'] || type === chartConstants['ECIInsightsOptimization']"
      [externalUrl]="type === chartConstants['ECIInsights'] ? (externalUrls[type]+lang) : type === chartConstants['ECIInsightsOptimization'] ? getECIOptimizationUrl(externalUrls[type]) : (_themeService.defaultTheme === 'light' ? externalUrls[type] : externalUrlsDark[type])"
      *ngIf="domains" [(small)]="small" [icons]="getDomains()" (resized)="resized($event)"
      [isExpandShow]="!(type === chartConstants['Tableau-Internal']|| appTypeData === chartConstants.tableau_internal || type === chartConstants['ECIInsights'] || type === chartConstants['ECIInsightsOptimization'] || type === chartConstants['basketInsights'])"
      (notificationEvent)="addNotification($event, analysisCard)" (openInNewTab)="scrollTop($event)"
      (myApps)="myappsEvent($event)"></ifp-analysis-card-header>
    <div class="ifp-analysis">
      <div class="ifp-analysis__left" [ngClass]="{'ifp-analysis__left--minimize': !this.small}">
        <div class="ifp-analysis__heading" [appIfpTooltip]="heading" [extraSpaceTop]="-10"
          [disableTooltip]="heading ? heading.length < textLimit : false" [delay]="3000">
          {{ heading ? heading.charAt(0).toUpperCase() + heading.slice(1): '' | translate}}
        </div>
        <ng-container *ngIf="currencyData.currency | shortNumber:numberType as currencyData">
          <div class="ifp-analysis__value" *ngIf="currencyData.value && appTypeData !== chartConstants.LIVEABILITY"
            [ngStyle]="{'color': currencyData.color }"
            [ngClass]="{'ifp-analysis__value--percentage':currencyData?.key === '%'}">
            <div class="ifp-analysis__currency-currency">{{currencyData.value}}{{currencyData?.key ? currencyData?.key :
              '' | translate}}</div>
            <div class="ifp-analysis__currency-warpper">
              <div class="ifp-analysis__currency-words">{{currencyData.currencyWords ? currencyData.currencyWords : ''|
                translate}}</div>
              <div class="ifp-analysis__currency-subtitle">
                <span *ngIf="data?.unit">{{data.unit}}</span>
              </div>
            </div>
          </div>
        </ng-container>

        <div class="ifp-analysis__substitle">
          {{subTitle | quotRemove}}
        </div>

        <!-- @if (filterKeys?.length > 0 ) {
                <p class="ifp-analysis__filter" [appIfpTooltip]="appliedFilterTooltip">
                  @for ( filter of filterKeys; let i = $index; track i) {
                    {{filter.label |titlecase}}: {{filter.value[0] |titlecase}}  @if (filterKeys.length != i+1) {|}
                  }
                </p>
                } -->

        <div class="ifp-analysis__txt-icons">
          <div class="ifp-analysis__txt-icon" *ngIf="publish">
            <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="publish" [key]="'Updated date'"></ifp-icon-text>
          </div>
          <div class="ifp-analysis__txt-icon" *ngIf="source">
            <ifp-icon-text [icon]="'ifp-icon-text'" [text]="source" [key]="'Source'"></ifp-icon-text>
          </div>
          <div class="ifp-analysis__txt-icon" *ngIf="tagName">
            <ifp-button [buttonClass]="'ifp-btn--disabled ifp-btn--md ifp-btn--capitalize'"
              [label]="tagName"></ifp-button>
          </div>
        </div>


        <div class="ifp-analysis__footer">
          @if (security && security.name) {
            <ifp-tag class="ifp-analysis__tag" [isBoxView]="true" [background]="'transparent'" [tagName]="security.name" [color]="security.color ?? ''" [infoHead]="('Data classification' | translate) + ': ' + security.name" [info]="security.description ?? ''"></ifp-tag>
          }
          <div class="ifp-analysis__footer-inner">
            @if (isHideShareCheck) {
              <app-ifp-checkbox [appIfpTooltip]="'Check to select indicator' | translate" [extraSpaceTop]="20"
                [type]="'checkbox'" [enableFor]="true" [hideLabel]="true" [id]="id" (checkedEvent)="selectNode($event)"
                [defualtChecked]="isSelected"></app-ifp-checkbox>
              }
              @if (remove) {
                <div class="ifp-analysis__remove">
                  <ifp-button [buttonClass]="buttonClass.icon" [buttonColor]="buttonColor.black"
                    [iconClass]="'ifp-icon-round-cross'" [tooltipValue]="'Remove card'| translate"
                    (ifpClick)="removeEvent()"></ifp-button>
                </div>
              }
          </div>
        </div>
      </div>
      <!-- <div class="ifp-analysis__right" *ngIf="!this.small">
            <div class="ifp-analysis__rate-card">
              <ifp-rating></ifp-rating>
            </div>
            <div class="ifp-analysis__chart">
              <ifp-highcharts *ngIf="chart" [chartName]="'barChart'" [data]="chartData" [height]="180"
                [chartName]="'barChart'"></ifp-highcharts>
            </div>
            <div class="ifp-analysis__selectors">
              <app-ifp-month-selector class="ifp-analysis__month-selector" [filter]="periodFilter"
                (filterSelected)="filterSelected($event)"></app-ifp-month-selector>
            </div>
          </div> -->
      <div class="ifp-analysis__right" *ngIf="chart">
        <div class="ifp-analysis__rate-card" *ngIf="ratingMeta">
          <ifp-rating [title]="ratingMeta.title" [value]="ratingMeta.value"
          [subValueColor]="rating.value < 0 ? (rating.invertColor ? '#1c9452' :'#FF001E') : (rating.invertColor ?'#FF001E' :'#1c9452')"
          [arrowType]="rating.value < 0 ? (rating.invertArrows ? 'up' : 'down') :  (rating.invertArrows ? 'down' : 'up')"></ifp-rating>
        </div>
        @if (chartData?.length > 0 || chartDataCorrelation?.length > 0) {
          <div class="ifp-analysis__chart">
            @if (chartType === chartConstants.LINECHART) { 
              <app-ifp-analytic-line-chart [chartData]="chartData"
                #chartComponent [height]="180" [isDatalabel]="false"></app-ifp-analytic-line-chart>
            }
            @if (chartType === chartConstants.correlation) {
              <app-ifp-analytic-line-chart [yaxisLabel]="chartCardData.yAccessor?.label ?? ''" [legend]="false" [xAxisLabel]="chartCardData.xAccessor.label.toUpperCase()" [yAxisLineWidth]="1" [spacingLeft]="0" [spacingRight]="0" [chartData]="chartDataCorrelation" [xAxisCatogory]="xAxis" [plotLinesY]="plotLinesY" [showGridLineX]="false" [xAxisLabelType]="xAxisLabelType" [enableCustomTooltip]="true" [customTooltipData]="tooltipData" [height]="250"></app-ifp-analytic-line-chart>
              }
            @if (chartType === chartConstants.TREECHART) {
              <app-ifp-analytic-tree-chart [treeData]="chartData"
                #chartComponent [initialData]="baseData" [height]="180" [width]="400"
                [fontSize]="'.8rem'"></app-ifp-analytic-tree-chart>
            }
          </div>
        }
        <div>

          @if (appTypeData == chartConstants.LIVEABILITY) {
          <app-ifp-geo-map [mapData]="liveabilityData?.dashboardMapData?.data" [height]="280"
            [isDataLabel]="true"></app-ifp-geo-map>
          }

        </div>
        <div class="ifp-analysis__selectors">
          <app-ifp-month-selector *ngIf="analyticalData && analyticalData?.indicatorFilters?.length > 0"
            [filter]="periodFilters" class="ifp-analysis__month-selector"
            (filterSelected)="filterBasedOnPeriod($event)"></app-ifp-month-selector>
        </div>
      </div>

    </div>
  </ifp-card>
  }
</div>

<!-- <app-ifp-modal #subscribeNotification [modalClass]="'ifp-modal__box-sm'" [enableOverlay]="false" [modalModifier]="'ifp-modal--alert-box'">
      <app-ifp-alert-box *ngIf="isSubscNotifOpen"  [alertMessage]="notificationMessage.subscribeEmail" (alertResponse)="setEmailNotifStatus($event)" (closeAlert)="closeModal()"></app-ifp-alert-box>
    </app-ifp-modal> -->

<div #alertBox class="ifp-alert-container">
  <app-ifp-alert-box [alertMessage]="notificationMessage.subscribeEmail" (alertResponse)="setEmailNotifStatus($event)"
    (closeAlert)="closeModal()" [ngStyle]="{'display': isSubscNotifOpen ? 'block' : 'none'}"></app-ifp-alert-box>
</div>


<ng-template #load>
  <app-ifp-card-loader class="ifp-loader" [type]="'small'"></app-ifp-card-loader>
</ng-template>
