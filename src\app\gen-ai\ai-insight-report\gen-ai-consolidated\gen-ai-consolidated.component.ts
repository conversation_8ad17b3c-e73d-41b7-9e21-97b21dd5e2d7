import {
  Component,
  inject,
  OnInit,
  signal,
  viewChild,
  WritableSignal,
} from '@angular/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpBreadcrumbsComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import {
  buttonClass,
  buttonIconPosition,
} from 'src/app/scad-insights/core/constants/button.constants';
import {
  Drivers,
  GenAiKeyHeaderComponent,
} from '../gen-ai-key-header/gen-ai-key-header.component';
import { AsyncPipe, NgClass } from '@angular/common';
import { AiInsightReportConsolidatedDomainComponent } from '../ai-insight-report-consolidated-domain/ai-insight-report-consolidated-domain.component';
import {
  AiInsightReportPopupComponent,
  InsightSave,
} from '../ai-insight-report-popup/ai-insight-report-popup.component';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { SubSink } from 'subsink';
import { aiInsightApi } from '../constants/ai-insight-api.contants';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import {
  ConsolidatedInsights,
  KeyDriversDataConsolidated,
} from '../interface/consolidated.interface';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import {
  GenarateDriver,
  GenrateKeyInsights,
  InsightComparison,
  InsightSentimentanalysis,
  KeyinsightInsights,
  ResponseInsight,
} from '../interface/insights.interface';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { slaService } from 'src/app/scad-insights/core/services/sla/sla.service';
import { AiInsightReportFooterComponent } from '../ai-insight-report-footer/ai-insight-report-footer.component';
import { ImpactInsights } from '../interface/consolidated.interface';
import { Store } from '@ngrx/store';
import { getDomainIconList } from 'src/app/scad-insights/store/domain-icon/domain-icon.action';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { environment } from 'src/environments/environment';
import {
  genarateKeys,
  genarateKeyValue,
} from '../constants/ai-insight.constant';
import { cloneDeep } from 'lodash';
import { IfpStepData, IfpStepperComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-stepper/ifp-stepper.component";

@Component({
  selector: 'ifp-gen-ai-consolidated',
  imports: [
    IfpBreadcrumbsComponent,
    TranslateModule,
    IfpButtonComponent,
    GenAiKeyHeaderComponent,
    AsyncPipe,
    AiInsightReportConsolidatedDomainComponent,
    AiInsightReportPopupComponent,
    IfpModalComponent,
    IfpSpinnerComponent,
    AiInsightReportFooterComponent,
    NgClass,
    IfpStepperComponent
],
  templateUrl: './gen-ai-consolidated.component.html',
  styleUrl: './gen-ai-consolidated.component.scss',
})
export class GenAiConsolidatedComponent implements OnInit {
  public buttonIconPosition = buttonIconPosition;
  public modalService = inject(IfpModalService);
  private _api = inject(ApiService);
  public loader = signal(true);
  public subs = new SubSink();
  private _modalService = inject(IfpModalService);
  private _slaService = inject(slaService);
  private _store = inject(Store);
  public previewGenaratePopup = signal(false);
  public modal = viewChild<IfpModalComponent>('modal');
  public popup = viewChild<AiInsightReportPopupComponent>('popup');
  public modelAnimate = signal(false);
  public genaratedPreview = signal(false);
  private _router = inject(Router);
  public sectionOne = '';
  public sectionTwo = '';
  public stepsData = signal<IfpStepData[]>([]);
  public completedSteps = signal(0);
  public modalType: WritableSignal<
    | 'header'
    | 'keyInsights'
    | 'table'
    | 'decription'
    | 'keyInsightsOnly'
    | 'gen'
  > = signal('table');
  public pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home',
    },
    {
      title: 'AI Insight Report Generator',
      route: '',
    },
  ];
  public title = signal('');
  public subTitle = signal('');
  public impact: {
    heading: string;
    text: string;
    whether_positive_trend: boolean;
  }[] = [];
  public comparison: InsightComparison | undefined;
  private _toaster = inject(ToastrService);
  public themeService = inject(ThemeService);
  public report: WritableSignal<KeyDriversDataConsolidated> = signal({});
  public temp: WritableSignal<KeyDriversDataConsolidated> = signal({});
  public buttonClass = buttonClass;
  private _activeRoute = inject(ActivatedRoute);
  private id = signal('');
  public _downloadService = inject(DownLoadService);
  public previewExternal = signal(false);
  public previewNot = signal(true);
  public popupData = signal(false);
  public domainId = '';
  public downloadProgress = signal(false);
  public loaderSubmitProgress = signal(false);
  public disableTranslate = signal(true);
  ngOnInit(): void {
    this.subs.add(
      this._activeRoute.params.subscribe((params) => {
        this.id.set(params['id']);
      })
    );
    const token = this._activeRoute.snapshot.queryParams['token'];
    if (token) {
      this._slaService.slaLoader$.next(false);
      this.genaratedPrewviewData(token);
      this.previewExternal.set(true);
      this.previewNot.set(false);
      this._store.dispatch(getDomainIconList({ token: token, lang: 'en' }));
    } else {
      this.genaratedData();
    }
  }

  genaratedData() {
    this.loader.set(true);
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.getGenaratedDataConsolidated + this.id())
        .subscribe((data: ConsolidatedInsights) => {
          this.report.set(data.keyDriversData);
          this.loader.set(false);
           const step: IfpStepData[] = [];
          data.keyDriversData?.workFlow?.stepNames.forEach((dataValue, index) => {
            step.push({
              counter: index+1,
              name: dataValue,
              stepCount: index+1,
            });
            if(dataValue === data?.keyDriversData?.workFlow?.currentStep) {
                if((data.keyDriversData.workFlow.stepNames.length-1 === index)) {
                  this.completedSteps.set(index + 1);
              } else {
                  this.completedSteps.set(index);
              }
            }

          });
          this.stepsData.set(step);

        })
    );
  }
  callEditHeader() {
    this.sectionOne = genarateKeys.summery;
    this.sectionTwo = genarateKeys.drivers;
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Key Drivers');
    this.subTitle.set('Cards view');
    this.modalType.set('header');
    this.popup()?.setValue(
      this.report().key_drivers?.ai_summary ?? '',
      '',
      [],
      this.report().key_drivers?.drivers ?? []
    );
  }

  callKeyInsightsEdit(
    value:
      | {
          impacts: ImpactInsights | undefined;
          keyinsights: KeyinsightInsights[];
        }
      | undefined,
    id: string
  ) {
    this.sectionOne = genarateKeys.impact;
    this.sectionTwo = genarateKeys.keyInsight;
    this.domainId = id;
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Impact');
    this.subTitle.set('Key Insights:');
    this.modalType.set('keyInsights');
    this.popup()?.setValue(
      value?.impacts?.text ?? '',
      value?.impacts?.heading ?? '',
      value?.keyinsights,
      [],
      { headers: [], rows: [] },
      value?.impacts?.chart
    );
  }

  callComparisonEdit(value: InsightComparison | undefined, id: string) {
    this.sectionOne = genarateKeys.comparison;
    this.sectionTwo = genarateKeys.comparison;
    this.domainId = id;
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Comparison');
    this.subTitle.set('Comparison Table');
    this.modalType.set('table');
    this.popup()?.setValue('', '', [], [], value);
  }
  callSentimentEdit(value: InsightSentimentanalysis | undefined, id: string) {
    this.sectionOne = genarateKeys.sentimental;
    this.sectionTwo = genarateKeys.sentimental;
    this.domainId = id;
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Sentiment Analysis');
    this.subTitle.set('Sentiment Analysis');
    this.modalType.set('decription');
    this.popup()?.setValue(
      value?.key_insights_desc,
      '',
      [],
      [],
      { headers: [], rows: [] },
      value?.chart
    );
  }

  genaratedPrewviewData(token: string) {
    this.loader.set(true);
    this.subs.add(
      this._api
        .getMethodPreview(
          aiInsightApi.getGenaratedDataConsolidated + this.id(),
          {},
          token
        )
        .subscribe((data: ConsolidatedInsights) => {
          this.report.set(data.keyDriversData);
          this.loader.set(false);
        })
    );
  }

  genarateAll(value: { type: string }) {
    switch (value.type) {
      case 'header': {
        this.genareteKeyDrivers();
        break;
      }
      case 'table': {
        this.genarateComparision();
        break;
      }
      case 'decription': {
        this.genarateSentimental();
        break;
      }
      case 'keyInsights': {
        this.genareteImpact();

        break;
      }
    }
  }

  genareteAllRecord(event: InsightSave) {
    console.log(event);
    this.closeModal();
    this._router.navigateByUrl('/insight-report-list');
  }

  genareteImpact() {
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.genarateImpact + this.id())
        .subscribe((data: GenrateKeyInsights) => {
          // this.popup()?.setValue(data.description ?? '',data.sub_heading ??'',data.key_insights, this.report()?.key_drivers.drivers,this.report()?.comparison);
        })
    );
  }

  genareteKeyDrivers() {
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.genarateKeyDrivers + this.id())
        .subscribe((data: GenarateDriver) => {
          // this.popup()?.setValue(data.description,'',this.report()?.key_insights, data.card_view);
        })
    );
  }

  genarateComparision() {
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.genarateComparision + this.id())
        .subscribe((data: { comparison: InsightComparison }) => {
          // this.popup()?.setValue(this.report()?.impact.text ?? '',this.report()?.impact.heading ??'',this.report()?.key_insights, this.report()?.key_drivers.drivers,data.comparison);
        })
    );
  }

  genarateSentimental() {
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.genarateSentimental + this.id())
        .subscribe((data: { key_insights_desc: string }) => {
          // this.popup()?.setValue(data.key_insights_desc,'',this.report()?.key_insights, this.report()?.key_drivers.drivers,this.report()?.comparison);
        })
    );
  }
  save(event: InsightSave) {
    switch (event.type) {
      case 'header': {
        this.saveKeyInsights(event);
        break;
      }
      case 'table': {
        this.saveComparison(event);
        break;
      }
      case 'decription': {
        this.editSentimetal(event);
        break;
      }
      case 'keyInsights': {
        this.saveImpact(event);
        break;
      }
    }
  }
  cancelGenerate() {
    this.previewNot.set(true);
    this.genaratedPreview.set(false);
    this.report.update(() => {
      return this.temp();
    });
  }
  submitGenerate() {
    this.previewNot.set(true);
    this.genaratedPreview.set(false);
    const keys = Object.keys(this.report() ?? {});
    const dataValue: Record<string, any>[] = [];
    keys.forEach((keys) => {
      dataValue.push({
        [keys]: this.report()?.[keys as keyof KeyDriversDataConsolidated],
      });
    });
    const editSentimetal = {
      section: 'all',
      data: dataValue,
    };

    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.saveAllData + this.id(),
          editSentimetal
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
        })
    );
  }

  saveImpact(event: InsightSave) {
    const impact = {
      section: 'impact',
      domain_id: this.domainId,
      data: [
        {
          impact: {
            heading: event.heading,
            text: event.description,
            chart: event.chart,
          },
        },
        {
          key_insights: event.keyInsights,
        },
      ],
    };
    this.subs.add(
      this._api
        .patchMethodRequest(aiInsightApi.impactEdit + this.id(), impact)
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  saveKeyInsights(event: InsightSave) {
    const keyInsights = {
      section: 'key_drivers',
      data: [
        {
          key_drivers: {
            ai_summary: event.description,
            drivers: event.drivers,
          },
        },
      ],
    };
    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.editKeyInsights + this.id(),
          keyInsights
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  saveComparison(event: InsightSave) {
    const comparison = {
      domain_id: this.domainId,
      section: 'comparison',
      data: [{ comparison: event.insightComparison }],
    };
    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.editComparision + this.id(),
          comparison
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  editSentimetal(event: InsightSave) {
    const editSentimetal = {
      domain_id: this.domainId,
      section: 'sentiment',
      data: [{ key_insights_desc: event.description }],
    };

    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.editSentimetal + this.id(),
          editSentimetal
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  regenarateAll() {
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Regenerate All');
    this.modalType.set('gen');
    this.previewGenaratePopup.set(true);
  }

  genarate(event: InsightSave) {
    console.log(this.domainId);
    const value: {
      report_id: string;
      section: string | undefined;
      user_prompt: string | undefined;
      domain_id?: string;
    } = {
      report_id: this.id(),
      section: event.section,
      user_prompt: event.value,
    };
    if (event.type !== 'header') {
      value['domain_id'] = this.domainId;
    }
    this.modelAnimate.set(true);
    this.subs.add(
      this._api.postMethodRequest(aiInsightApi.genarateCards, value).subscribe({
        next: (data) => {
          switch (event.type) {
            case 'header': {
              this.popup()?.setParticularValue(
                event.section,
                data?.[genarateKeyValue.summery] ?? '',
                '',
                data?.[genarateKeyValue.keyInsight],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison]
              );
              break;
            }
            case 'table': {
              this.popup()?.setParticularValue(
                event.section,
                data?.[genarateKeyValue.summery] ?? '',
                '',
                data?.[genarateKeyValue.keyInsight],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison]
              );
              break;
            }
            case 'decription': {
              this.popup()?.setParticularValue(
                event.section,
                data?.[genarateKeyValue.sentimental] ?? '',
                '',
                data?.[genarateKeyValue.keyInsight],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison]
              );
              break;
            }
            case 'keyInsights': {
              this.popup()?.setParticularValue(
                event.section,
                data?.text ?? '',
                data?.heading ?? '',
                data?.[genarateKeyValue.keyInsight] ?? [],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison],
                data?.chart
              );
              break;
            }
            case 'gen': {
              this.temp.set(cloneDeep(this.report()));
              this.report.update(() => {
                return data;
              });
              this.previewNot.set(false);
              this.genaratedPreview.set(true);
              this.closeModal();
              break;
            }
          }

          this.modelAnimate.set(false);
        },
        error: () => {
          this.modelAnimate.set(false);
        },
      })
    );
  }

  genareteKeyCards(event: InsightSave) {
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.genarateCards)
        .subscribe((data: { card_view: Drivers[] }) => {
          // this.popup()?.setParticularValue(event.type, [], data.card_view);
        })
    );
  }

  submit() {
    this._toaster.info(
      'The PDF for the Consolidated page is being generated and will be attached to the external system soon. Please expect some delay.'
    );
    this.loaderSubmitProgress.set(true);
    //this._router.navigateByUrl('/insight-report-list');
    this.subs.add(
      this._api
        .postMethodRequest(aiInsightApi.reviewSubmitConsolidated, {report_id : this.id()})
        .subscribe((data: { key_insights: KeyinsightInsights[] }) => {
          // this.popup()?.setParticularValue(event.type,data.key_insights, []);
          this._toaster.success(
            'Sanadkom ticket has been created for the consolidate and sent for publication approval.'
          );
          this.loaderSubmitProgress.set(false);
          this._router.navigateByUrl('/insight-report-list');
        })
    );
  }

  genarateKeyInsights(event: InsightSave) {
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.genarateKeyInsights)
        .subscribe((data: { key_insights: KeyinsightInsights[] }) => {
          // this.popup()?.setParticularValue(event.type,data.key_insights, []);
        })
    );
  }

  downloadReport() {
    const query = {
      report_id: this.id(),
      report_type: this.report()?.report_type,
    };
    this.downloadProgress.set(true);
    this.subs.add(
      this._api
        .getDownloadRequest(
          `${environment.baseUrl}${environment.apiVersion}${aiInsightApi.reportDownload}`,
          query
        )
        .subscribe({next:(data) => {
          const matches = data.headers
            .get('Content-Disposition')
            ?.match(/"(.*?)"/);
          const nameValue = matches ? matches[1] : '.pdf';
          this.downloadProgress.set(false);
          this._downloadService.downloadFiles(data.body, nameValue);
        },
        error: ()=> {
          this.downloadProgress.set(false);
        }
      })
    );
  }

  closeModal() {
    this.popupData.set(false);
    this.modal()?.removeModal();
    this._modalService.removeAllModal();
    this.previewGenaratePopup.set(false);
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
