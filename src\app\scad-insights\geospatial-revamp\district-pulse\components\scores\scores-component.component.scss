@use "../../../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-scores-container {
  z-index: 2;
  position: absolute;
  margin: 80px 50px;
  display: flex;
  flex-direction: column;
}

.score-card {
  z-index: 2;
  padding: 15px;
  width: 250px;
  background: #ffffff99;
  transition: height 0.5s ease, background 0.5s ease;
  border-radius: 15px 15px 0px 0px;
  overflow: hidden;
  cursor: pointer;

  .score-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .right-icons em {
      margin: 0px 5px;
    }
    
    .ifp-icon {
      cursor: pointer;
      font-size: 18px;
    }
    
    .left-icons em {
      font-size: 24px;
    }
    
    .right-icons {
      position: relative;
    }
  }
}

.district-score {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .district-score-values {
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-weight: bold;

    #score-label {
      font-weight: normal;
      font-size: 10px;
    }
  }
  
  .district-score-chart {
    width: 130px;
    height: 130px;
    position: relative;
    top: -50px;
  }
}

.score-body {
  margin-top: 10px;
  
  .district-score-disclaimer {
    span {
      font-size: 10px;
      font-weight: 500;
    }
    margin-top: -80px;
  }
}

.indicator-card {
  z-index: 2;
  padding: 0px 15px;
  width: 250px;
  background: #ffffff99;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  margin-top: 3px;
  cursor: pointer;
  
  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(2px);
  }

  .score-indicator {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    
    .indicator-title span {
      font-size: 14px;
      font-weight: bold;
    }
    
    .indicator-chart {
      width: 90px;
      height: 90px;
    }
    
    .indicator-expand {
      padding: 5px;
      border-radius: 50%;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      
      em {
        font-size: 12px;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      &:hover {
        background-color: rgba(0, 123, 255, 0.1);
        transform: scale(1.1);
        
        em {
          transform: translateX(2px);
        }
      }
      
      // Active arrow state
      .arrow-active {
        transform: rotate(90deg);
      }
    }
  }
}

.indicator-details-wrapper {
  position: absolute;
  left: 280px; 
  top: 0;
  z-index: 3;
  
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.indicator-details--hidden {
    transform: translateX(100%);
    opacity: 0;
    pointer-events: none;
  }
  
  // Visible state - slides into view
  &.indicator-details--visible {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
  }
}

// Enhanced active indicator styling
.active-indicator {
  background-color: #FFF !important;
  
  .indicator-expand {
 
    em {
      transform: rotate(90deg);
    }
  }
}

.last-indicator {
  border-radius: 0px 0px 15px 15px;
}

// Responsive design
@media (max-width: 1200px) {
  .indicator-details-wrapper {
    left: 260px;
  }
}

@media (max-width: 768px) {
  .ifp-scores-container {
    margin: 20px 20px;
  }
  
  .indicator-details-wrapper {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.indicator-details--hidden {
      transform: translateY(100%);
    }
    
    &.indicator-details--visible {
      transform: translateY(0);
    }
  }
  
  .score-card,
  .indicator-card {
    width: 100%;
    max-width: 300px;
  }
}

// Animation keyframes for additional effects
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

