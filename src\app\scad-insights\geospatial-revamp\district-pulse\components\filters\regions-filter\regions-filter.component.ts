import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule  } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { CommonService } from '../../../../common.service';


@Component({
  selector: 'ifp-regions-filter',
  standalone: true,
  imports: [ 
    CommonModule,
    FormsModule,
    TranslateModule
  ],
  templateUrl: './regions-filter.component.html',
  styleUrl: './regions-filter.component.scss'
})
export class RegionsFilterComponent {

  @Input() menuId!: string;
  @Input() openMenu!: string | null;
  @Output() toggleDropdownMenu = new EventEmitter<string>();

  constructor(
    private commonService: CommonService,
    private themeService: ThemeService) {
  }

  ngOnInit() {
  }

  get isMenuOpen(): boolean {
    return this.openMenu === this.menuId; 
  }

  toggleMenu(): void {
    this.toggleDropdownMenu.emit(this.menuId);
  }

}
