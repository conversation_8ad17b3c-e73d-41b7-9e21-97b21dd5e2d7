import { NgClass } from '@angular/common';
import { Component, ElementRef, EventEmitter, input, Input, OnChanges, OnDestroy, Output, Renderer2, SimpleChanges, ViewChild } from '@angular/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { SubSink } from 'subsink';

@Component({
  selector: 'ifp-range-slider',
  standalone: true,
  imports: [NgClass],
  templateUrl: './ifp-range-slider.component.html',
  styleUrl: './ifp-range-slider.component.scss'
})
export class IfpRangeSliderComponent implements OnChanges, OnDestroy {
  @ViewChild('rangeInput') rangeInput!: ElementRef;
  @ViewChild('rangeTooltip') rangeTooltip!: ElementRef;
  
  @Output() selectRange: EventEmitter<number> = new EventEmitter<number>();
  
  @Input() minRangeValue: number = 0;
  @Input() maxRangeValue: number = 100;
  @Input() decimals: number = 0; // Changed default to 0 for percentage
  @Input() rangeValue: number = 0;
  @Input() enableTooltip: boolean = true;
  @Input() sliderTheme: string = sliderThemes.default;
  @Input() unit: string = '%'; // Default to percentage
  @Input() step: number = 1; // Default step of 1
  
  public disable = input(false);
  public fillValue!: number;
  public dots: any[] = [];
  public subs: SubSink = new SubSink();
  public direction: string = localStorage.getItem('lang') ?? 'en';

  constructor(
    private readonly renderer: Renderer2, 
    private readonly _themeService: ThemeService
  ) {
    this.subs.add(
      this._themeService.defaultLang$.subscribe((theme: string) => {
        this.direction = theme === 'en' ? 'left' : 'right';
      })
    );
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['step'] && this.step > 1) {
      this.dots = new Array(Math.floor(this.maxRangeValue / this.step) + 1);
    }
    
    if (changes['rangeValue']) {
      setTimeout(() => {
        this.onChangeSlider(this.rangeValue);
      });
    }
  }

  onChangeSlider(value: number) {
    this.rangeValue = value;
    this.fillValue = ((value - this.minRangeValue) / (this.maxRangeValue - this.minRangeValue)) * 100;
    
    // Update thumb color based on position
    this.updateThumbColor(this.fillValue);
    
    // Position tooltip
    if (this.rangeTooltip && this.enableTooltip) {
      this.renderer.setStyle(
        this.rangeTooltip?.nativeElement, 
        this.direction, 
        `${this.fillValue}%`
      );
    }
    
    this.selectRange.emit(+value.toFixed(this.decimals));
  }

  private updateThumbColor(percentage: number) {
    if (!this.rangeInput?.nativeElement) return;
    
    let borderColor = '#4A90E2'; // Default blue
    
    if (percentage <= 50) {
      // Interpolate between blue and green
      const ratio = percentage / 50;
      borderColor = this.interpolateColor('#4A90E2', '#7ED321', ratio);
    } else {
      // Interpolate between green and orange
      const ratio = (percentage - 50) / 50;
      borderColor = this.interpolateColor('#7ED321', '#FF6B35', ratio);
    }
    
    this.renderer.setStyle(
      this.rangeInput.nativeElement,
      '--thumb-border-color',
      borderColor
    );
  }

  private interpolateColor(color1: string, color2: string, ratio: number): string {
    const hex1 = color1.replace('#', '');
    const hex2 = color2.replace('#', '');
    
    const r1 = parseInt(hex1.substr(0, 2), 16);
    const g1 = parseInt(hex1.substr(2, 2), 16);
    const b1 = parseInt(hex1.substr(4, 2), 16);
    
    const r2 = parseInt(hex2.substr(0, 2), 16);
    const g2 = parseInt(hex2.substr(2, 2), 16);
    const b2 = parseInt(hex2.substr(4, 2), 16);
    
    const r = Math.round(r1 + (r2 - r1) * ratio);
    const g = Math.round(g1 + (g2 - g1) * ratio);
    const b = Math.round(b1 + (b2 - b1) * ratio);
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}

export const sliderThemes = {
  default: '',
  green: 'green'
}