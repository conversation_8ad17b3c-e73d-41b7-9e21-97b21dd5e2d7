<div id="viewDivCensus">
    <div *ngIf="!isCustomizeOpen">

        <div class="map-widgets">
            <ifp-map-zoom  [view]="view"></ifp-map-zoom>
            <div  #selectionLevel class="esri-component level-selection">
                <div class="custom-multiselect" [ngClass]="{'open': isOpen}">
                    <div class="select-trigger" (click)="toggleDropdown()">
                        <span>
                            {{ language == 'ar' ? selectionLevelLabel.AR : selectionLevelLabel.EN }}:  
                            {{ language == 'ar' ? currentSelectionLevel.SELECT_AR :  currentSelectionLevel.SELECT_EN}}
                        </span>
                        <span> <i class="arrow-down"></i> </span> 
                    </div> 
                    <div class="options-container" *ngIf="isOpen">
                        <div class="options-list">
                            <label *ngFor="let level of selectionLevels" class="option-item">
                                <span (click)="toggleSelection(level)" class="region-name">
                                    {{ language == 'ar' ? level.SELECT_AR : level.SELECT_EN }}
                                </span>
                            </label>
                        </div>
                    </div> 
                </div>
            </div> 
            <!-- <ifp-measurements [view]="view"></ifp-measurements> -->
            <ifp-basemap-gallery [view]="view"></ifp-basemap-gallery>
            <ifp-map-search 
                [filtersData]="filtersData"  
                [view]="view"
                (locationChange)="locationChanged($event)"
                (filterChange)="filterChanged($event)" 
                (districtChange)="districtChanged($event)">
            </ifp-map-search>
            <div (click)="download('XL')" class="esri-component download-btn"> 
                <em class="ifp-icon ifp-icon-download"></em> 
            </div> 
            <ifp-poi [view]="view"></ifp-poi>
        </div>
    </div>
</div>