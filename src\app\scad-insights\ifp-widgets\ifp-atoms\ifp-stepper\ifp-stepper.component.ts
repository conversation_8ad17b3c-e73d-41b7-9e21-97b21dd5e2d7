import { NgClass } from '@angular/common';
import { Component, EventEmitter, Input, Output, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-ifp-stepper',
    templateUrl: './ifp-stepper.component.html',
    styleUrls: ['./ifp-stepper.component.scss'],
    imports: [TranslateModule, NgClass]
})
export class IfpStepperComponent {

  @Input() stepData?: IfpStepData[] = [];
  @Input() completed: number = 1;
  @Input() advance = false;
  @Input() icon!: string;
  @Input() pending!: number;
  @Input() count = true;
  @Input() showStepCount = true;
  public blueTheme = input(false);
  @Output() stepClickEvent = new EventEmitter();
  public showCount = input(false);

  public gradientTheme = input(false);
  stepClick(item: IfpStepData) {
    this.stepClickEvent.emit(item);
  }

}
export interface IfpStepData{
  counter:number;
  name: string;
  subName?: string;
  optional?: boolean;
  url?: string;
  stepCount: number;
}
