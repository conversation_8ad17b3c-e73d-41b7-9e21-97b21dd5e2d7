<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="43.286" height="43.286" viewBox="0 0 43.286 43.286">
  <defs>
    <filter id="Rectangle_9580" x="0" y="0" width="43.286" height="43.286" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path">
      <rect id="Rectangle_9577" data-name="Rectangle 9577" width="18.352" height="14.692" fill="none" stroke="#09202c" stroke-width="1"/>
    </clipPath>
  </defs>
  <g id="Group_10052" data-name="Group 10052" transform="translate(-1590 -265.145)">
    <g transform="matrix(1, 0, 0, 1, 1590, 265.15)" filter="url(#Rectangle_9580)">
      <rect id="Rectangle_9580-2" data-name="Rectangle 9580" width="25.286" height="25.286" rx="12.643" transform="translate(9 9)" fill="#fff"/>
    </g>
    <g id="Group_9888" data-name="Group 9888" transform="translate(1602.733 277.784)">
      <g id="Group_9887" data-name="Group 9887" transform="translate(0 0)" clip-path="url(#clip-path)">
        <path id="Path_14814" data-name="Path 14814" d="M5.448,3.908A2.3,2.3,0,0,1,9.56,3.737l-.418.655.721,1.293L9.128,6.971l.721,1.293-.654.93.365,1.117c-.017-.01-.312-.178-.742-.464" transform="translate(-0.992 -0.465)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14815" data-name="Path 14815" d="M12.546,9.886c-.411.27-.685.426-.685.426h0l-.446-1.068.736-.979-.722-1.292.736-1.286-.722-1.292.419-.656a2.3,2.3,0,0,1,4.113.171" transform="translate(-2.078 -0.465)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14816" data-name="Path 14816" d="M12.195,16.709l.087-1.936A1.517,1.517,0,0,1,13.3,13.392l.081-.025,1.251-.346a.694.694,0,0,0,.148-.061.68.68,0,0,0,.216-.186l.348-.728" transform="translate(-2.22 -2.193)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14817" data-name="Path 14817" d="M18.371,12.142l.353.649a.684.684,0,0,0,.364.247l1.249.346.081.025a1.518,1.518,0,0,1,1.016,1.381l.087,1.936" transform="translate(-3.344 -2.21)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14818" data-name="Path 14818" d="M14.006,17.485a5.908,5.908,0,0,0-.355-1.178" transform="translate(-2.485 -2.969)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <line id="Line_1895" data-name="Line 1895" x1="0.704" y1="2.934" transform="translate(12.752 11.582)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14819" data-name="Path 14819" d="M20.689,16.308a5.891,5.891,0,0,0-.355,1.178" transform="translate(-3.702 -2.969)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <line id="Line_1896" data-name="Line 1896" x1="0.907" y2="3.035" transform="translate(14.479 11.48)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14820" data-name="Path 14820" d="M15.589,13.033c0,.262.588.474,1.314.474s1.315-.213,1.315-.474" transform="translate(-2.838 -2.373)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14821" data-name="Path 14821" d="M14.97,10.5A2.113,2.113,0,0,0,16.8,12.041,2.114,2.114,0,0,0,18.631,10.5" transform="translate(-2.725 -1.912)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14822" data-name="Path 14822" d="M14.84,10.247c-.206.061-.45-.145-.546-.461s-.006-.624.2-.686" transform="translate(-2.595 -1.657)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14823" data-name="Path 14823" d="M19.446,10.247c.206.061.45-.145.546-.461s.006-.624-.2-.686" transform="translate(-3.54 -1.657)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14824" data-name="Path 14824" d="M14.969,7.535a1.9,1.9,0,0,1,.559-.548,4.347,4.347,0,0,0,2.9.283l.009,0a1.515,1.515,0,0,0,.095.858" transform="translate(-2.725 -1.272)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14825" data-name="Path 14825" d="M14.2,8.7a3,3,0,0,1-.066-1.49,2.789,2.789,0,0,1,1.788-2.065,2.239,2.239,0,0,1,2.12.6,2,2,0,0,1,.309.436.549.549,0,0,1,.058.037,1.593,1.593,0,0,1,.661,1.407c-.018.292-.069.747-.069.928" transform="translate(-2.561 -0.926)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14826" data-name="Path 14826" d="M.215,16.663l.321-2.024a1.614,1.614,0,0,1,.924-1.215l.187-.084,1.383-.631a.679.679,0,0,0,.4-.618v-.3" transform="translate(-0.039 -2.146)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14827" data-name="Path 14827" d="M6.631,11.789v.3a.682.682,0,0,0,.4.619l1.571.715a1.613,1.613,0,0,1,.924,1.215l.321,2.024" transform="translate(-1.207 -2.146)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14828" data-name="Path 14828" d="M3.132,13.152A2.065,2.065,0,0,0,5,14.07a2.078,2.078,0,0,0,1.86-.9" transform="translate(-0.57 -2.394)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14829" data-name="Path 14829" d="M1.1,14.249,1.618,15.7V17.11" transform="translate(-0.201 -2.594)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14830" data-name="Path 14830" d="M9.009,17.11V15.7l.516-1.452" transform="translate(-1.64 -2.594)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14831" data-name="Path 14831" d="M6.776,8.257A3.25,3.25,0,0,1,5.047,6.945L4.9,6.723l-.041.068A3.3,3.3,0,0,1,3.132,8.24" transform="translate(-0.57 -1.224)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14832" data-name="Path 14832" d="M3.317,9.31v.473a2.031,2.031,0,0,0,.593,1.435,1.307,1.307,0,0,0,.923.383h.155a1.662,1.662,0,0,0,1.467-.851A2.035,2.035,0,0,0,6.7,9.784V9.31" transform="translate(-0.604 -1.695)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14833" data-name="Path 14833" d="M8.516,8.734v1.71c0,.03,0,.059,0,.088a2.287,2.287,0,0,1-.168.789" transform="translate(-1.519 -1.59)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14834" data-name="Path 14834" d="M2.2,8.086a3.134,3.134,0,0,1,.512-1.735v0A2.516,2.516,0,0,1,4.8,5.177,2.529,2.529,0,0,1,6.933,6.425,3.139,3.139,0,0,1,7.4,8.086" transform="translate(-0.401 -0.943)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <path id="Path_14835" data-name="Path 14835" d="M2.338,11.247a2.287,2.287,0,0,1-.124-.54,2.558,2.558,0,0,1-.012-.263V8.733" transform="translate(-0.401 -1.59)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <line id="Line_1897" data-name="Line 1897" y2="1.365" transform="translate(4.431 11.675)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <line id="Line_1898" data-name="Line 1898" y1="0.9" transform="translate(9.176 0.176)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <line id="Line_1899" data-name="Line 1899" x1="0.779" y1="0.45" transform="translate(7.47 1.199)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
        <line id="Line_1900" data-name="Line 1900" y1="0.45" x2="0.779" transform="translate(10.103 1.199)" fill="none" stroke="#09202c" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.43"/>
      </g>
    </g>
  </g>
</svg>
