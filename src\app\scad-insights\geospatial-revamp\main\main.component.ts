import { Component, ViewChild, ChangeDetectorRef } from '@angular/core';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { GeoBreadcrumbsComponent } from '../components/breadcrumbs/geo-breadcrumbs.component';
import { IndicatorsComponent } from '../components/indicators/indicators-component.component';
import { FiltersComponent } from '../components/filters/filters.component';
import { MapComponentComponent } from '../components/map-component/map-component.component';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { NgClass } from '@angular/common';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { CommonService } from '../common.service';
import { CensusServiceService } from '../census-service.service';
import { geoMapKeys } from '../geospatial.contants';
import { LoadingMapComponent } from '../components/indicators/loading-map/loading-map.component';
import { SubSink } from 'subsink';
import { ThemeService } from '../../core/services/theme/theme.service';
import { ActivatedRoute, Router } from '@angular/router';

interface IndicatorObject {
  [indicatorKey: string]: IndicatorList;
}

interface GroupedIndicator {
  index: number;
  domains: number[];
  indicator: string;
}

interface Security {
  id: number;
  label: string;
  name: string;
}

interface IndicatorList {
  title: string;
  indicator_value: string;
  domain_id: number;
  card_position: string; // Or 'left' | 'right' | 'leftCenter' | 'rightCenter'
  light_icon: string;
  dark_icon: string;
  security: Security;
}

type IndicatorsList = Record<string, IndicatorList>;

@Component({
  selector: 'ifp-main',
  standalone: true,
  imports: [
    IndicatorsComponent,
    FiltersComponent,
    MapComponentComponent,
    LoadingMapComponent,
    TranslateModule,
    RouterModule,
    NgClass,
    GeoBreadcrumbsComponent
  ],
  templateUrl: './main.component.html',
  styleUrl: './main.component.scss'
})
export class MainComponent {

  private subscription = new Subscription();
  @ViewChild('indicatorCmp') indicatorCmp!: IndicatorsComponent;
  @ViewChild('filterCmp') filterCmp!: FiltersComponent;
  @ViewChild('mapCmp') mapCmp!: MapComponentComponent;
  public filtersBackupAfterCustomize: any = [];
  public isPreviewOpen: boolean = false;
  public isCustomizeOpen: boolean = false;
  public filterObject = geoMapKeys.defaultQueryParams;
  public filtersData: any = [];

  public indicatorsList: IndicatorsList = {};
  public indicatorsListValues: any = {};
  public leftIndicators: any = {};
  public rightIndicators: any = {};
  public leftCenterIndicators: any = {};
  public rightCenterIndicators: any = {};
  public lastQuarter: string = '';
  public lastYear: number = 0;
  public filterLoading: boolean = true;
  public indicatorsLoading: boolean = true;
  public userAccessPermissionloading: boolean = true;
  public userAccessPermission: any = {};
  public isMapLoadingComplete: boolean = false;
  public defaultLang: string = 'en';

  public geoConfig: Record<string, any> = {
    opacity: 60
  }

  public selectedMapButton!: string;
  public geoMapKeys = geoMapKeys;
  subsink: SubSink = new SubSink();
  public breadCrumb: any = [
    {
      title_en: 'Home',
      title_ar: 'الرئيسية',
      route: '/'
    },
    {
      title_en: 'Census Geomap',
      title_ar: 'خريطة التعداد',
      route: ''
    }
  ];


  constructor(
    private commonService: CommonService,
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private gisSharedService: CensusServiceService, private _themeService: ThemeService) {

    this._themeService.defaultLang$.subscribe(resp => {
      this.defaultLang = resp;
    })
  }

  ngOnInit(): void {
    this.loadDomainsPermission();
    this.loadFiltersData();
    this.loadIndicatorsListFromCMS();

    this.subscription = this.gisSharedService._isMapLoadingCompleted$.subscribe((loading: boolean) => {
      this.isMapLoadingComplete = loading;
    });

  }


  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  /**
  *
  * @param data Load indicators list from cms
  */
  private loadIndicatorsListFromCMS() {
    this.indicatorsLoading = true;
    const sub = this.commonService.getIndicatorsList()
      .pipe(
        finalize(() => {
          this.indicatorsLoading = false;
        })
      )
      .subscribe({
        next: (data: any) => {
          data.forEach((item: any, index: number) => {
            this.indicatorsList[index] = {
              indicator_value: item.indicator_value,
              title: item.title,
              domain_id: item.domain_id,
              card_position: item.card_position,
              light_icon: item.light_theme_light_icon,
              dark_icon: item.dark_theme_dark_icon,
              security: item.security
            };
          });
          data.forEach((item: any) => {
            this.indicatorsListValues[item.indicator_value] = item.indicator_value;
          });
          this.groupIndicatorsByPosition(this.indicatorsList);
        },
        error: (error) => {
          console.error('Error loading indicators:', error);
        }
      });

    this.subscription.add(sub);
  }

  /**
  *
  * @param data Load filters data
  */
  private loadFiltersData() {
    this.filterLoading = true;
    const sub = this.commonService.getAllFilters()
      .pipe(
        finalize(() => {
          this.filterLoading = false;
        })
      )
      .subscribe({
        next: (data: any) => {

          this.filtersData = data;
          let lastQuarterData = this.commonService.getLastQuarterAndYear(data.quarters);
          this.lastQuarter = lastQuarterData.QUARTER_CODE;
          this.lastYear = lastQuarterData.YEAR_CODE;
          this.filterObject.YEAR_CODE = [this.lastYear.toString()];
          this.filterObject.QUARTER_CODE = [this.lastQuarter];
          this.cdr.detectChanges();

          // update the breadCrumb with current selected year and quarter
          this.breadCrumb = [
            { title_en: 'Home', title_ar: 'الرئيسية', route: '/' },
            { title_en: `${'Census Geomap - ' + this.lastYear}`, title_ar: `${'خريطة التعداد -' + this.lastYear}`, route: '' }
          ];
        },
        error: (error) => {
          console.error('Error loading quarters:', error);
        }
      });
    this.subscription.add(sub);
  }


  private loadDomainsPermission() {
    this.userAccessPermissionloading = true;
    const sub = this.commonService.getDomainsPermissions()
      .pipe(finalize(() => { this.userAccessPermissionloading = false; }))
      .subscribe({
        next: (data: any) => {
          this.userAccessPermission = data || {};
          this.gisSharedService.setGeoMapAccessPermission(this.userAccessPermission);
          console.log("this.userAccessPermission", this.userAccessPermission)
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading permissions:', error);
          this.userAccessPermission = {};
        }
      });
    this.subscription.add(sub);
  }


  groupIndicatorsByPosition(indicatorsObj: IndicatorObject) {
    // First, convert the indicators object into an array of IndicatorList items
    const indicatorsArray = Object.values(indicatorsObj);

    // A helper type for each position’s map
    // - key: indicator_value
    // - value: { indicator: string, domains: Set<number> }
    type IndicatorMap = Map<string, { indicator: string; domains: Set<number> }>;

    // Prepare maps for each position we care about
    const positions: Record<
      'left' | 'right' | 'leftCenter' | 'rightCenter',
      IndicatorMap
    > = {
      left: new Map(),
      right: new Map(),
      leftCenter: new Map(),
      rightCenter: new Map(),
    };

    // Go through the array of indicators, grouping them by position
    indicatorsArray.forEach((indicatorItem) => {
      const { card_position, domain_id, indicator_value } = indicatorItem;

      // Only handle known positions (you can change or remove this check if needed)
      if (
        card_position !== 'left' &&
        card_position !== 'right' &&
        card_position !== 'leftCenter' &&
        card_position !== 'rightCenter'
      ) {
        return; // or handle unknown positions differently
      }

      const mapForPosition = positions[card_position];

      if (!mapForPosition.has(indicator_value)) {
        mapForPosition.set(indicator_value, {
          indicator: indicator_value,
          domains: new Set<number>(),
        });
      }

      // Merge the domain ID for this indicator_value
      mapForPosition.get(indicator_value)!.domains.add(domain_id);
    });

    // Convert a map to the desired array form: {index, domains[], indicator}
    function toGroupedArray(map: IndicatorMap): GroupedIndicator[] {
      let counter = 1;
      return Array.from(map.values()).map(({ indicator, domains }) => ({
        index: counter++,
        domains: Array.from(domains),
        indicator,
      }));
    }

    this.leftIndicators = toGroupedArray(positions.left);
    this.rightIndicators = toGroupedArray(positions.right);
    this.leftCenterIndicators = toGroupedArray(positions.leftCenter);
    this.rightCenterIndicators = toGroupedArray(positions.rightCenter);
  }


  togglePreviewMode(actionType: string) {
    this.isPreviewOpen = !this.isPreviewOpen;
    this.selectMapAction(actionType)
  }

  toggleCustomizeMode(actionType: string) {
    this.isCustomizeOpen = !this.isCustomizeOpen;
    this.commonService.setIsCustomizeOpen(this.isCustomizeOpen);
    this.selectMapAction(actionType)
  }

  hideCustomization() {
    this.isCustomizeOpen = !this.isCustomizeOpen;
    this.selectedMapButton = '';
  }


  selectMapAction(actionType: string) {
    this.selectedMapButton = actionType;
    // this.selectedMapButton = actionType;
    this.indicatorCmp.setCardAction(actionType);
    this.filtersBackupAfterCustomize = this.filterCmp.getSelectedFilters();
  }


  actionChange(event: { event: string; config: Record<string, any> }) {
    this.selectedMapButton = event.event;
    this.geoConfig = event.config;
    //this.selectedMapButton = event;
    this.isCustomizeOpen = !this.isCustomizeOpen;
    this.commonService.setIsCustomizeOpen(this.isCustomizeOpen);
  }


  filterChanged(filter: any) {
    this.indicatorCmp?.applyFilter(filter);

    // update the breadCrumb with current selected year and quarter
    this.breadCrumb = [
      { title_en: 'Home', title_ar: 'الرئيسية', route: '/' },
      { title_en: `${'Census Geomap - ' + filter.YEAR_CODE[0]}`, title_ar: `${'خريطة التعداد - ' + filter.YEAR_CODE[0]}`, route: '' }
    ];
  }

  filterReset(reset: boolean) {
    this.mapCmp.resetFilter(reset);
  }


  updateSelectionLevel(level: string) {
    this.mapCmp.updateSelectionLevel(level);
  }

  domainChanged(event: any) {
    this.indicatorCmp.changeDomain(event);
  }

  regionChange(event: any) {
    this.filterCmp.regionChanged(event);
  }
  regionChangedFromMap(event: any) {
    this.filterCmp.regionChangedFromMap(event);
  }

  districtsChange(event: any) {
    this.filterCmp.changeDistrict(event);
  }
  districtChangedFromMap(event: any) {
    this.filterCmp.districtChangedFromMap(event);
  }

  communityChange(event: any) {
    this.filterCmp.communityChanged(event);
  }
  communityChangedFromMap(event: any) {
    this.filterCmp.communityChangedFromMap(event);
  }


  regionChangeFromChart(event: any) {
    this.filterCmp.regionChangeFromChart(event);
  }

  resetFilterComponent(event: any) {
    this.filterCmp.onReset();
  }


  resetDomainFilter(event: any) {
    let domain_id = 1;
    setTimeout(() => {
      this.route.paramMap.subscribe(params => {domain_id = Number(params.get('domain_id'))});
      if(domain_id == 1){
        this.filterCmp.selectedDomains.selectedDomain = 
        this.defaultLang == 'en' ? 
        this.filterCmp.selectedDomains.domainsList[0].SELECT_EN : 
        this.filterCmp.selectedDomains.domainsList[0].SELECT_AR;
      } else if(domain_id == 2){
        this.filterCmp.selectedDomains.selectedDomain = 
        this.defaultLang == 'en' ? 
        this.filterCmp.selectedDomains.domainsList[1].SELECT_EN : 
        this.filterCmp.selectedDomains.domainsList[1].SELECT_AR;
      } else if(domain_id == 3){
        this.filterCmp.selectedDomains.selectedDomain = 
        this.defaultLang == 'en' ? 
        this.filterCmp.selectedDomains.domainsList[2].SELECT_EN : 
        this.filterCmp.selectedDomains.domainsList[2].SELECT_AR;
      }
    }, 200);

   
  }

}
