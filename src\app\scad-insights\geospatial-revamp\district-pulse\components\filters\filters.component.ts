import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ElementRef, HostListener, Output, EventEmitter, Input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonService } from '../../../common.service';
import { Subscription } from 'rxjs';
import { RegionsFilterComponent } from './regions-filter/regions-filter.component';
import { DistrictsFilterComponent } from './districts-filter/districts-filter.component';
import { CustomFilterComponent } from './custom-filter/custom-filter.component';
import { CommonModule } from '@angular/common';
import { CensusServiceService } from 'src/app/scad-insights/geospatial-revamp/census-service.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { geoMapKeys } from '../../../geospatial.contants';

@Component({
  selector: 'ifp-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RegionsFilterComponent,
    DistrictsFilterComponent,
    CustomFilterComponent,
  ],
  templateUrl: './filters.component.html',
  styleUrl: './filters.component.scss'
})
export class FiltersComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild('filtersContainer') filtersContainer!: ElementRef;

  public isMenuHidden: boolean = false; 
  public openMenu: string | null = null;
  public regionMenuId = geoMapKeys.regionMenuId;
  public districtMenuId = geoMapKeys.districtMenuId;
  public otherMenuId = geoMapKeys.otherMenuId;
  public isAnimating: boolean = false;
  private animationTimeout: any;

  constructor(
    private commonService: CommonService,
    private cdr: ChangeDetectorRef,
    private gisSharedService: CensusServiceService,
    private themeService: ThemeService,
  ) {
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
  }

  ngOnDestroy(): void {
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout);
    }
  }

  toggleMenu(menuId: string): void {
    this.openMenu = this.openMenu === menuId ? null : menuId;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event): void {
    const target = event.target as HTMLElement;

    if (!this.filtersContainer.nativeElement.contains(target)) {
      this.openMenu = null;
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (!this.isMenuHidden) {
      this.hideFilters();
    }
    this.openMenu = null;
  }

  onToggleFilters(): void {
    if (this.isAnimating) {
      return; 
    }

    if (this.isMenuHidden) {
      this.showFilters();
    } else {
      this.hideFilters();
    }
  }

  private showFilters(): void {
    this.isAnimating = true;
    this.isMenuHidden = false;
    
    this.openMenu = null;

    setTimeout(() => {
      const filtersMenu = this.filtersContainer.nativeElement.querySelector('.filters-menu');
      if (filtersMenu) {
        filtersMenu.classList.remove('hidden');
        filtersMenu.classList.add('visible');
        
        setTimeout(() => {
          filtersMenu.style.transform = 'translateX(-5px)';
          setTimeout(() => {
            filtersMenu.style.transform = 'translateX(0)';
          }, 100);
        }, 200);
      }
      
      this.animationTimeout = setTimeout(() => {
        this.isAnimating = false;
      }, 500);
    }, 10);
  }

  private hideFilters(): void {
    this.isAnimating = true;
    
    const filtersMenu = this.filtersContainer.nativeElement.querySelector('.filters-menu');
    if (filtersMenu) {
      filtersMenu.classList.remove('visible');
      filtersMenu.classList.add('hidden');
    }
    
    this.animationTimeout = setTimeout(() => {
      this.isMenuHidden = true;
      this.isAnimating = false;
      this.openMenu = null; 
    }, 500);
  }

  public get isFiltersVisible(): boolean {
    return !this.isMenuHidden;
  }

  public get arrowRotationClass(): string {
    return this.isFiltersVisible ? 'open-filters-icon--rotate' : '';
  }
}