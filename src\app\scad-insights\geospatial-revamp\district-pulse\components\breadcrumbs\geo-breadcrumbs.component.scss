@use '../../../../../../assets/ifp-styles/abstracts' as *;
.ifp-breadcrumb {
  position: relative;
  z-index: 4;
  background-color: #ffffff99;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  padding: $spacer-2 $spacer-3;
  width: 100%;  
  max-width: 850px;
  margin: $spacer-0 auto;
  border: 1px solid $ifp-color-grey-7;
  z-index: 999;
  &__ul {
    display: flex;
    align-items: center;
    justify-content: center;
    // padding-left: 74px;
    // margin-left: -74px;
  }

  &__li {
    color: $ifp-color-blue-hover;
    transition: color 0.3s;
    display: flex;
    align-items: center;
    &:hover {
      color: $ifp-color-link;
    }
    .ifp-icon {
      color: $ifp-color-grey-2;
      pointer-events: none;
      font-size: 1.2rem;
      margin: $spacer-0 $spacer-2;
      position: relative;
      top: 2px;
    }
    &:last-child {
      color: $ifp-color-grey-2;
      pointer-events: none;
      max-width: 100%;
      overflow: hidden;
      .ifp-icon {
        display: none;
      }
    }
    &--disable {
      color: $ifp-color-grey-2;
      pointer-events: none;
    }
  }
  &__link {
    color: inherit;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
  &__wrapper {
    position: relative;
    padding-inline-start: 20px;
  }
  &__back {
    position: absolute;
    top: 50%;
    left: $spacer-2;
    transform: translateY(-50%);
    transition: color 0.3s;
    .ifp-icon {
      transform: rotate(180deg);
      display: inline-block;
      margin-right: $spacer-2;
    }
    &:hover {
      color: $ifp-color-secondary-blue;
    }
  }
  &__page-nav {
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: 2px;
    cursor: pointer;
  }
  &--sticky {
    position: fixed;
    top: 94px;
    left: 50%;
    transform: translateX(-50%);
    animation: slide-down 0.3s forwards;
    box-shadow: 0px 3px 9px rgba(0, 0, 0, 0.24);
  }
}

:host-context([dir="rtl"]) {
  .ifp-breadcrumb {
    &__page-nav {
      left: auto;
      right: 0;
    }
  }
}

@include mobile {
  .ifp-breadcrumb {
    &__link {
      max-width: 80px;
    }
    &__li {
      .ifp-icon {
        margin: $spacer-0 $spacer-1;
      }
    }
  }
}

@keyframes slide-down {
  0% {
    top: 36px;
  }
  100% {
    top: 94px;
  }
}
