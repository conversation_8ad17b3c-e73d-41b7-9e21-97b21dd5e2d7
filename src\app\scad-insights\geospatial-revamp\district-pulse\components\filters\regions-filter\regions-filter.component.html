<div class="custom-multiselect" [ngClass]="{'open': isMenuOpen}">
  <!-- Dropdown trigger -->
  <div class="select-trigger" (click)="toggleMenu()">
    <span>Abu Dhabi Emirate</span>
    <span> <i class="arrow-down"></i> </span> 
  </div> 

  <!-- Dropdown menu -->
  <div class="options-container" *ngIf="isMenuOpen">
    <div class="options-list">
      <label class="region-item active">Abu Dhabi Emirate</label>
      <label class="region-item">Abu Dhabi Region</label>
      <label class="region-item">Al Ain Region</label>
      <label class="region-item">Al Dhafra Region</label>
    </div>
  </div>
</div>  