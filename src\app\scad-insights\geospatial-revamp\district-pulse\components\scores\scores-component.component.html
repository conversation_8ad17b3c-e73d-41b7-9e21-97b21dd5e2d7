<!-- scores-component.component.html -->
<div class="ifp-scores-container">
  <!-- Main Score Card -->
  <div class="score-card">
    <div class="score-header">
      <div class="left-icons">
        <em class="ifp-icon ifp-icon-gauge-meter"></em>
      </div>
      <div class="right-icons">
        <em matTooltip="Notification" class="ifp-icon ifp-icon-notification"></em>
      </div>
    </div>
    <div class="score-body">
      <div class="district-score">
        <div class="district-score-values">
          <span>Score</span>
          <span id="score-value">{{ mainScore.value }}%</span>
          <span id="score-label">{{ getScoreLevel(mainScore.value) }}</span>
        </div>
        <div class="district-score-chart">
          <ifp-score-gauge [score]="mainScore.value"></ifp-score-gauge>
        </div>
      </div>
      <div class="district-score-disclaimer">
        <span>This score was built based on the collected scores of all the following {{ indicators.length }} domains:</span>
      </div>
    </div>
  </div>

  <!-- Indicator Cards -->
  <div 
    class="indicator-card"
    *ngFor="let indicator of indicators; let last = last"
    [class.active-indicator]="indicator.isActive"
    [class.last-indicator]="last"
    (click)="onIndicatorCardClick(indicator)"
  >
    <div class="score-indicator">
      <div class="indicator-title">
        <span>{{ indicator.title }}</span>
      </div>
      <div class="indicator-chart">
        <ifp-gauge [score]="indicator.score"></ifp-gauge>
      </div>
      <div 
        class="indicator-expand"
        (click)="onIndicatorArrowClick(indicator, $event)"
        [attr.aria-label]="'Show ' + indicator.title + ' details'"
        role="button"
        tabindex="0"
        (keydown.enter)="onIndicatorArrowClick(indicator, $event)"
        (keydown.space)="onIndicatorArrowClick(indicator, $event)"
      >
        <em 
          class="ifp-icon ifp-icon-right-arrow"
          [class.arrow-active]="indicator.isActive && showIndicatorDetails"
        ></em>
      </div>
    </div>
  </div>

  <!-- Indicator Details Component -->
  <div
    class="indicator-details-wrapper"
    [ngClass]="detailsAnimationClass"
    [attr.aria-hidden]="!showIndicatorDetails"
  >
    <ifp-indicator-details
      *ngIf="showIndicatorDetails"
      [selectedIndicator]="selectedIndicator"
      [indicatorData]="getIndicatorById(selectedIndicator || '')"
      (closeDetails)="closeIndicatorDetails()"
    ></ifp-indicator-details>
  </div>
</div>