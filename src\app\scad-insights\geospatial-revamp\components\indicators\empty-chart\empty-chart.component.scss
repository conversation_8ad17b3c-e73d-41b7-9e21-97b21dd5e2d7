@use "../../../../../../assets/ifp-styles/abstracts/index" as *;

.geospatial-revamp-box {
  z-index: 2;
  padding: 15px;
  background: #ffffff99;
  transition: height 0.5s ease, background 0.5s ease;
  margin: 2.5px 0px;
}

.geospatial-revamp-box .indicator-card {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.indicator-card .indicator-header{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.indicator-header em{
  font-size: 1.6rem;
}

.indicator-header .right-icons em{
  margin: 0px 5px;
}

.indicator-card .indicator-title{
  margin: 5px 0px;
  font-weight: bold;
}


.right-icons .ifp-icon {
  cursor: pointer;
}

.left-icons img{
  width: 2.8rem;
}

.right-icons {
  position: relative;
}


.ifp-chart-icon {
  background-color: $ifp-color-white;
  box-shadow: 0 0 $spacer-3 $ifp-color-black-16;
  border-radius: 6px;
  display: flex;
  align-items: center;
  left: auto;
  right: 14px;
  position: absolute;
  padding: $spacer-2;
  z-index: 3;

  .ifp-icon {
    padding: $spacer-2;
    border: 1px solid $ifp-color-grey-7;
    margin: $spacer-2;
    cursor: pointer;
  }
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #998f8f;
}
