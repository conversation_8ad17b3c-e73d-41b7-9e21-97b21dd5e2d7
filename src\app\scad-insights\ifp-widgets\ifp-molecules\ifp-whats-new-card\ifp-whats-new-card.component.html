@if(unauthorized && addMyAppsLanding) {
<ifp-unauthorized-card [nodeId]="id" [contentType]="contentType" [isPending]="isAccessPending"></ifp-unauthorized-card>
} @else {
<ng-container *ngIf="!loader;else load">
  <div class="ifp-whats-new-card" #card *ngIf="!hybridCard; else analyticCard"
    [ngClass]="{'ifp-whats-new-card--download': isDownload || isCompare}">
    <ifp-card>
      <div class="ifp-whats-new-card__header">
        <div class="ifp-whats-new-card__icon-wrapper">
          <ng-container *ngFor="let domainIconsValue of domains">
            <ifp-domain-icon [domainName]="domainIconsValue"></ifp-domain-icon>
          </ng-container>
          <!-- <em class="ifp-whats-new-card__type ifp-icon" [class]="indicatorType.officialStatics === (data?.content_classification ? data?.content_classification : contentClassification) ? 'ifp-icon-verifyed-tick' : 'ifp-icon-conical-flask'"></em> -->
          <em
            *ngIf="data?.content_classification_key ? data?.content_classification_key=== classifications.officialStatistics : indicatorType.officialStatics === (data?.content_classification ? data?.content_classification : contentClassification)"
            class="ifp-whats-new-card__type ifp-icon ifp-icon-verifyed-tick"
            [appIfpTooltip]="indicatorType.officialStatics | translate"></em>
          <em
            *ngIf="data?.content_classification_key ? data?.content_classification_key=== classifications.innovativeStatistics :indicatorType.innovativeStatics === (data?.content_classification ? data?.content_classification : contentClassification)"
            class="ifp-whats-new-card__type ifp-icon ifp-icon-conical-flask"
            [appIfpTooltip]="indicatorType.innovativeStatics | translate"></em>
        </div>
        <div class="ifp-whats-new-card__buttons">
          <ifp-button *ngIf="!addMyAppsLanding"
            class="ifp-whats-new-card__btn ifp-myApps"
            [buttonColor]="addMyApps? ( myAppsStatus ?( removeStatus ? 'black' :'blue' ):  (addStatus ? 'blue' :'black' )):( myAppsStatus ? 'blue' :'black')"
            [event]="addMyApps? ( myAppsStatus ?( removeStatus ? false :true ):  (addStatus ? true :false )):( myAppsStatus ? true :false)"
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-plus-square'" (ifpClick)="addDataMyApps($event)"
            id="add-myapps"
            [analyticClass]="!myAppsStatus ? analyticsClasses.addToMyApps : analyticsClasses.removeMyApps"></ifp-button>
          <ifp-button  class="ifp-whats-new-card__btn ifp-notication-action"
            id="add-notification" [buttonColor]="notificationSelector ? 'blue' :'black'"
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-notification'"
            [event]="(notificationSelector) ? true : false" (ifpClick)="addNotification($event, card)"
            [analyticClass]="notificationSelector ? analyticsClasses.disableNotification : analyticsClasses.enableNotification"></ifp-button>
          <div class="ifp-whats-new-card__rect ifp-whats-new-card__rect--small {{analyticsClasses.collapsedCard}}"
            [ngClass]="{'ifp-whats-new-card__fill': this.small}" 
             (click)="resize(true)" id="card-small"></div>
          <div class="ifp-whats-new-card__rect ifp-whats-new-card__rect--large {{analyticsClasses.expandedCard}}"
            [ngClass]="{'ifp-whats-new-card__fill': !this.small}" 
             (click)="resize(false)" id="card-large"></div>
          <ifp-button class="ifp-whats-new-card__btn ifp-open-new" [buttonColor]="'black'"
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-link-curve'" [link]="false"
            (click)="openUrl('/statistics-insights/'+contentType+'/'+id)" id="open-new"></ifp-button>
        </div>
      </div>
      <div class="ifp-whats-new-card__body" [ngClass]="{'ifp-whats-new-card__body--expanded': !small}">
        <div class="ifp-whats-new-card__left">
          <div class="ifp-whats-new-card__value-range">
            <div class="ifp-whats-new-card__value" *ngIf="value || +value === 0 ">
              {{(visaData?.value < visaData?.thresholdValue ? '< ' : '' )+(visaData?.value < visaData?.thresholdValue ?
                visaData?.thresholdValue : value | shortNumber)}} </div>
                <span *ngIf="data?.unit" class="ifp-whats-new-card__unit">{{data?.unit}}</span>
                <div class="ifp-whats-new-card__range" *ngIf="range">
                  {{range | translate}}
                </div>
            </div>
            @if ((rating.value | shortNumber) != 0) {
            <div class="ifp-whats-new-card__rating" [ngStyle]="{'color': rating.color}">
              <em class="ifp-icon" [class]="rating.color === red ? 'ifp-icon-triangle' : 'ifp-icon-triangle-up'"></em>
              <span class="ifp-whats-new-card__rating-value">{{(rating.value | shortNumber)}}</span>
              ({{ (rating.percentage | number: this.format)}} {{'%'}})
            </div>
            } @else {
            @if (chartData?.[0]?.data?.length > 1) {
            <div class="ifp-whats-new-card__rating ifp-whats-new-card__rating--grey">
              <span class="ifp-whats-new-card__rating-value">{{(rating.value | shortNumber)}}</span>
              ({{ (rating.percentage | number: this.format)}} {{'%'}})
            </div>
            }

            }
            @if (chartData?.[0]?.data?.length > 1) {
            <div class="ifp-whats-new-card__dropdown">
              {{'Comparing to' | translate}} <app-ifp-dropdown (dropDownItemClicked)="dropDownItemClicked($event)"
                [isInline]="true" [dropDownItems]="comparisonList"></app-ifp-dropdown>
            </div>
            }

            <div class="ifp-whats-new-card__name" *ngIf="name" [appIfpTooltip]="name"
              [ngClass]="{'ifp-whats-new-card__name--census' : chartData?.[0]?.data?.length <= 1}"
              [disableTooltip]="name ? name.length < textLimit : true" [delay]="3000">
              {{name ? name.charAt(0).toUpperCase() + name.slice(1): '' | translate}}
            </div>
            @if (appliedFilters?.length > 0 ) {
            <p class="ifp-whats-new-card__filter" [appIfpTooltip]="appliedFilterTooltip">
              @for ( filter of appliedFilters; let i = $index; track i) {
              {{filter.key |titlecase}}: {{filter.value |titlecase}} @if (appliedFilters.length != i+1) {|}
              }
            </p>
            }
            @if (baseDate) {
            <div class="ifp-whats-new-card__txt-icon">
              <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="baseDate" [key]="'Updated date'"></ifp-icon-text>
            </div>
            }
          </div>
          <div class="ifp-whats-new-card__right" *ngIf="!small">
            <ng-container *ngIf="!loaderChart; else chartLoad">
              <div class="ifp-whats-new-card__name" *ngIf="name" @fadeInOut [appIfpTooltip]="name"
                [disableTooltip]="name ? name.length < textLimit : true" [delay]="3000">
                {{ name ? name.charAt(0).toUpperCase() + name.slice(1): '' | translate}}
              </div>
              <ifp-highcharts *ngIf="chart" [height]="100" #chartRef [data]="chartData" [chartName]="'lineChart'"
                [chartClass]="''" [format]="format" [comparison]="comparison"></ifp-highcharts>
              <div class="ifp-whats-new-card__compare-legend" *ngIf="chart && chartData?.[0]?.data?.length > 1">
                <img src="../../../../assets/images/trg.png">
                <span class="ifp-whats-new-card__compare-text">{{'Compared Indicator'| translate}}</span>
              </div>
            </ng-container>
            <ng-template #chartLoad>
              <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
            </ng-template>
          </div>
        </div>
        <!-- <div class="">
          <app-ifp-checkbox *ngIf="isCompare" [type]="'checkbox'" [enableFor]="true" [hideLabel]="true" [id]="index" (checkedEvent)="addOrRemoveCompare($event)" [ngClass]="{'ifp-whats-new-card__checbox-disabled': (enableCompare?.length >=4 && !checkboxChecked && !checkvalue() )|| data?.isMultiDimension }" [defualtChecked]="checkvalue()"></app-ifp-checkbox>
        </div> -->


        <div class="ifp-whats-new-card__remove">
          @if (security) {
          <ifp-tag class="ifp-whats-new-card__tag" [isBoxView]="true" [background]="'transparent'"
            [tagName]="security.name" [color]="security.color ?? ''"
            [infoHead]="('Data classification' | translate) + ': ' + security.name"
            [info]="security.description ?? ''"></ifp-tag>
          <!-- <ifp-tag class="ifp-whats-new-card__tag" [isBoxView]="true" [background]="'transparent'" [tagName]="security.name" [color]="security.color ?? ''" [info]="('Data classification' | translate) + ': ' + security.name"></ifp-tag> -->
          }
          <div class="ifp-whats-new-card__footer-icons">
            <div (click)="showWarningMessage()">
              <app-ifp-checkbox *ngIf="isCompare" [appIfpTooltip]="'Check to compare multiple indicators' | translate"
                [extraSpaceTop]="20" [type]="'checkbox'" [enableFor]="true" [hideLabel]="true" [id]="id"
                (checkedEvent)="addOrRemoveCompare($event)"
                [ngClass]="{'ifp-whats-new-card__checbox-disabled':secondLoader  ? secondLoader : ( (enableCompare?.length >= compareLimit && !checkboxChecked && !checkvalue() )|| data?.isMultiDimension ) }"
                [defualtChecked]="duplicateMeta?.isChecked"></app-ifp-checkbox>
            </div>
            @if (isHideShareCheck) {
            <app-ifp-checkbox [appIfpTooltip]="'Check to select indicator' | translate" [extraSpaceTop]="20"
              [type]="'checkbox'" [enableFor]="true" [hideLabel]="true" [id]="id" (checkedEvent)="selectNode($event)"
              [defualtChecked]="isSelected"></app-ifp-checkbox>
            }
            @if (remove) {
            <ifp-button class="ifp-whats-new-card__remove-btn" [buttonClass]="buttonClass.icon"
              [buttonColor]="buttonColor.black" [tooltipValue]="'Remove card'| translate"
              [iconClass]="'ifp-icon-round-cross'" (ifpClick)="removeEvent()"></ifp-button>
            }
            <!-- @if (downloadTypes && downloadTypes.length) { -->
            <app-ifp-dropdown class="ifp-whats-new-card__download" (dropDownItemClicked)="checkTermsStatus($event)"
              [isInline]="true" [dropDownItems]="downloadTypes" [isDownload]="isDownload"
              [key]="'label'"></app-ifp-dropdown>
            <!-- } -->
          </div>
        </div>
    </ifp-card>
  </div>

  <ng-template #analyticCard>
    <div #cardAnalytic class="ifp-analysis__outer">
      <ifp-card class="ifp-whats-new-card__analysis">
        <ifp-analysis-card-header [domainIconWhite]="false" [isLink]="false" [link]="undefined"
          [addMyAppsLanding]="addMyAppsLanding" [addMyApps]="addMyApps"
          (openInNewTab)="openUrl('/statistics-insights/'+contentType+'/'+id)" [notification]="notificationSelector"
          *ngIf="domains" [small]="small" [icons]="domains" (resized)="resize($event)" [isExpandShow]="true"
          (notificationEvent)="addNotification($event, cardAnalytic)" [id]="this.id" [title]="name"
          (myApps)="myappsEvent($event)" [contentType]="contentType"></ifp-analysis-card-header>
        <div class="ifp-analysis ifp-analysis--hybrid">
          <div class="ifp-analysis__left" [ngClass]="{'ifp-analysis__left--minimize': !this.small}">
            @if (name) {
            <h3 class="ifp-analysis__heading" [appIfpTooltip]="name"
              [disableTooltip]="name ? name.length < textLimit : true" [delay]="3000" [extraSpaceTop]="-10">{{name ?
              name.charAt(0).toUpperCase() + name.slice(1): '' | translate }}</h3>
            }
            <ng-container *ngIf="value">
              <div class="ifp-analysis__value ifp-analysis__value--baseline">
                <div class="ifp-analysis__currency-currency">{{(value) | shortNumber}}</div>
                @if (data?.unit) {
                <span class="ifp-analysis__unit">{{data?.unit}}</span>
                }
              </div>
            </ng-container>
            <p class="ifp-analysis__substitle">{{subTitle | quotRemove}}</p>
            <div class="ifp-analysis__txt-icons">
              <div class="ifp-analysis__txt-icon" *ngIf="publish">
                <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="publish" [key]="'Updated date'"></ifp-icon-text>
              </div>

              <div class="ifp-analysis__txt-icon" *ngIf="tagName &&  tagName !== ''">
                <ifp-button [buttonClass]="'ifp-btn--disabled ifp-btn--md ifp-btn--capitalize'"
                  [label]="tagName"></ifp-button>
              </div>
            </div>
            <div class="ifp-analysis__footer">
              @if (security) {
              <ifp-tag class="ifp-analysis__tag" [isBoxView]="true" [background]="'transparent'"
                [tagName]="security.name" [color]="security.color ?? ''"
                [infoHead]="('Data classification' | translate) + ': ' + security.name"
                [info]="security.description ?? ''"></ifp-tag>
              }
              <div class="ifp-analysis__footer-inner">
                @if (isHideShareCheck) {
                <app-ifp-checkbox [appIfpTooltip]="'Check to select indicator' | translate" [extraSpaceTop]="20"
                  [type]="'checkbox'" [enableFor]="true" [hideLabel]="true" [id]="id"
                  (checkedEvent)="selectNode($event)" [defualtChecked]="isSelected"></app-ifp-checkbox>
                }
                <div class="ifp-analysis__remove" *ngIf="remove">
                  <ifp-button [buttonClass]="buttonClass.icon" [buttonColor]="buttonColor.black"
                    [iconClass]="'ifp-icon-round-cross'" [tooltipValue]="'Remove'| translate"
                    (ifpClick)="removeEvent()"></ifp-button>
                </div>
              </div>
            </div>
          </div>
          <div class="ifp-analysis__right" *ngIf="!this.small">
            <ng-container *ngIf=" !loaderChart; else anChart">
              <div class="ifp-analysis__rate-card" *ngIf="rating">
                @if((rating.value | number:percentageFormat) != '0') {
                <div class="ifp-whats-new-card__rating" [ngStyle]="{'color': rating.color}">
                  <em *ngIf="rating.value" class="ifp-icon"
                    [class]="rating.color === red ? 'ifp-icon-triangle' : 'ifp-icon-triangle-up'"></em>
                  <span class="ifp-whats-new-card__rating-value">{{(rating.value | number:percentageFormat)}}
                    {{valueType === valueTypes.percentage ? '%' :''}}</span>
                  <ng-container *ngIf="rating.percentage">
                    ({{ (rating.percentage | number: percentageFormat)}}%)
                  </ng-container>
                </div>
                } @else {
                <div class="ifp-whats-new-card__rating ifp-whats-new-card__rating--grey">
                  <span class="ifp-whats-new-card__rating-value">{{(rating.value | number:percentageFormat)}}
                    {{valueType === valueTypes.percentage ? '%' :''}}</span>
                  <ng-container *ngIf="rating.percentage">
                    ({{ (rating.percentage | number: percentageFormat)}}%)
                  </ng-container>
                </div>
                }
                <div class="ifp-analysis__chart">
                  <ifp-highcharts *ngIf="chart" #chartRef [data]="chartData" [chartName]="'lineChart'" [chartClass]="''"
                    [format]="format" [height]="180"></ifp-highcharts>
                </div>
              </div>
              <!-- <div class="ifp-analysis__right" *ngIf="!this.small">
              <ng-container *ngIf=" !loaderChart; else anChart">
                <div class="ifp-analysis__rate-card" *ngIf="rating">
                  <div class="ifp-whats-new-card__rating" [ngStyle]="{'color': rating.color}">
                    <em *ngIf="rating.value" class="ifp-icon" [class]="rating.color === red ? 'ifp-icon-triangle' : 'ifp-icon-triangle-up'"></em>
                    <span class="ifp-whats-new-card__rating-value">{{(rating.value | number:percentageFormat)}} {{valueType
                      === valueTypes.percentage ? '%' :''}}</span>
                    <ng-container *ngIf="rating.percentage">

                      ({{ (rating.percentage | number: percentageFormat)}}%)
                    </ng-container>

                  </div>
                </div>
                <div class="ifp-analysis__selectors">
                </div> -->
            </ng-container>
            <ng-template #anChart>
              <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
            </ng-template>
          </div>

        </div>
      </ifp-card>
    </div>
  </ng-template>
</ng-container>

<ng-template #load>
  <app-ifp-card-loader class="ifp-loader" [type]="'small'"></app-ifp-card-loader>
</ng-template>


<ng-container *ngIf="isTerms">
  <app-ifp-modal #tncModal [modalClass]="'ifp-modal__template-certificate'">
    <app-ifp-tnc-modal (termsResponse)="termsResponse($event)" [isAccepted]="tncState"></app-ifp-tnc-modal>
  </app-ifp-modal>
</ng-container>


<div #alertBox class="ifp-alert-container">
  <app-ifp-alert-box [timeOut]="alertTimeOutInSec" (alertResponse)="setEmailNotifStatus($event)"
    (closeAlert)="closeModal()" [ngStyle]="{'display': isSubscNotifOpen ? 'block' : 'none'}"></app-ifp-alert-box>
</div>
}
