import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, OnInit } from '@angular/core';
import { IfpButtonComponent } from '../../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from '../../core/constants/button.constants';
import { Store } from '@ngrx/store';
import { loadCensusData } from '../../store/pages/page.action';
import { selectCensusResponse } from '../../store/pages/page.selector';
import { CommonApiService } from '../../core/services/common-api/commonApi.service';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-census',
    templateUrl: './ifp-census.component.html',
    styleUrl: './ifp-census.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        IfpButtonComponent,
        TranslateModule
    ]
})
export class IfpCensusComponent implements OnInit {

  // @HostListener('window:resize')
  // getBanner() {
  //   const mobileBanner = '../../../../assets/images/census-mobile.jpg';
  //   if (window.matchMedia('(max-width: 767px)').matches) {
  //     this.image = mobileBanner;
  //   } else {
  //     this.image = this.censusData?.image;
  //   }
  // }

  public image: string = '';
  public text: string = '';
  public buttonText: string = '';
  public url: string = '';
  public year: string = '';
  public logo: string = '';
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public censusData!: any;

  constructor(private store: Store, private _cdr: ChangeDetectorRef, private _commonService: CommonApiService) { }

  openCensus() {
    window.open(this.url, '_blank');
  }


  ngOnInit(): void {
    this.store.dispatch(loadCensusData());
    this.store.select(selectCensusResponse).subscribe(resp => {
      if (resp?.resp) {
        this.censusData = resp?.resp;
        this._commonService.censusData = this.censusData;
        this.logo = resp?.resp?.logo;
        this.text = resp.resp.title;
        this.buttonText = resp.resp.url.title;
        this.url = resp?.resp?.url.uri;
        this.year = resp?.resp.year;
        // this.getBanner();
        this._cdr.detectChanges();
      }
    });
  }

}
