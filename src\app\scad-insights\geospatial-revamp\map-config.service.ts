import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, forkJoin } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MapConfigService {

  _esriPortalUrl: string = 'https://portalstg.scad.gov.ae/portal';
  _esriTokenForAuthentication: string = 'pKpB5Ya08UfLRHu-PK2iA1vfKSg5eCSZohJz-G4-gsKyLdbPGtbyq_927sjWavSsjR7_8PQUEyW3xodwD7hm48-pzaOnQ1R8vIwGn98l2k4YawRNF2QCurUT8hB5GK9P-Hfv1_jVIFa1mW6XnrN-gg..';
  // -jLhSrW892keFDCSqPv22ZPEMxetojpG9EO0HBslbnjrzwJ3O0uyjlh9eIySSZUiur_k4UA_iLtjFWKHCOojZmRnI_JNfCd80Jgs1gMuBgxNPry3E02E91xip8uH81Wn5XLgU7JfiOgNck5fgti8VwQ6QEBzYVA-9p9xRERNJWA.

  private _esriTokenForAuthenticationForApi: BehaviorSubject<string> =
    new BehaviorSubject<string>('');
  public _esriTokenForAuthenticationForApi$: Observable<string> =
    this._esriTokenForAuthenticationForApi.asObservable();
  public setEsriTokenForAuthenticationForApi(value: string): void {
    this._esriTokenForAuthenticationForApi.next(value);
  }
 
  constructor(
  ) {
    // console.log(this._esriTokenForAuthentication);
   }

}
