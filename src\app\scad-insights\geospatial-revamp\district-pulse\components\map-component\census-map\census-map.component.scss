#viewDivCensus {
  // height: 100vh;
  // width: 100vw;
  // min-height: 120vh; 
  height: 100%;
  width: 100%;
  // max-height: calc(100vh);
  border-radius: inherit;
  position: relative;
} 

// @media screen and (max-width: 1300px) {
//   #viewDivCensus{
//     max-height: calc(100vh + 10px);
//   }
// }

.arrow-down {
  position: relative;
  top: -2px;
  left: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
} 



// dropdown menu 

.custom-multiselect {
  position: relative;
  width: 100%;
  font-family: Arial, sans-serif;
}

.select-trigger {
  position: relative; 
  border-radius: 4px;
  background-color: transparent;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
}


.arrow-down {
  position: relative;
  left: -5px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
}

.options-container {
  width: 156px;
  position: absolute;
  bottom: 25px;
  left: -10px;
  background: rgba(255, 255, 255, 0.6);
  color: '#000000e0';
  border-radius: 4px;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.options-list {
  padding: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 6px;
  cursor: pointer;
}

.region-name {
  margin-top: 5px;
  margin-left: 8px;
  color: #000;
}


