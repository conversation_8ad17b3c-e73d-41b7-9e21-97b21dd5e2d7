<svg xmlns="http://www.w3.org/2000/svg" width="36.988" height="22.058" viewBox="0 0 36.988 22.058">
  <g id="Group_9940" data-name="Group 9940" transform="translate(0.535 -6.853)">
    <g id="Group_9941" data-name="Group 9941" transform="translate(0.323 7.64)">
      <path id="Path_14816" data-name="Path 14816" d="M12.195,21.2l.17-3.8a2.979,2.979,0,0,1,1.993-2.711l.159-.05,2.456-.679a1.364,1.364,0,0,0,.291-.119,1.334,1.334,0,0,0,.424-.366l.684-1.43" transform="translate(7.048 -0.863)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14817" data-name="Path 14817" d="M18.371,12.142l.692,1.275a1.342,1.342,0,0,0,.715.485l2.453.679c.053.016.106.032.159.05a2.98,2.98,0,0,1,1.995,2.711l.17,3.8" transform="translate(10.792 -0.805)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14818" data-name="Path 14818" d="M14.348,18.62a11.6,11.6,0,0,0-.7-2.313" transform="translate(7.931 1.72)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <line id="Line_1895" data-name="Line 1895" x1="1.383" y1="5.762" transform="translate(24.695 14.578)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14819" data-name="Path 14819" d="M21.031,16.308a11.569,11.569,0,0,0-.7,2.313" transform="translate(11.982 1.721)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <line id="Line_1896" data-name="Line 1896" x1="1.781" y2="5.959" transform="translate(28.088 14.378)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14820" data-name="Path 14820" d="M15.589,13.033c0,.514,1.155.932,2.581.932s2.583-.418,2.583-.932" transform="translate(9.106 -0.265)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14821" data-name="Path 14821" d="M14.97,10.5c.692,1.8,2.043,3.023,3.595,3.023s2.9-1.222,3.595-3.023" transform="translate(8.73 -1.799)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14822" data-name="Path 14822" d="M15.4,11.353c-.4.12-.883-.284-1.071-.906s-.011-1.226.394-1.348" transform="translate(8.298 -2.65)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14823" data-name="Path 14823" d="M19.446,11.353c.4.12.883-.284,1.071-.906s.011-1.226-.394-1.348" transform="translate(11.444 -2.65)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14824" data-name="Path 14824" d="M14.969,8.063a3.734,3.734,0,0,1,1.1-1.076,8.537,8.537,0,0,0,5.7.556l.018,0a2.976,2.976,0,0,0,.186,1.685" transform="translate(8.73 -3.93)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14825" data-name="Path 14825" d="M14.326,12.188a5.9,5.9,0,0,1-.13-2.927c.043-.2.785-3.375,3.511-4.054a4.4,4.4,0,0,1,4.163,1.171,3.919,3.919,0,0,1,.607.856,1.079,1.079,0,0,1,.114.072,3.129,3.129,0,0,1,1.3,2.763c-.035.573-.135,1.467-.135,1.821" transform="translate(8.184 -5.084)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14826" data-name="Path 14826" d="M.215,21.358l.63-3.975A3.17,3.17,0,0,1,2.66,15l.368-.165,2.716-1.238a1.334,1.334,0,0,0,.782-1.214V11.79" transform="translate(-0.215 -1.018)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14827" data-name="Path 14827" d="M6.631,11.789v.588a1.339,1.339,0,0,0,.781,1.216L10.5,15a3.167,3.167,0,0,1,1.815,2.387l.63,3.974" transform="translate(3.675 -1.019)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14828" data-name="Path 14828" d="M3.132,13.152a4.055,4.055,0,0,0,3.672,1.8,4.08,4.08,0,0,0,3.653-1.768" transform="translate(1.553 -0.193)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14829" data-name="Path 14829" d="M1.1,14.249,2.116,17.1v2.768" transform="translate(0.323 0.472)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14830" data-name="Path 14830" d="M9.008,19.868V17.1l1.014-2.851" transform="translate(5.116 0.472)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14831" data-name="Path 14831" d="M10.288,9.736a6.382,6.382,0,0,1-3.4-2.576L6.6,6.723l-.08.133A6.473,6.473,0,0,1,3.132,9.7" transform="translate(1.553 -4.09)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14832" data-name="Path 14832" d="M3.317,9.31v.928a3.989,3.989,0,0,0,1.165,2.817,2.567,2.567,0,0,0,1.812.752H6.6A3.265,3.265,0,0,0,9.48,12.135a4,4,0,0,0,.477-1.894V9.31" transform="translate(1.666 -2.522)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14833" data-name="Path 14833" d="M8.68,8.734v3.357c0,.059,0,.116,0,.173a4.491,4.491,0,0,1-.331,1.55" transform="translate(4.714 -2.871)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14834" data-name="Path 14834" d="M2.2,10.89A6.154,6.154,0,0,1,3.208,7.484l0,0A4.941,4.941,0,0,1,7.3,5.177a4.966,4.966,0,0,1,4.189,2.451A6.163,6.163,0,0,1,12.4,10.89" transform="translate(0.99 -5.028)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <path id="Path_14835" data-name="Path 14835" d="M2.469,13.669a4.491,4.491,0,0,1-.243-1.06,5.024,5.024,0,0,1-.024-.517V8.733" transform="translate(0.99 -2.872)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
      <line id="Line_1897" data-name="Line 1897" y2="2.681" transform="translate(8.356 14.761)" fill="none" stroke="#808a9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
    </g>
  </g>
</svg>
