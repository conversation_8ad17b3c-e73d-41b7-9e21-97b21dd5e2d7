import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ifp-district-statistics',
  standalone: true,
  imports: [
    CommonModule,
  ],
  templateUrl: './statistics-component.component.html',
  styleUrl: './statistics-component.component.scss'
})
export class StatisticsComponent implements OnInit {
  @ViewChild('poiContainer', { static: false }) poiContainer!: ElementRef;

  // Statistics data
  public statisticsData = [
    {
      icon: '../../../assets/icons/geospatial-ravamp/school-icon.svg',
      label: 'Schools',
      value: '1'
    },
    {
      icon: '../../../assets/icons/geospatial-ravamp/hospital_1.svg',
      label: 'Nrseries',
      value: '2'
    },
    {
      icon: '../../../assets/icons/geospatial-ravamp/green-space-icon.svg',
      label: 'Green Space',
      value: '1'
    },
    {
      icon: '../../../assets/icons/geospatial-ravamp/air-quality-icon.svg',
      label: 'Air Quality Index',
      value: '87.45%'
    },
    {
      icon: '../../../assets/icons/geospatial-ravamp/building_icon.svg',
      label: 'Buildings',
      value: '128'
    },
    {
      icon: '../../../assets/icons/geospatial-ravamp/unit_icon.svg',
      label: 'Units',
      value: '128'
    }
  ];

  constructor() {}

  ngOnInit(): void {}

  scrollLeft(): void {
    if (this.poiContainer) {
      const container = this.poiContainer.nativeElement;
      const scrollAmount = 150;
      container.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }
  }

  scrollRight(): void {
    if (this.poiContainer) {
      const container = this.poiContainer.nativeElement;
      const scrollAmount = 150;
      container.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  }
}