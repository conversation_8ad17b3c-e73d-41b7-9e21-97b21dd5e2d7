{"name": "bayaan", "version": "3.1.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "dev": "node --max_old_space_size=8184 ./node_modules/@angular/cli/bin/ng build  --configuration dev  && gzipper compress ./dist", "staging": "ng build  --configuration staging  && gzipper compress ./dist", "prod": "ng build  --configuration production  && gzipper compress ./dist", "demo": "ng build  --configuration demo  && gzipper compress ./dist", "test": "ng test", "lint": "ng lint", "serve": "node --max_old_space_size=8184 ./node_modules/@angular/cli/bin/ng serve --o"}, "private": true, "dependencies": {"@angular-devkit/core": "^19.0.7", "@angular/animations": "^19.0.6", "@angular/cdk": "^19.0.5", "@angular/common": "^19.0.6", "@angular/compiler": "^19.0.6", "@angular/core": "^19.0.6", "@angular/forms": "^19.0.6", "@angular/material": "^19.0.5", "@angular/material-moment-adapter": "^19.0.5", "@angular/platform-browser": "^19.0.6", "@angular/platform-browser-dynamic": "^19.0.6", "@angular/router": "^19.0.6", "@angular/service-worker": "^19.0.6", "@arcgis/core": "^4.26.5", "@azure/msal-browser": "^4.0.1", "@ctrl/ngx-codemirror": "^7.0.0", "@esri/calcite-ui-icons": "^3.22.4", "@highcharts/map-collection": "^2.1.0", "@kolkov/angular-editor": "^3.0.0-beta.0", "@microsoft/fetch-event-source": "^2.0.1", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ngrx/effects": "^19.0.0", "@ngrx/entity": "^19.0.0", "@ngrx/operators": "^18.0.0", "@ngrx/router-store": "^19.0.0", "@ngrx/signals": "^19.0.0", "@ngrx/store": "^19.0.0", "@ngrx/store-devtools": "^19.0.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@popperjs/core": "^2.11.8", "@syncfusion/ej2-spreadsheet": "^25.1.42", "@types/lodash": "^4.14.195", "@types/quill": "^2.0.14", "angular-gridster2": "^19.0.0", "angular-markdown-editor": "^3.1.1", "bootstrap": "^5.3.0", "bootstrap-icons": "^1.10.5", "d3": "^7.8.4", "drawflow": "^0.0.59", "esri-loader": "^3.7.0", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "gzipper": "^7.2.0", "highcharts": "^11.4.3", "highcharts-angular": "4.0", "html2pdf.js": "^0.10.1", "jspdf": "^2.5.1", "katex": "^0.16.0", "lodash": "^4.17.21", "marked": "^15.0.6", "ngx-csv-parser": "^2.0.0", "ngx-device-detector": "^9.0.0", "ngx-infinite-scroll": "^19.0.0", "ngx-markdown": "^19.0.0", "ngx-toastr": "^19.0.0", "powerbi-client-angular": "^5.0.0", "rxjs": "~7.8.0", "subsink": "^1.0.2", "swiper": "^9.2.4", "tslib": "^2.3.0", "uuid": "^11.0.5", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.7", "@angular-eslint/builder": "19.0.2", "@angular-eslint/eslint-plugin": "19.0.2", "@angular-eslint/eslint-plugin-template": "19.0.2", "@angular-eslint/schematics": "^19.0.2", "@angular-eslint/template-parser": "19.0.2", "@angular/cli": "~19.0.7", "@angular/compiler-cli": "^19.0.6", "@angular/localize": "^19.0.6", "@eslint/js": "^9.18.0", "@ngrx/eslint-plugin": "^19.0.0", "@types/d3": "^7.4.0", "@types/drawflow": "^0.0.11", "@types/file-saver": "^2.0.5", "@types/gtag.js": "^0.0.18", "@types/jasmine": "~4.3.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "eslint": "^9.18.0", "globals": "^15.14.0", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.5.4", "typescript-eslint": "^8.21.0"}}