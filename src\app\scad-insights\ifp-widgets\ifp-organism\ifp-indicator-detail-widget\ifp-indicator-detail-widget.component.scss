@use '../../../../../assets/ifp-styles/abstracts' as *;

// .ifp-node {
//   display: flex;
//   border: 1px solid $ifp-color-grey-7;
//   border-radius: 10px;
//   padding: $spacer-5 $spacer-0;

//   &__buttons {
//     display: flex;
//   }

//   &__left-actions{
//     width: 100%;
//   }

//   &__card-left {
//     position: relative;
//     width: 100%;
//     background-color: $ifp-color-section-white;
//     padding: $spacer-4 $spacer-0 $spacer-6 $spacer-4;
//     display: flex;
//   }

//   &__card-right {
//       width: 25%;
//       min-width: 320px;
//       background-color: $ifp-color-grey-4;
//       border-left: 1px solid $ifp-color-grey-7;
//   }

//   &__group-one {
//       display: flex;
//       align-items: center;
//       margin-bottom: $spacer-4;
//   }

//   &__tiltle {
//       font-size: 3rem;
//       font-weight: $fw-bold;
//       &::first-letter {
//         text-transform: uppercase;
//       }
//   }

//   &__button-icon {
//       margin-left: $spacer-2;
//   }

//   &__rating {
//       // margin-top: $spacer-3 ;
//       // display: flex;
//       // justify-content: space-between;
//       // max-width: 50%;
//       margin-bottom: $spacer-4;
//   }

//   &__content {
//       margin-top: $spacer-5;
//       margin-bottom: $spacer-5;
//       font-size: 1.5rem;
//   }

//   &__credits {
//       margin-top: auto;
//       color: $ifp-color-grey-disabled;
//   }

//   &__download-icons {
//       display: flex;
//   }

//   &__filter-title {
//       margin: $spacer-2 $spacer-0;
//       font-size: $spacer-3;
//       font-weight: bold;
//   }
//   &__counter {
//       display: flex;
//       align-items: center;
//       justify-content: space-between;
//       margin-bottom: $spacer-4;
//   }
//   &__drive-outer {
//       padding: $spacer-3 $spacer-4;
//       margin: $spacer-0 (-$spacer-3);
//       &--active {
//           background-color: $ifp-color-white;
//       }
//       &--disabled {
//           opacity: 0.5;
//           pointer-events: none;
//       }
//   }
//   &__filters {
//     display: flex;
//     margin: $spacer-0 (-$spacer-2) $spacer-4;
//   }
//   &__filter-item {
//     margin: $spacer-0 $spacer-2;
//   }
//   &__month-selector {
//     margin-top: $spacer-3;
//     display: block;
//     margin-left: auto;
//   }
// }

// :host::ng-deep {
//   .ifp-node {
//     &__buttons .ifp-btn .ifp-icon {
//       font-size: 2rem;
//       position: relative;
//       top: 2px;
//     }
//     &__month-selector {
//       .ifp-month {
//         justify-content: center;
//       }
//     }
//     .ifp-dropdown {
//       background-color: $ifp-color-white;
//     }
//   }
// }

.ifp-node {

  &__value {
    margin: $spacer-0 $spacer-3;
    min-width: 100px;
  }

  &__vertical-line {
    border-left: 2px solid $ifp-color-green;
    height: 60px;
    border-style: dashed;
    margin-right: $spacer-4;

  }

  &__table {
    width: 100%;
    overflow: auto;
  }

  &__tiltle {
    .ifp-icon {
      margin-left: $spacer-2;
      font-size: $ifp-fs-6;
      color: $ifp-color-blue-hover;
    }
    .ifp-icon-conical-flask {
      position: relative;
      top: 1px;
      font-size: 2.1rem;
    }
  }

  // Download section styles with left/right layout matching reference image
  &__download-section {
    margin-top: $spacer-4;
    display: flex;
    align-items: stretch;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    min-height: 100px;
  }

  // Left side: Title and Terms
  &__download-left,
  &__download-right {
    flex: 1;
    padding: $spacer-3 $spacer-4;
  }

  &__download-left {
    flex: 1;
    min-width: 0;
    background-color: $ifp-color-grey-18;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    &::after {
      content: '';
      width: 25px;
      height: 25px;
      position: absolute;
      top: 50%;
      right: 0;
      background-color: $ifp-color-grey-18;
      transform: translate(50%, -50%) rotate(45deg);
      z-index: 1;
    }
  }

  &__download-title {
    color: $ifp-color-black;
    margin: 0 0 $spacer-2 0;
    font-size: 1.6rem;
    font-weight: $fw-bold;
  }

  &__download-terms {
    display: flex;
    align-items: center;
    gap: $spacer-2;

    &--disable {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  &__download-checkbox {
    flex-shrink: 0;
  }

  &__download-terms-text {
    color: $ifp-color-black;
    font-size: 1.4rem;
    line-height: 1.4;
    margin: 0;

    .ifp-link {
      color: $ifp-color-link;
      cursor: pointer;
      text-decoration: underline;
      transition: color 0.2s ease;

      &:hover {
        color: $ifp-color-blue-hover;
      }
    }
  }

  // Right side: Download buttons
  &__download-right {
    flex-shrink: 0;
    background-color: #F1F1F8;
    padding-inline-start: $spacer-7;
    position: relative;
    display: flex;
    align-items: center;
    min-width: 300px;
  }

  &__download-wrapper {
    display: flex;
    gap: $spacer-3;
    margin-bottom: $spacer-4;
    flex-wrap: wrap;

    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  &__download-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: $spacer-2;
    width: 70px;
    height: 70px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:nth-child(1) { background-color: #8B3E44; }
    &:nth-child(2) { background-color: #4F7E52; }
    &:nth-child(3) { background-color: #3A5B8A; }
    &:nth-child(4) { background-color: #008080; }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
  }

  &__download-icon {
    width: 28px;
    height: 28px;
  }

  &__download-label {
    color: $ifp-color-white;
    font-size: 1.2rem;
    font-weight: 500;
  }

  &__terms {
    display: flex;
    align-items: center;
    gap: $spacer-2;
  }

  &__tnc-checkbox {
    flex-shrink: 0;
  }

  &__tnc-text {
    font-size: 1.4rem;
    line-height: 1.5;

    .ifp-link {
      color: $ifp-color-link;
      text-decoration: underline;
      cursor: pointer;

      &:hover {
        color: $ifp-color-blue-hover;
      }
    }
  }
}


.ifp-chart-toolbar {
  &__card-btn {
    margin: $spacer-2;
  }
}


:host::ng-deep {
  .ifp-chart-toolbar {
    &__card-btn {
      .ifp-btn .ifp-icon {
        font-size: 2.6rem;
        padding: $spacer-2;
        border: 2px solid $ifp-color-secondary-blue;
        border-radius: $spacer-2 ;
        margin-bottom: $spacer-3;
      }

      &--active {
        .ifp-btn .ifp-icon {
          border: 2px solid $ifp-color-active-blue;
          background-color: $ifp-color-active-blue;
        }
      }
    }
    &__header-wrapper {
      @extend %header-wrapper;
    }
    &__right-title {
      @extend %right-title;
    }
  }
}


// animation style
.lds-spinner {
  color: official;
  display: inline-block;
  width: 80px;
  height: 80px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%
  );
z-index: 99;
}

.lds-spinner div {
  transform-origin: 40px 40px;
  animation: lds-spinner 1.2s linear infinite;
}

.lds-spinner div:after {
  content: " ";
  display: block;
  position: absolute;
  top: 3px;
  left: 37px;
  width: 6px;
  height: 18px;
  border-radius: 20%;
  background: $ifp-color-secondary-blue;
}

.lds-spinner div:nth-child(1) {
  transform: rotate(0deg);
  animation-delay: -1.1s;
}

.lds-spinner div:nth-child(2) {
  transform: rotate(30deg);
  animation-delay: -1s;
}

.lds-spinner div:nth-child(3) {
  transform: rotate(60deg);
  animation-delay: -0.9s;
}

.lds-spinner div:nth-child(4) {
  transform: rotate(90deg);
  animation-delay: -0.8s;
}

.lds-spinner div:nth-child(5) {
  transform: rotate(120deg);
  animation-delay: -0.7s;
}

.lds-spinner div:nth-child(6) {
  transform: rotate(150deg);
  animation-delay: -0.6s;
}

.lds-spinner div:nth-child(7) {
  transform: rotate(180deg);
  animation-delay: -0.5s;
}

.lds-spinner div:nth-child(8) {
  transform: rotate(210deg);
  animation-delay: -0.4s;
}

.lds-spinner div:nth-child(9) {
  transform: rotate(240deg);
  animation-delay: -0.3s;
}

.lds-spinner div:nth-child(10) {
  transform: rotate(270deg);
  animation-delay: -0.2s;
}

.lds-spinner div:nth-child(11) {
  transform: rotate(300deg);
  animation-delay: -0.1s;
}

.lds-spinner div:nth-child(12) {
  transform: rotate(330deg);
  animation-delay: 0s;
}

@keyframes lds-spinner {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

// Responsive styles for download section
@include mobile-tablet {
  .ifp-node {
    &__download-section {
      flex-direction: column;
      gap: 0;
      margin-top: $spacer-3;
      align-items: stretch;
      min-height: auto;
    }

    &__download-left {
      width: 100%;
      padding: $spacer-3;
    }

    &__download-title {
      font-size: 1.5rem;
      margin-bottom: $spacer-2;
    }

    &__download-right {
      width: 100%;
      padding: $spacer-3;
    }

    &__download-wrapper {
      justify-content: flex-start;
      gap: $spacer-3;
    }

    &__download-item {
      width: 45px;
      height: 45px;
    }

    &__download-label {
      font-size: 1.1rem;
    }

    &__download-icon {
      width: 18px;
      height: 18px;
      color: white;
      stroke: white;
      fill: none;
    }

    &__download-terms {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacer-2;
    }

    &__download-terms-text {
      font-size: 1.3rem;
    }
  }
}

// RTL styles for download section
[dir="rtl"] {
  .ifp-node {
    &__download-section {
      direction: rtl;
    }

    &__download-terms {
      direction: rtl;
    }

    @include mobile-tablet {
      &__download-section {
        direction: rtl;
      }

      &__download-terms {
        direction: rtl;
        align-items: flex-start;
      }
    }
  }
}
%header-wrapper {
  padding: $spacer-0 $spacer-3;
  border-bottom: 1px solid $ifp-color-grey-7;
  margin: $spacer-0 (-$spacer-3) $spacer-4;
}

%right-title {
  font-size: $ifp-fs-7;
  display: inline-block;
  padding-bottom: $spacer-2;
  border-bottom: 4px solid $ifp-color-blue-hover;
  margin-bottom: -2px;
}
// Related Products styles
.ifp-chart-toolbar {
  &__custom-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: transparent;
    }
  }

  &__tool-item--active &__custom-button {
    background-color: transparent;
  }

  &__link-icon {
    width: 22px;
    height: 22px;
    transition: filter 0.2s ease;
  }

  &__action-box {
    padding-top: $spacer-3;
  }

  &__header-wrapper {
    @extend %header-wrapper;
  }
  &__right-title {
    @extend %right-title;
  }

  &__view-all {
    color: #3267FF;
    text-decoration: none;
    font-size: 1.4rem;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  &__search-container {
    display: flex;
    align-items: center;
    background-color: #F8F9FA;
    border-radius: 8px;
    padding: $spacer-2 $spacer-3;
    border: 1px solid #E9ECEF;
    margin-bottom: $spacer-4;
  }

  &__search-input {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 1.4rem;
    color: $ifp-color-black;

    &::placeholder {
      color: #6C757D;
    }
  }

  &__search-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-left: $spacer-2;
    color: #6C757D;

    .ifp-icon {
      font-size: 1.6rem;
    }

    &:hover {
      color: #3267FF;
    }
  }

  &__products-list {
    max-height: 700px;
    padding-right: $spacer-2;
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
  }

  &__product-card {
    display: flex;
    align-items: center;
    padding: $spacer-3;
    margin-bottom: $spacer-2;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #E9ECEF;

    &:hover {
      background: #D8DBE5;
      transform: translateY(-1px);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__product-tick {
    color: #3267FF;
    font-size: $ifp-fs-4;
    margin-inline-end: $spacer-2;
    flex-shrink: 0;
  }

  &__product-text {
    flex: 1;
    color: $ifp-color-black;
    margin-inline-end: $spacer-3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  &__product-arrow {
    color: #6C757D;
    font-size: 1.2rem;
    flex-shrink: 0;
  }

  &__no-results {
    padding: $spacer-4;
    text-align: center;
    color: #6C757D;
    font-size: 1.4rem;
    font-style: italic;
    background: #F8F9FA;
    border-radius: 8px;
  }

  // Scrollbar styling for products list
  &__products-list {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
}

// Responsive styles for Related Products list
@include mobile-tablet {
  .ifp-chart-toolbar {
    &__products-list {
      max-height: 250px;
      padding-right: 0;
    }

    &__product-card {
      padding: $spacer-2;
      margin-bottom: $spacer-2;
    }

    &__product-text {
      font-size: 1.3rem;
    }

    &__product-tick {
      margin-right: $spacer-2;
    }

    &__product-arrow {
      margin-left: $spacer-2;
    }
  }
}
