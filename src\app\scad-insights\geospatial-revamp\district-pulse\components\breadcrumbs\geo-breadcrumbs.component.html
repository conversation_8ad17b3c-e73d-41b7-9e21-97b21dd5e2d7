<div class="ifp-breadcrumb" [ngClass]="{'ifp-breadcrumb--sticky': isSticky}">
  <div class="ifp-breadcrumb__wrapper">
    <div class="ifp-breadcrumb__page-nav" >
      <span class="ifp-header__page-back" [title]="'Back' | translate" (click)="location.back()"><em
          class="ifp-icon ifp-icon-leftarrow"></em></span>
    </div>
    <!-- <div class="ifp-breadcrumb__back"><em class="ifp-icon ifp-icon ifp-icon-rightarrow" (click)="navBack()"></em>{{'Back' | translate}}</div> -->
    <ul class="ifp-breadcrumb__ul">
      <ng-container *ngFor="let item of pageData">
        <li class="ifp-breadcrumb__li" [ngClass]="{'ifp-breadcrumb__li--disable': item.disable}">
          <a (click)="itemClick(item)" class="ifp-breadcrumb__link">
            {{ (item.title | quotRemove) | translate }}
          </a>
          <em class="ifp-icon ifp-icon-right-arrow"></em>
        </li>
      </ng-container>
    </ul> 
     
  </div>
</div>
