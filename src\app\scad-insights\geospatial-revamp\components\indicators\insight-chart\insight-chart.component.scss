@use "../../../../../../assets/ifp-styles/abstracts/index" as *;

.geospatial-revamp-box {
  z-index: 2;
  padding: 15px;
  background: #ffffff99;
  transition: height 0.5s ease, background 0.5s ease;
  margin: 2.5px 0px;
  position: relative;
  @include geo-blur;
  &__icon {
    font-size: $ifp-fs-4;
    margin: $spacer-0 $spacer-1;
    cursor: pointer;

    &--active {
      color: $ifp-color-blue-hover;
    }
  }

  // Enhanced Population 15+ Chart Styles
  .population-15-progress-card {

    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .population-15-content {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 12px;

      .population-15-value-display {
        .population-15-main-number {
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.2;
          color: #333;
        }

        .population-15-category-label {
          font-size: 1.3rem;
          color: #666;
          margin-top: 2px;
          font-weight: 400;
        }
      }
    }

    .population-15-progress-bar-wrapper {
      background: #E0E0E0;
      border-radius: 10px;
      height: 20px;
      overflow: visible;
      cursor: pointer;
      position: relative;

      .population-15-progress-bar-fill {
        background: linear-gradient(90deg, #FF6B35 0%, #FF8250 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 20px;
        position: relative;
      }

      .population-15-percentage-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 600;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
        z-index: 1;
      }
    }
  }

  // Enhanced Job Seekers Chart Styles
  .job-seekers-progress-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .job-seekers-content {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 12px;

      .job-seekers-value-display {
        .job-seekers-main-number {
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.2;
          color: #333;
        }

        .job-seekers-category-label {
          font-size: 1.3rem;
          color: #666;
          margin-top: 2px;
          font-weight: 400;
        }
      }
    }

    .job-seekers-progress-bar-wrapper {
      background: #E0E0E0;
      border-radius: 10px;
      height: 20px;
      overflow: visible;
      cursor: pointer;
      position: relative;

      .job-seekers-progress-bar-fill {
        background: linear-gradient(90deg, #DC6F6F 0%, #E5848A 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 20px;
        position: relative;
      }

      .job-seekers-percentage-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 600;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
        z-index: 1;
      }
    }
  }

  // Enhanced Buildings Chart Styles
  .buildings-progress-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .buildings-content {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 12px;

      .buildings-value-display {
        .buildings-main-number {
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.2;
          color: #333;
        }

        .buildings-category-label {
          font-size: 1.3rem;
          color: #666;
          margin-top: 2px;
          font-weight: 400;
        }
      }
    }

    .buildings-progress-bar-wrapper {
      background: #E0E0E0;
      border-radius: 10px;
      height: 20px;
      overflow: visible;
      cursor: pointer;
      position: relative;

      .buildings-progress-bar-fill {
        background: linear-gradient(90deg, #8B5A3C 0%, #A16B4A 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 20px;
        position: relative;
      }

      .buildings-percentage-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 600;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
        z-index: 1;
      }
    }
  }

  // Enhanced Units Chart Styles
  .units-progress-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .units-content {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 12px;

      .units-value-display {
        .units-main-number {
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.2;
          color: #333;
        }

        .units-category-label {
          font-size: 1.3rem;
          color: #666;
          margin-top: 2px;
          font-weight: 400;
        }
      }
    }

    .units-progress-bar-wrapper {
      background: #E0E0E0;
      border-radius: 10px;
      height: 20px;
      overflow: visible;
      cursor: pointer;
      position: relative;

      .units-progress-bar-fill {
        background: linear-gradient(90deg, #6B46C1 0%, #8B5CF6 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 20px;
        position: relative;
      }

      .units-percentage-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 600;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
        z-index: 1;
      }
    }
  }

  // Enhanced Citizenship Chart Styles
  .citizenship-progress-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .citizenship-content {

      .citizenship-legend {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16px;

        .citizenship-item {
          flex: 1;
          display: flex;
          flex-direction: column;

          .citizenship-header {
            margin: 0px 1px 8px 1px;
            height: 40px; // Fixed height to ensure alignment
            display: flex;
            align-items: center;
            cursor: pointer;

            .citizenship-indicator {
              display: flex;
              align-items: center;
              gap: 8px;

              .citizenship-dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                cursor: pointer;
                transition: all 0.2s ease;
                background-color: lightgray;
                border: 2px solid #FFF;
                box-shadow: 0 0 0 1px darkgray;

                &.emirati-dot,
                &.non-emirati-dot {
                  text-decoration: none;
                  border: 2px solid #FFF;
                  & + .citizenship-label {
                    text-decoration: none;
                  }
                }

                &.emirati-dot {
                  background-color: #DC6F6F;
                  box-shadow: 0 0 0 1px #DC6F6F;
                }

                &.non-emirati-dot {
                  background-color: #5329E3;
                  box-shadow: 0 0 0 1px #5329E3;
                }

                &:hover {
                  transform: scale(1.1);
                }
              }

              .citizenship-label {
                font-size: 1.3rem;
                color: #666;
                font-weight: 400;
                text-decoration: line-through;

              }
            }
          }

          .citizenship-values {
            display: flex;
            align-items: baseline;
            gap: 8px;
            height: 32px; // Fixed height to ensure consistent alignment

            .citizenship-number {
              font-size: 1.2rem;
              font-weight: 700;
              color: #333;
              line-height: 1.2;
            }

            .citizenship-percentage {
              font-size: 0.9rem;
              color: #666;
              font-weight: 600;
              line-height: 1.2;
            }
          }
        }
      }
    }

    .citizenship-progress-bar-wrapper {
      position: relative;
      height: 20px;
      border-radius: 10px;
      overflow: hidden;
      background: #E0E0E0;

      .citizenship-progress-track {
        display: flex;
        height: 100%;

        .citizenship-segment {
          height: 100%;
          transition: width 0.3s ease;

          &.emirati-segment {
            background: linear-gradient(90deg, #DC6F6F 0%, #E58A8A 100%);
            border-radius: 10px 0 0 10px;
          }

          &.non-emirati-segment {
            background: linear-gradient(90deg, #5329E3 0%, #6B3FE8 100%);
            border-radius: 0 10px 10px 0;
          }

          // When only one segment is present, make it fully rounded
          &:only-child {
            border-radius: 10px;
          }

          // RTL adjustments
          &.rtl-segment.emirati-segment {
            border-radius: 0 10px 10px 0;
          }

          &.rtl-segment.non-emirati-segment {
            border-radius: 10px 0 0 10px;
          }
        }
      }

      .citizenship-bar-labels {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        display: flex;
        pointer-events: none;

        .emirati-bar-label,
        .non-emirati-bar-label {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.8rem;
          font-weight: 600;
          color: white;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }
    }

    // Compact version
    &.compact {
      .citizenship-content {
        .citizenship-legend {
          .citizenship-item {
            .citizenship-header {
              margin-bottom: 4px;

              .citizenship-indicator {
                .citizenship-label {
                  font-size: 1rem;
                }
              }
            }

            .citizenship-values {
              .citizenship-number {
                font-size: 1rem;
              }

              .citizenship-percentage {
                font-size: 0.8rem;
              }
            }
          }
        }
      }

      .citizenship-progress-bar-wrapper {
        height: 16px;

        .citizenship-bar-labels {

          .emirati-bar-label,
          .non-emirati-bar-label {
            font-size: 0.7rem;
          }
        }
      }
    }

    // Vertical layout for mobile
    &.vertical {
      .citizenship-content {
        .citizenship-legend {
          flex-direction: column;
          gap: 12px;
        }
      }
    }
  }

  // Enhanced Growth Charts Styles
  .growth-comparison-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .growth-header {
      margin-bottom: 16px;

      .growth-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0;
        text-align: center;
      }
    }

    .growth-periods {
      display: flex;
      gap: 16px;

      .growth-period {
        flex: 1;

        .period-header {
          margin-bottom: 8px;
          text-align: center;

          .period-label {
            font-size: 1.3rem;
            color: #666;
            font-weight: 500;
            line-height: 1.3;
          }
        }

        .period-content {
          margin-bottom: 12px;
          text-align: center;

          .period-main-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
          }

          .growth-indicator {
            .growth-value {
              font-size: 1.3rem;
              font-weight: 600;

              &.growth-positive {
                color: #2A9F08;

                &::before {
                  content: "↗ ";
                  font-size: 3rem;
                }
              }

              &.growth-negative {
                color: #9f2b08;

                &::before {
                  content: "↘ ";
                  font-size: 3rem;
                }
              }
            }
          }
        }

        .period-bar {
          height: 12px;
          background: #E0E0E0;
          border-radius: 6px;
          overflow: hidden;

          .growth-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #4A90A4 0%, #5BA0B4 100%);
            border-radius: 6px;
            width: 80%;
            transition: width 0.3s ease;

            &.current-fill {
              background: linear-gradient(90deg, #2A9F08 0%, #34B509 100%);
              width: 100%;
            }
          }
        }

        &.current {
          .period-label {
            color: #2A9F08;
            font-weight: 600;
          }

          .period-main-value {
            color: #2A9F08;
          }
        }
      }
    }
  }

  // Enhanced Multi-Growth Charts Styles
  .multi-growth-comparison-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .growth-header {
      margin-bottom: 16px;

      .growth-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0;
        text-align: center;
      }
    }

    .multi-growth-periods {
      display: flex;
      gap: 20px;

      .multi-growth-period {
        flex: 1;

        .period-header {
          margin-bottom: 12px;
          text-align: center;

          .period-label {
            font-size: 1.3rem;
            color: #666;
            font-weight: 500;
            line-height: 1.3;
          }
        }

        .demographic-items {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          gap: 12px;

          .demographic-item {
            .demographic-header {
              margin-bottom: 4px;

              .demographic-label {
                font-size: 1.3rem;
                color: #666;
                font-weight: 500;
              }
            }

            .demographic-content {
              margin-bottom: 6px;

              .demographic-value {
                font-size: 1.3rem;
                font-weight: 600;

                &.growth-positive {
                  color: #2A9F08;

                  &::before {
                    content: "↗ ";
                    font-size: 3rem;
                  }
                }

                &.growth-negative {
                  color: #9f2b08;

                  &::before {
                    content: "↘ ";
                    font-size: 3rem;
                  }
                }
              }
            }

            .demographic-bar {
              height: 8px;
              background: #E0E0E0;
              border-radius: 4px;
              overflow: hidden;

              .demographic-bar-fill {
                height: 100%;
                background: linear-gradient(90deg, #6B46C1 0%, #8B5CF6 100%);
                border-radius: 4px;
                width: 75%;
                transition: width 0.3s ease;

                &.current-fill {
                  background: linear-gradient(90deg, #2A9F08 0%, #34B509 100%);
                  width: 100%;
                }
              }
            }
          }
        }

        &.current {
          .period-label {
            color: #2A9F08;
            font-weight: 600;
          }
        }
      }
    }
  }

  // Compact versions
  .growth-comparison-card.compact {
    .growth-header .growth-title {
      font-size: 1rem;
    }

    .growth-periods .growth-period {
      .period-content {
        .period-main-value {
          font-size: 1.1rem;
        }

        .growth-indicator .growth-value {
          font-size: 0.9rem;
        }
      }

      .period-bar {
        height: 10px;
      }
    }
  }

  .multi-growth-comparison-card.compact {
    .growth-header .growth-title {
      font-size: 1rem;
    }

    .multi-growth-periods .multi-growth-period {
      .demographic-items .demographic-item {
        .demographic-content .demographic-value {
          font-size: 0.9rem;
        }

        .demographic-bar {
          height: 6px;
        }
      }
    }
  }

  .growth-comparison-card {
    .growth-periods {
      .growth-period {
        .period-header .period-label {
          text-align: right;
        }

        .period-content {

          .period-main-value,
          .growth-indicator {
            text-align: center;
          }
        }
      }
    }
  }

  .multi-growth-comparison-card {
    .multi-growth-periods {
      .multi-growth-period {
        .period-header .period-label {
          text-align: right;
        }

        .demographic-items .demographic-item {
          .demographic-header .demographic-label {
            text-align: right;
          }

          .demographic-content .demographic-value {
            text-align: right;
          }
        }
      }
    }
  }

  // Enhanced Pie Chart Styles
  .pie-chart-container {
    border-radius: 8px;
    margin-bottom: 12px;

    .enhanced-pie-chart {
      width: 100% !important;
      display: block !important;
      margin: 0 !important;
      overflow: visible !important;

      // Ensure chart container has proper styling
      .highcharts-container {
        overflow: visible !important;
      }

      // Enhanced data labels
      .highcharts-data-labels {
        .highcharts-data-label {
          .pie-label {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 6px 10px;
            backdrop-filter: blur(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            .pie-label-name {
              font-size: 12px;
              font-weight: 600;
              color: #333;
              line-height: 1.3;
              margin-bottom: 2px;
            }

            .pie-label-percentage {
              font-size: 11px;
              font-weight: 700;
              color: #666;
              line-height: 1.2;
            }
          }
        }
      }

      // Enhanced legend styling
      .highcharts-legend {
        .highcharts-legend-item {
          .highcharts-legend-symbol {
            border-radius: 50% !important;
          }

          text {
            font-weight: 500 !important;
            fill: #333 !important;
          }

          &:hover text {
            fill: #666 !important;
          }
        }
      }

      // Enhanced pie slice styling
      .highcharts-series {
        .highcharts-point {
          stroke: #fff !important;
          stroke-width: 2 !important;
          transition: all 0.2s ease !important;

          &:hover {
            stroke: #666 !important;
            stroke-width: 3 !important;
            filter: brightness(1.05) !important;
          }

          &.highcharts-point-select {
            stroke: #333 !important;
            stroke-width: 3 !important;
            filter: brightness(1.1) !important;
          }
        }
      }
    }

    // Compact version for smaller spaces
    &.compact {
      padding: 12px;

      .enhanced-pie-chart {
        .highcharts-data-labels {
          .pie-label {
            padding: 4px 8px;

            .pie-label-name {
              font-size: 11px;
            }

            .pie-label-percentage {
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  // Enhanced tooltip styles
  .enhanced-pie-tooltip {
    .enhanced-tooltip-content {
      padding: 0;

      .tooltip-header {
        font-size: 10px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 6px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 6px;
      }

      .tooltip-body {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;

        .tooltip-value {
          font-size: 10px;
          font-weight: 700;
          color: #fff;
        }

        .tooltip-percentage {
          font-size: 12px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.8);
          background: rgba(255, 255, 255, 0.1);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }
  }

  // RTL Support for pie charts
  [dir="rtl"] .pie-chart-container {
    .enhanced-pie-chart {
      .highcharts-legend {
        direction: rtl;
      }

      .highcharts-data-labels {
        .pie-label {
          direction: rtl;
          text-align: right;
        }
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 480px) {
    .pie-chart-container {
      padding: 8px;

      .enhanced-pie-chart {
        .highcharts-data-labels {
          .pie-label {
            padding: 3px 6px;

            .pie-label-name {
              font-size: 10px;
            }

            .pie-label-percentage {
              font-size: 9px;
            }
          }
        }
      }
    }
  }

  // Enhanced Bar Chart Styles
  .bar-chart-container {
    border-radius: 8px;
    margin-bottom: 12px;

    .enhanced-bar-chart {
      width: 100% !important;
      display: block !important;
      margin: 0 !important;
      overflow: visible !important;

      // Enhanced bar styling
      .highcharts-series {
        .highcharts-point {
          transition: all 0.2s ease !important;

          &:hover {
            filter: brightness(1.1) !important;
            transform: translateY(-1px) !important;
          }

          &.highcharts-point-select {
            filter: brightness(1.15) !important;
            stroke: #333 !important;
            stroke-width: 2 !important;
          }
        }

        // Column chart specific styling
        &.highcharts-column-series {
          .highcharts-point {
            &:hover {
              transform: translateY(-2px) !important;
            }
          }
        }

        // Bar chart specific styling
        &.highcharts-bar-series {
          .highcharts-point {
            &:hover {
              transform: translateX(2px) !important;
            }
          }
        }
      }

      // Enhanced data labels
      .highcharts-data-labels {
        .highcharts-data-label {
          .highcharts-data-label-box {
            fill: rgba(255, 255, 255, 0.95) !important;
            stroke: #e0e0e0 !important;
            stroke-width: 1 !important;
            rx: 4 !important;
            ry: 4 !important;
            transition: all 0.2s ease !important;
          }

          text {
            fill: #333 !important;
            font-weight: 600 !important;
            font-size: 11px !important;
            transition: all 0.2s ease !important;
          }

          // Hover state for data labels
          &:hover {
            .highcharts-data-label-box {
              fill: rgba(51, 51, 51, 0.95) !important;
              stroke: #333 !important;
            }

            text {
              fill: #fff !important;
            }
          }
        }
      }

      // Enhanced axis styling
      .highcharts-xaxis {
        .highcharts-axis-line {
          stroke: #e0e0e0 !important;
          stroke-width: 1 !important;
        }

        .highcharts-tick {
          stroke: #e0e0e0 !important;
          stroke-width: 1 !important;
        }

        .highcharts-axis-labels {
          text {
            fill: #666 !important;
            font-weight: 500 !important;
          }
        }
      }

      .highcharts-yaxis {
        .highcharts-axis-line {
          stroke: #e0e0e0 !important;
          stroke-width: 1 !important;
        }

        .highcharts-tick {
          stroke: #e0e0e0 !important;
          stroke-width: 1 !important;
        }

        .highcharts-grid-line {
          stroke: #f5f5f5 !important;
          stroke-width: 1 !important;
        }

        .highcharts-axis-labels {
          text {
            fill: #666 !important;
            font-weight: 500 !important;
          }
        }
      }

      // Enhanced legend styling
      .highcharts-legend {
        .highcharts-legend-item {
          text {
            fill: #333 !important;
            font-weight: 500 !important;
          }

          &:hover text {
            fill: #666 !important;
          }
        }
      }
    }

    // Compact version for smaller spaces
    &.compact {
      padding: 12px;

      .enhanced-bar-chart {
        .highcharts-data-labels {
          .highcharts-data-label {
            text {
              font-size: 10px !important;
            }
          }
        }
      }
    }
  }

  // Enhanced tooltip styles for bar charts
  .enhanced-bar-tooltip {
    .enhanced-tooltip-content {
      padding: 0;

      .tooltip-header {
        font-size: 13px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 6px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 6px;
      }

      .tooltip-body {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .tooltip-series {
          font-size: 11px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
        }

        .tooltip-value {
          font-size: 14px;
          font-weight: 700;
          color: #fff;
        }
      }
    }
  }

  // RTL Support for bar charts
  [dir="rtl"] .bar-chart-container {
    .enhanced-bar-chart {

      .highcharts-xaxis .highcharts-axis-labels,
      .highcharts-yaxis .highcharts-axis-labels {
        direction: rtl;
        text-anchor: end;
      }

      .highcharts-legend {
        direction: rtl;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 600px) {
    .bar-chart-container {
      padding: 8px;

      .enhanced-bar-chart {
        .highcharts-data-labels {
          display: none !important;
        }

        .highcharts-xaxis .highcharts-axis-labels text {
          font-size: 10px !important;
        }

        .highcharts-yaxis .highcharts-axis-labels text {
          font-size: 10px !important;
        }
      }
    }
  }
}

// .geospatial-revamp-box:hover {
//   height: 244px;
//   background: linear-gradient(129deg, #A6DFE9, #43B6CA);
// }

.geospatial-revamp-box .indicator-card {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.indicator-card .indicator-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.indicator-header em {
  font-size: 1.6rem;
}

.indicator-header .right-icons em {
  margin: 0px 5px;
}

.indicator-card .indicator-title {
  margin: 5px 0px;
  font-weight: bold;

  .ifp-icon {
    padding: 2px 5px 0px 5px;
    font-size: 12px;
    font-weight: bold;
  }
}

// Enhanced progress bar styles
.indicator-chart {
  margin-top: 12px;

  .progress-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .progress-content {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 12px;

      .value-display {
        .main-number {
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.2;
          color: #333;
        }

        .category-label {
          font-size: 1.3rem;
          color: #666;
          margin-top: 2px;
          font-weight: 400;
        }
      }
    }

    .progress-bar-wrapper {
      background: #E0E0E0;
      border-radius: 10px;
      height: 20px;
      overflow: visible;
      //cursor: pointer;
      position: relative;

      .progress-bar-fill {
        background: #4A90A4;
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 20px;
        position: relative;
      }

      .percentage-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 600;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
        z-index: 1;
      }
    }
  }

  // Enhanced Total Chart Styles
  .total-progress-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .total-content {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 12px;

      .total-value-display {
        .total-main-number {
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.2;
          color: #333;
        }

        .total-category-label {
          font-size: 1.3rem;
          color: #666;
          margin-top: 2px;
          font-weight: 400;
        }
      }
    }

    .total-progress-bar-wrapper {
      background: #E0E0E0;
      border-radius: 10px;
      height: 20px;
      overflow: visible;
      cursor: pointer;
      position: relative;

      .total-progress-bar-fill {
        background: linear-gradient(90deg, #2A9F08 0%, #34B509 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 20px;
        position: relative;
      }

      .total-percentage-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 600;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
        z-index: 1;
      }
    }

    // Variant for negative growth/decline
    &.decline {
      .total-progress-bar-fill {
        background: linear-gradient(90deg, #9f2b08 0%, #B53309 100%);
      }
    }
  }

  // Alternative layout for horizontal display
  .total-progress-card.horizontal {
    .total-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .total-value-display {
        display: flex;
        align-items: baseline;
        gap: 12px;

        .total-main-number {
          font-size: 1.4rem;
        }

        .total-category-label {
          font-size: 1.1rem;
          margin-top: 0;
        }
      }
    }
  }

  // Compact version for smaller spaces
  .total-progress-card.compact {
    .total-content {
      margin-bottom: 8px;

      .total-value-display {
        .total-main-number {
          font-size: 1rem;
        }

        .total-category-label {
          font-size: 1rem;
        }
      }
    }

    .total-progress-bar-wrapper {
      height: 16px;

      .total-percentage-text {
        font-size: 0.8rem;
      }
    }
  }

  // Enhanced Non-Citizen Chart Styles
  .non-citizen-progress-card {
    border-radius: 8px;
    margin-bottom: 12px;
    color: #333;

    .non-citizen-content {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-bottom: 12px;

      .non-citizen-value-display {
        .non-citizen-main-number {
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.2;
          color: #333;
        }

        .non-citizen-category-label {
          font-size: 1.3rem;
          color: #666;
          margin-top: 2px;
          font-weight: 400;
        }
      }
    }

    .non-citizen-progress-bar-wrapper {
      background: #E0E0E0;
      border-radius: 10px;
      height: 20px;
      overflow: visible;
      cursor: pointer;
      position: relative;

      .non-citizen-progress-bar-fill {
        background: linear-gradient(90deg, #5329E3 0%, #6B3FE8 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 20px;
        position: relative;
      }

      .non-citizen-percentage-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 600;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        white-space: nowrap;
        z-index: 1;
      }
    }

    // Variant for high concentration
    &.high-concentration {
      .non-citizen-progress-bar-fill {
        background: linear-gradient(90deg, #4A22D1 0%, #5D35E0 100%);
      }
    }

    // Variant for low concentration
    &.low-concentration {
      .non-citizen-progress-bar-fill {
        background: linear-gradient(90deg, #7B5EF0 0%, #8A6BF2 100%);
      }
    }
  }

  // Alternative layout for horizontal display
  .non-citizen-progress-card.horizontal {
    .non-citizen-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .non-citizen-value-display {
        display: flex;
        align-items: baseline;
        gap: 12px;

        .non-citizen-main-number {
          font-size: 1.4rem;
        }

        .non-citizen-category-label {
          font-size: 1.1rem;
          margin-top: 0;
        }
      }
    }
  }

  // Compact version for smaller spaces
  .non-citizen-progress-card.compact {
    .non-citizen-content {
      margin-bottom: 8px;

      .non-citizen-value-display {
        .non-citizen-main-number {
          font-size: 1rem;
        }

        .non-citizen-category-label {
          font-size: 1rem;
        }
      }
    }

    .non-citizen-progress-bar-wrapper {
      height: 16px;

      .non-citizen-percentage-text {
        font-size: 0.8rem;
      }
    }
  }
}

// RTL support
[dir="rtl"] .indicator-chart {
  .progress-card {
    .progress-content {
      .value-display {
        text-align: right;
      }
    }

    .progress-bar-wrapper .progress-bar-fill {
      margin-left: auto;
    }
  }

  .total-progress-card {
    .total-content {
      .total-value-display {
        text-align: right;
      }
    }

    .total-progress-bar-wrapper .total-progress-bar-fill {
      margin-left: auto;
    }
  }

  .non-citizen-progress-card {
    .non-citizen-content {
      .non-citizen-value-display {
        text-align: right;
      }
    }

    .non-citizen-progress-bar-wrapper .non-citizen-progress-bar-fill {
      margin-left: auto;
    }
  }

  .population-15-progress-card {
    .population-15-content {
      .population-15-value-display {
        text-align: right;
      }
    }

    .population-15-progress-bar-wrapper .population-15-progress-bar-fill {
      margin-left: auto;
    }
  }

  .job-seekers-progress-card {
    .job-seekers-content {
      .job-seekers-value-display {
        text-align: right;
      }
    }

    .job-seekers-progress-bar-wrapper .job-seekers-progress-bar-fill {
      margin-left: auto;
    }
  }

  .buildings-progress-card {
    .buildings-content {
      .buildings-value-display {
        text-align: right;
      }
    }

    .buildings-progress-bar-wrapper .buildings-progress-bar-fill {
      margin-left: auto;
    }
  }

  .units-progress-card {
    .units-content {
      .units-value-display {
        text-align: right;
      }
    }

    .units-progress-bar-wrapper .units-progress-bar-fill {
      margin-left: auto;
    }
  }

  .citizenship-progress-card {
    .citizenship-content {
      .citizenship-legend {
        .citizenship-item {
          .citizenship-header {
            .citizenship-indicator {
              .citizenship-label {
                text-align: right;
              }
            }
          }

          .citizenship-values {
            justify-content: flex-end;
          }
        }
      }
    }

    .citizenship-progress-bar-wrapper {
      direction: rtl;

      .citizenship-progress-track {
        direction: ltr;
        flex-direction: row-reverse;
      }
    }
  }
}

.right-icons .ifp-icon {
  cursor: pointer;
}

.left-icons img {
  width: 2.8rem;
}

.right-icons {
  position: relative;
}

.ifp-chart-icon {
  background-color: $ifp-color-white;
  box-shadow: 0 0 $spacer-3 $ifp-color-black-16;
  border-radius: 6px;
  display: flex;
  align-items: center;
  left: auto;
  right: 14px;
  position: absolute;
  padding: $spacer-2;
  z-index: 3;

  .ifp-icon {
    padding: $spacer-2;
    border: 1px solid $ifp-color-grey-7;
    margin: $spacer-2;
    cursor: pointer;
  }
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #998f8f;
}

//************************************************************///
// Legacy total chart style (kept for backward compatibility)

.total-chart .progress-bar-values {
  display: flex;
  justify-content: space-between;
}

.total-chart .indicator-value,
.total-chart .indicator-number {
  font-size: 1.2rem;
}

.total-chart .indicator-number {
  margin-right: 5px;
  font-weight: bold;
}

.total-chart .indicator-percentage {
  font-size: 0.9rem;
}

.total-chart .progress-bar-charts .progress-bar-line.total {
  border-radius: 5px;
  height: 8px;
  background-color: #2A9F08;
}

.total-chart .progress-bar-charts .progress-bar-line.total.growthdown {
  background-color: #9f2b08;
}

//************************************************************///
// citizenship chart style

.citizenship-chart {
  display: flex;
  flex-direction: column;
}

.citizenship-chart .progress-bar-values {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.citizenship-chart .progress-bar-charts {
  display: flex;
  margin-top: 10px;
}

.citizenship-chart .no-flex {
  display: block;
}

.citizenship-chart .progress-bar-charts .progress-bar-line {
  height: 8px;
}

.citizenship-chart .indicator-value,
.citizenship-chart .indicator-number {
  font-size: 1.2rem;
}

.citizenship-chart .indicator-number {
  margin-right: 5px;
  font-weight: bold;
}

.citizenship-chart .indicator-percentage {
  font-size: 0.9rem;
}

.citizenship-chart .progress-bar-charts .progress-bar-line.emirati {
  border-radius: 5px 0px 0px 0px;
  background-color: #DC6F6F;
}

.citizenship-chart .progress-bar-charts .progress-bar-line.non-emirati {
  border-radius: 0px 5px 5px 0px;
  background-color: #5329E3;
}

.progress-bar-line-ar.emirati {
  border-radius: 0px 5px 5px 0px !important;
}

.progress-bar-line-ar.non-emirati {
  border-radius: 5px 0px 0px 0px !important;
}

.citizenship-chart .indicator-value {
  display: flex;
  align-items: baseline;
}

.citizenship-chart .custom-radio {
  width: 12px;
  height: 12px;
  margin-top: 5px;
  appearance: none;
  border: 1px solid #6a7578a6;
  background-color: #6a7578a6;
  border-radius: 50px;
  cursor: pointer;
}

.citizenship-chart .custom-radio.emirati {
  background-color: #DC6F6F;
  border-color: #FFF;
}

.citizenship-chart .custom-radio.nonEmirati {
  background-color: #5329E3;
  border-color: #FFF;
}

//************************************************************///
// growth chart style
.growth-chart {
  text-align: center;
}

.growth-chart .total-chart {
  display: flex;
  justify-content: space-between;
}

.growth-chart .multi-chart {
  display: flex;
  justify-content: space-between;
}

.growth-chart .indicator-value,
.growth-chart .indicator-number {
  font-size: 1.2rem;
  padding: 5px;
}

.growth-chart .indicator-number {
  margin-right: 5px;
  font-weight: bold;
}

.growth-chart .progress-bar-charts {
  flex: auto;
  padding-top: 15px;
}

.growth-chart .progress-bar-charts .progress-bar-line {
  height: 8px;
}

.growth-chart .progress-bar-charts .progress-bar-line.total {
  border-radius: 5px;
  height: 8px;
  background-color: #2A9F08;
}

.growth-chart .progress-bar-charts .progress-bar-line {
  background-color: #9f2b08;
}

.growth-chart .indicator-number.growthup {
  color: #2A9F08;
}

.growthup::before {
  content: "\2B9D";
}

.growth-chart .indicator-number.growthdown {
  color: #9f2b08;
}

.growthdown::before {
  content: "\2B9F";
}

.typeHeightlight {
  background-color: aliceblue !important;
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #998f8f;
}

.content-classification {
  display: inline-flex;
  align-items: center;
  padding: 3px $spacer-1;
  margin-top: 16px;
  border: 1px solid #9c0d0d;
  border-radius: 3px;
  background-color: #ffffff;
  font-size: $ifp-fs-1;
  font-weight: $fw-medium;
  color: #9c0d0d;
  text-transform: uppercase;

  // Add the info icon using a pseudo-element
  .ifp-icon {
    font-size: 1.3em;
    color: #9c0d0d;
    font-weight: normal;
    margin-inline-start: $spacer-1;
    position: relative;
    top: 1px;
  }

  // Hover effect
  // &:hover {
  //   background-color: #f9fafb;
  //   border-color: #9c0d0d;
  // }

  // Optional: Add transition for smooth hover effect
  // transition: all 0.2s ease-in-out;
}

:host::ng-deep {
  .ifp-link {
    color: $ifp-color-black;
  }
}

