import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule  } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { CommonService } from '../../../common.service';
import { geoMapKeys } from '../../../geospatial.contants';

interface Region {
  REGION_CODE: number;
  REGION_AR: string;
  REGION_EN: string;
  districts?: District[];
}

interface District {
  REGION_CODE: number;
  REGION_AR: string;
  REGION_EN: string;
  DISTRICT_CODE: string;
  DISTRICT_AR: string;
  DISTRICT_EN: string;
}

interface Community {
  REGION_CODE: number,
  DISTRICT_CODE: string;
  DISTRICT_AR: string;
  DISTRICT_EN: string;
  COMMUNITY_CODE: string;
  COMMUNITY_AR: string;
  COMMUNITY_EN: string;
}


@Component({
  selector: 'ifp-regions-filter',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule
  ],
  templateUrl: './regions-filter.component.html',
  styleUrl: './regions-filter.component.scss'
})
export class RegionsFilterComponent {

  @Output() selectionChange = new EventEmitter<Region[]>();

  @Input() menuId!: string;
  @Input() openMenu!: string | null;
  @Input() filtersData: any = [];
  @Input() mapTheme: string = 'light';
  @Input() filtersBackupAfterCustomize: any = [];
  @Output() toggleDropdownMenu = new EventEmitter<string>();
  @Output() regionChanged = new EventEmitter();

  regions: Region[] = [];
  selectedRegions: Region[] = [];
  selectedRegionsByDistrictsAndCommunities: Region[] = [];
  searchText = '';
  filteredRegions: Region[] = [];
  public regionsFilterLabel: any = geoMapKeys.regionsFilterLabel;
  public language: string = 'en';

  constructor(
    private commonService: CommonService,
    public themeService: ThemeService,
  ) {
  }

  ngOnInit() {
    this.processData();
    this.selectRegionsAfterCustomize();
    this.themeService.defaultLang$.subscribe((lang: string) => {
      this.language = lang;
    });
  }


  private processData() {
    // Create a map of regions with their districts and communities
    this.regions = this.filtersData?.regions.map((region: Region) => ({
      ...region,
      districts: this.filtersData?.districts
        .filter((district: District) => district.REGION_CODE === region.REGION_CODE)
        .map((district: District) => ({
          ...district,
          communities: this.filtersData?.communities
            .filter((community: Community) => community.DISTRICT_CODE === district.DISTRICT_CODE)
            .sort((a: Community, b: Community) => a.COMMUNITY_CODE.localeCompare(b.COMMUNITY_CODE)) // Sort communities if needed
        }))
        .sort((a: District, b: District) => a.DISTRICT_CODE.localeCompare(b.DISTRICT_CODE)) // Sort districts
    })).sort((a: Region, b: Region) => a.REGION_CODE - b.REGION_CODE); // Sort regions

    this.filteredRegions = [geoMapKeys.allRegions, ...this.regions];
    this.selectedRegions = [this.filteredRegions[0]];
  }


  get isMenuOpen(): boolean {
    return this.openMenu === this.menuId; // Check if this menu is currently open
  }

  toggleMenu(): void {
    this.toggleDropdownMenu.emit(this.menuId);
  }


  isSelected(region: Region): boolean {
    return (
      this.selectedRegions.some(r => r.REGION_CODE === region.REGION_CODE)
      ||
      this.selectedRegionsByDistrictsAndCommunities.some(r => r.REGION_CODE === region.REGION_CODE)
    );
  }

  isAllRegionsOptionSelected(region: Region): boolean {
    return (
      region.REGION_CODE == 0
      &&
      this.selectedRegions.length == 1
      &&
      this.selectedRegions[0].REGION_CODE == 0
    );
  }

  // re-select regions after customize
  selectRegionsAfterCustomize() {
    if(this.filtersBackupAfterCustomize.REGION){
      if(this.filtersBackupAfterCustomize.REGION.length > 0){
        this.selectedRegions = this.filtersBackupAfterCustomize.REGION;
      }
    }
  }

  toggleSelection(region: Region): void {
    const index = this.selectedRegions.findIndex(r => r.REGION_CODE === region.REGION_CODE);
    this.selectedRegions = this.selectedRegions.filter((region: Region) => region.REGION_CODE !== 0);

    if (index === -1) {
      if(region.REGION_CODE == 0){
        this.selectedRegions = [];
        this.selectedRegions.push(region);
      }else {
        this.selectedRegions.push(region);
      }
    }
    else {
      if(this.selectedRegions.length == 1){
        this.selectedRegions.splice(index, 1);
        this.selectedRegions = [this.filteredRegions[0]];
      }else {
        this.selectedRegions.splice(index, 1);
      }
    }

    // handel all selected
    const isOnlyAllRegionsSelected =
    this.selectedRegions.length === 1 && this.selectedRegions[0].REGION_CODE === 0;

    if(isOnlyAllRegionsSelected) {
      this.selectionChange.emit(this.regions);
      this.regionChanged.emit(this.regions);
      this.checkAllBox();
    } else {
      this.selectionChange.emit(this.selectedRegions);
      this.regionChanged.emit(this.selectedRegions);
    }
  }


  selectAll() {
    this.selectedRegions = [...this.filteredRegions];
    this.selectionChange.emit(this.selectedRegions);
  }

  selectFirstOption() {
     this.selectedRegions = [this.filteredRegions[0]];
  }

  checkAllBox() {
    this.selectedRegions = [geoMapKeys.allRegions];
  }

  clearAll() {
    this.selectedRegions = [...this.regions];
  }

  updateSelectedRegions(regions: any) {
    if(regions.length > 0){
      const idsToMatch = regions.map((obj: any) => obj.REGION_CODE).toString();
      this.selectedRegions = this.regions.filter((obj: any) => idsToMatch.includes(obj.REGION_CODE));
    }
  }

  updateSelectedRegionsFromBarChart(regions: any) {
    if(regions.length > 0){
      const idsToMatch = regions.map((obj: any) => obj).toString();
      this.selectedRegions = this.regions.filter((obj: any) => idsToMatch.includes(obj.REGION_CODE));
    }
  }
  
  updateSelectedRegionsFromMap(dataObj: any) {
    if(dataObj.length > 0){
      const idsToMatch = dataObj.map((obj: any) => obj.REGION_CODE).toString();
      this.selectedRegions = this.regions.filter((obj: any) => idsToMatch.includes(obj.REGION_CODE));
    }
  }

}
