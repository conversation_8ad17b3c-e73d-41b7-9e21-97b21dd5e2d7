import { Component, ViewChild, ChangeDetectorRef } from '@angular/core';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { GeoBreadcrumbsComponent } from '../components/breadcrumbs/geo-breadcrumbs.component';
import { MapComponentComponent } from '../components/map-component/map-component.component';
import { FiltersComponent } from '../components/filters/filters.component';
import { ScoresComponent } from '../components/scores/scores-component.component';
import { IfpRangeSliderComponent } from '../components/map-component/census-map/map-widgets/ifp-range-slider/ifp-range-slider.component'; 
import { NgClass } from '@angular/common';
import { Subscription } from 'rxjs';


@Component({
  selector: 'ifp-main',
  standalone: true,
  imports: [
    TranslateModule, 
    FiltersComponent,
    ScoresComponent,
    MapComponentComponent,
    GeoBreadcrumbsComponent,
    IfpRangeSliderComponent,
    RouterModule, 
    NgClass, 
  ],
  templateUrl: './main.component.html',
  styleUrl: './main.component.scss'
})
export class MainComponent {

  private subscription = new Subscription(); 
  public breadCrumb: PageData[] = [
    {
      title: 'Home',
      route: '/'
    },
    {
      title: 'ADDP',
      route: ''
    }
  ];

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
  }


  onRangeChange(event: any) {
    
  }
  
}


