@use "../../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-chart-insight {
  .ifp-input-info {
    margin-top: $spacer-1;
  }
  @include fullscreen {
    padding-bottom: $spacer-5;
  }
  &__card-inner {
    display: flex;
    align-items: center;
    width: 100%;
  }
  &__input {
    display: flex;
    align-items: center;
  }

  &__textarea,
  &__text-outer {
    display: block;
    width: 100%;
    padding: ($spacer-2 + 2px) $spacer-3;
    resize: none;
    border-radius: 30px;
    color: $ifp-color-secondary-grey;
  }

  &__textarea {
    border: none;
    background-color: $ifp-color-section-white;
  }

  &__text-outer {
    display: flex;
    align-items: center;
    margin-inline-end: $spacer-3;
    background-color: $ifp-color-grey-bg;
    border: 1px solid $ifp-color-grey-bg;
    position: relative;
    .ifp-icon {
      margin-inline-start: $spacer-2;
    }
  }

  &__textarea-outer {
    width: 100%;
    position: relative;
    padding-inline-end: 80px;
    border: 1px solid $ifp-color-grey-3;
    border-radius: 30px;
  }

  &__text {
    color: $ifp-color-secondary-grey;
    width: 100%;
    @include lineLimit(2);
  }

  &__add-icon {
    color: $ifp-color-white-global;
    display: flex;
    align-items: center;
    padding: $spacer-2;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    pointer-events: none;
    background-color: $ifp-color-grey-disabled;
    margin-inline-start: $spacer-3;
    &::before {
      position: relative;
      top: 1px;
    }
    &--active {
      background-color: $ifp-color-active-blue;
      pointer-events: all;
      cursor: pointer;
    }
  }

  &__btn-sec {
    margin: $spacer-4 (-$spacer-2) $spacer-0;
  }

  &__btn {
    margin: $spacer-0 $spacer-2;
  }

  &__btn-inline {
    display: inline-block;
    margin-top: $spacer-3;

    .ifp-icon {
      margin-inline-end: $spacer-2;
      position: relative;
      top: 1px;
    }
  }

  &__modal {
    background-color: $ifp-color-white;
    border-radius: 10px;
  }
  &__modal-body {
    display: flex;
  }
  &__modal-header,
  &__sec-2 {
    padding: $spacer-5;
  }
  &__sec-1 {
    width: 40%;
    border-inline-end: 1px solid $ifp-color-grey-7;
  }
  &__sec-2 {
    width: 60%;
  }
  &__modal-header {
    border-bottom: 1px solid $ifp-color-grey-7;
  }
  &__modal-head {
    font-size: $ifp-fs-7;
    font-weight: $fw-bold;
  }
  &__list {
    max-height: 400px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-4, 4px, 8px);
  }
  &__list-check {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    min-width: 20px;
    height: 20px;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 50%;
    margin-inline-start: $spacer-2;
  }
  &__list-tick {
    font-size: 1rem;
    color: $ifp-color-white-global;
    position: relative;
    top: 2px;
  }
  &__list-item {
    padding: $spacer-3 $spacer-5;
    border-top: 1px solid $ifp-color-grey-7;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    cursor: pointer;
    &:first-child {
      border-top: 0;
    }
    &--selected,
    &--active {
      .ifp-chart-insight__list-check {
        border: 1px solid $ifp-color-blue-hover;
        background-color: $ifp-color-blue-hover;
      }
    }
    &--active {
      background-color: $ifp-color-dropdown-select;
      font-weight: $fw-semi-bold;
    }
  }
  &__list-text {
    max-height: 38px;
    @include lineLimit(2);
  }
  &__modal-title,
  &__modal-desc {
    color: $ifp-color-secondary-grey;
  }
  &__modal-title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }
  &__modal-desc {
    margin-bottom: $spacer-3;
  }
  &__action-sec {
    display: flex;
    align-items: center;
    position: absolute;
    top: 50%;
    right: $spacer-3;
    transform: translateY(-50%);
    .ifp-chart-insight__btn-icon {
      margin-inline-start: $spacer-2;
    }
    .ifp-chart-insight__icon {
      position: relative;
      top: 2px;
    }
  }
  &__modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: $spacer-3 $spacer-5;
    border-top: 1px solid $ifp-color-grey-7;
  }
  &__card {
    margin: $spacer-3 $spacer-0;
    .ifp-input-info {
      padding-inline-start: $spacer-5;
    }
  }
  &__modal-comment {
    min-height: 260px;
    .ifp-chart-insight__btn-sec {
      margin-top: $spacer-3;
    }
  }
  &__user-info {
    margin-bottom: $spacer-1;
    margin-inline-start: $spacer-5 + $spacer-3;
  }
  &__user {
    font-size: $ifp-fs-2;
    color: $ifp-color-tertiary-text;
    display: inline-block;
    &:first-child {
      padding-inline-end: $spacer-2;
      border-inline-end: 1px solid $ifp-color-grey-7;
      margin-inline-end: $spacer-2;
    }
  }
  &__title {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    letter-spacing: 2px;
    margin-bottom: $spacer-2;
  }
  &__desc {
    margin-bottom: $spacer-3;
  }
  &__add {
    color: $ifp-color-active-blue;
    border: 1px solid $ifp-color-active-blue;
    text-align: center;
    padding: $spacer-2;
    border-radius: 50px;
    transition: 0.3s;
    cursor: pointer;
    .ifp-icon {
      font-size: $ifp-fs-5;
      margin-inline-end: $spacer-2;
      position: relative;
      top: 3px;
    }
    &:hover {
      color: $ifp-color-hover-blue;
      border: 1px solid $ifp-color-hover-blue;
    }
  }
  &__manage {
    padding: $spacer-3;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 20px;
    &--user {
      .ifp-chart-insight__text-outer {
        padding-inline-end: 80px;
      }
    }
  }
  &__manage-title {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-3;
  }
  &__checkbox {
    opacity: 0.5;
    pointer-events: none;
    &--show {
      opacity: 1;
      pointer-events: all;
    }
  }
  &__add-btn-sec {
    margin-top: $spacer-5;
  }
  &__add-btn {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    color: $ifp-color-active-blue;
    cursor: pointer;
    .ifp-icon {
      display: inline-block;
      position: relative;
      top: 2px;
      margin-inline-start: $spacer-2;
    }
  }
  &__btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
    transition: 0.3s;
    &.ifp-icon-cross {
      font-size: $ifp-fs-2;
    }
    &--primary {
      background-color: $ifp-color-active-blue;
      color: $ifp-color-white-global;
      &:hover {
        background-color: $ifp-color-hover-blue;
      }
    }
    &--secondary {
      border: 1px solid $ifp-color-active-blue;
      color: $ifp-color-active-blue;
      &:hover {
        color: $ifp-color-white-global;
        background-color: $ifp-color-active-blue;
      }
    }
    &--pending {
      color: $ifp-color-orange;
      background-color: $ifp-color-orange-light;
      cursor: default;
    }
    &--disabled {
      background-color: $ifp-color-grey-disabled;
      pointer-events: none;
    }
  }
  &__icon {
    font-size: $ifp-fs-4;
    color: $ifp-color-tertiary-text;
    cursor: pointer;
    transition: 0.3s;
    position: relative;
    top: 1px;
    &:hover {
      color: $ifp-color-active-blue;
    }
    &--delete {
      color: $ifp-color-active-blue;
      &:hover {
        color: $ifp-color-red;
      }
    }
  }
  &__manage-footer {
    padding-inline-start: $spacer-5;
  }
  &__approved-list {
    margin-bottom: $spacer-5;
  }
  &__approved-item {
    padding: ($spacer-2 + 4px) $spacer-0 ($spacer-2 + 4px) ($spacer-2 + 4px);
    border-bottom: 1px solid $ifp-color-grey-7;
    display: flex;
    align-items: center;
    .ifp-icon {
      margin-inline-start: $spacer-3;
    }
    &:last-child {
      border-bottom: none;
    }
  }
  &__modal-textarea {
    display: block;
    width: 100%;
    min-height: 150px;
    padding: $spacer-3;
    resize: none;
    border-radius: 5px;
    color: $ifp-color-secondary-grey;
    border: 1px solid $ifp-color-grey-7;
    background-color: $ifp-color-section-white;
  }
  &__bullet {
    width: 7px;
    height: 7px;
    min-width: 7px;
    border-radius: 50%;
    background-color: $ifp-color-secondary-grey;
    margin-inline-end: $spacer-3;
    align-self: flex-start;
    margin-top: $spacer-1 + 1px;
  }
}

:host-context([dir="rtl"]) {
  .ifp-chart-insight {
    &__action-sec {
      right: auto;
      left: $spacer-3;
    }
  }
}
