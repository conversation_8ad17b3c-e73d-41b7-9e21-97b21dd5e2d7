import { Component, input, linkedSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-indicator-disclaimer',
  imports: [TranslateModule],
  templateUrl: './ifp-indicator-disclaimer.component.html',
  styleUrl: './ifp-indicator-disclaimer.component.scss'
})
export class IfpIndicatorDisclaimerComponent {
  public data = input.required<DisclaimerData>();
  public text = linkedSignal(()=> {
    if(this.data().text) return this.setTextHtml();
    return ''
  });

  setTextHtml() {
    let text = this.data().text;

    // Replace {link} with an anchor tag
    if (this.data()?.link) {
      const linkHTML = `<a class="ifp-link" href="${this.data().link?.content}" target="_blank">${this.data().link?.linkText}</a>`;
      text = text.replace('{link}', linkHTML);
    }

    // Replace {email} with a mailto link
    if (this.data()?.email) {
      const emailHTML = `<a class="ifp-link" href="mailto:${this.data().email?.content}">${this.data().email?.content}</a>`;
      text = text.replace('{email}', emailHTML);
    }
    return text;
  }
}

export interface DisclaimerData {
  text: string;
  email: Email;
  link: Link;
}

interface Link {
  type: string;
  linkText: string;
  content: string;
}

interface Email {
  type: string;
  content: string;
}
