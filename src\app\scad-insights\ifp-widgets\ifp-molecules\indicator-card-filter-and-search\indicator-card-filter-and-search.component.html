<div class="ifp-search-filter__filter-wrapper" [ngClass]="{'ifp-search-filter--sort' : isSort}">
  <div class="ifp-search-filter__dropdown-wrapper"
    *ngIf="(types.indicator === type || types.glossery === type) && !isInnovative;else cate">
    <!-- <ifp-button class="ifp-search-filter__dropdown" [iconClass]="'ifp-icon-double-arrow'" [buttonClass]=" buttonClass.tertiaryWhite"></ifp-button> -->
    <app-ifp-dropdown  *ngIf="isSort" [singleDefaultSelect]="true" [selectedValue]="this.sortValue"
      [appIfpTooltip]="'Sort by'| translate" [extraSpaceTop]="0" [isMulti]="false" [key]="'label'"
      [iconClass]="'ifp-icon-double-arrow'" class="ifp-search-filter__tools-item"  (dropDownItemClicked)="sortClick($event)" [dropDownItems]="sort" [iconEnable]="true" [analyticClasses]="analyticClass"></app-ifp-dropdown>
    <app-ifp-dropdown *ngIf="enableTheme" [boarderBottom]="true" [selectedValue]="selectedDomainSingle"
      [checkBoxKey]="'domain'"  [singleDefaultSelect]='false' (dropDownItemMultiClicked)="onItemDomain($event)"
      [selectedValues]="selectedDomain" [key]="domainKey" [isMulti]="multi" class="ifp-search-filter__dropdown" [placeHolder]="themeName"
      [dropDownItems]="domainList" [selectAllBox]="selectAllBox" [selectAll]="selectAll" [analyticClasses]="analyticClass"></app-ifp-dropdown>
      <app-ifp-dropdown *ngIf="enableSubtheme" [boarderBottom]="true" [selectedValue]="themeListSingle"
      [singleDefaultSelect]='false' [checkBoxKey]="'subDomain'" [key]="'name'"
      (dropDownItemMultiClicked)="onItemTheme($event)" [selectedValues]="selectedTheme" [isMulti]="multi"
      class="ifp-search-filter__dropdown" [placeHolder]="subThemeName" [dropDownItems]="themeList"></app-ifp-dropdown>
    <ifp-button *ngIf="buttonsApply" class="ifp-search-filter__button" (ifpClick)="apply()" [label]="'Apply'"
      [buttonClass]=" applyDisabled ?  buttonClass.disabled: buttonClass.primary"></ifp-button>
    <ifp-button *ngIf="buttonsClear" class="ifp-search-filter__button" (ifpClick)="clear()" [label]="'Clear'"
      [buttonClass]=" clearDisabled ?  buttonClass.disabled: buttonClass.primary"></ifp-button>
  </div>
  <ng-template #cate>
    <app-ifp-dropdown *ngIf="enableCategory" [boarderBottom]="true" [selectedValue]="selectedCategory"
    (dropDownItemMultiClicked)="category($event)"
    class="ifp-search-filter__dropdown" [placeHolder]="'category'"
    [dropDownItems]="categoryList" ></app-ifp-dropdown>
  </ng-template>
</div>

<div class="ifp-search-filter__tools">
  <!-- Domain Details Dropdowns - Only show on domain-details page -->
   @if(isReportTab){
<div class="ifp-search-filter__domain-controls" *ngIf="isDomainDetailsPage">
    <app-ifp-dropdown
      class="ifp-search-filter__tools-item ifp-search-filter__domain-dropdown"
      [dropDownItems]="regularDropdownItems"
      [key]="'label'"
      [placeHolder]="'Select Option'"
      [selectedValue]="selectedRegularOption"
      (dropDownItemClicked)="onRegularDropdownChange($event)">
    </app-ifp-dropdown>

    <ifp-mat-date-range-picker
      class="ifp-search-filter__tools-item ifp-search-filter__date-picker"
      [startDate]="startDateControl"
      [endDate]="endDateControl"
      [placeholder]="'Date range'"
      [showIcon]="true"
      [iconClass]="'ifp-icon ifp-icon-calender'"
      (date)="onDateRangeChange($event)">
    </ifp-mat-date-range-picker>
  </div>
   }
  

  @if(searchDisable) {
    <ifp-search class="ifp-search-filter__tools-item" [isKeypress]="true" (searchEvent)="onSearch($event)" [onSearch]="search" [boxType]="boxTypeSearch" [placeholderText]="searchPlaceHolder"
    [ngClass]="{'ifp-search-filter__search' : !isSort}"></ifp-search>
  }
  <ifp-button *ngIf="isCompare" (ifpClick)="compare()" class="ifp-search-filter__button" [label]="'Compare'"
    [buttonClass]=" !enableCompare ?  buttonClass.disabled: buttonClass.primary"></ifp-button>
</div>
