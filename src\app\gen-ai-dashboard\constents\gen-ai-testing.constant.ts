export const genAiTestingApi ={
  templateDownload: 'evaluation/question/template',
  uploadFile: 'evaluation/question',
  evaluation: 'evaluation/',
  question: '/question',
  run: 'run',
  evaluationRun: 'evaluation/run/' ,
  rating: '/rating',
  accuracy: 'evaluation/analytics/accuracy',
  averageQuestion: 'evaluation/analytics/average-questions',
  accuracyComplexity: 'evaluation/analytics/accuracy-v-complexity',
  accuracyCategory: 'evaluation/analytics/accuracy-v-category',
  responseLatency: 'evaluation/analytics/response-time',
  analytics: 'evaluation/analytics/metric/',
  dropdownType: 'dropdowns/',
  history:'evaluation/run',
  preview:'evaluation/run/',
    weeklyAccuracy: 'evaluation/analytics/weekly-accuracy',
    like:'feedback/ai-message/'
}

export const  questionDropDown = {
  referenceQType : 'reference_q_type',
  sourceData : 'source_data',
  dataClassification: 'data_classification',
  referenceQComplexity : 'reference_q_complexity',
  aiModel: 'ai_model',
  evalAnsRelevance: 'evaluation_answer_relevance',
  evalAnsAccuracy: 'evaluation_answer_accuracy',
  evalAnsStyle: 'evaluation_answer_response_style'
}
