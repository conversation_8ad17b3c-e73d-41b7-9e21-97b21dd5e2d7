@use '../../../../assets/ifp-styles/abstracts' as *;

:host {
  display: block;
}

.ifp-census {
  &__wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 8px;

    // &::after {
    //   content: "";
    //   width: 100%;
    //   height: 100%;
    //   background: linear-gradient(90deg, rgba(0, 0, 0, 0.33), transparent);
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    // }
  }

  &__txt-wrapper {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    padding: 80px;
    z-index: 1;
    width: 50%;
  }

  &__txt {
    color: $ifp-color-black-global;
    font-size: $ifp-fs-8;
    line-height: 1.4;
    margin-top: $spacer-3;
    margin-bottom: $spacer-4;
    text-transform: uppercase;
    width: 80%;
    max-width: 500px;
    font-weight: $fw-semi-bold;
  }

  &__logo-wrapper {
    position: relative;
    display: inline-block;
    padding-bottom: $spacer-3;
  }

  &__logo {
    width: 170px;
  }

  &__image {
    width: 100%;
    min-height: 350px;
    object-fit: cover;

    &--mobile {
      display: none;
    }
  }

  &__year-badge {
    font-size: 0.8rem;
    background-color: $ifp-color-white-global;
    color: $ifp-color-black-global;
    padding: $spacer-0 $spacer-1;
    border-radius: 30px;
    position: absolute;
    bottom: 0;
    right: 0;
  }

  &__txt-one,
  &__inner-sub-txt {
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
  }

  &__txt-content-wrapper {
    margin-bottom: $spacer-2 + 2px;
  }

  &__text-sec,
  &__inner-text-wrapper {
    display: flex;
    align-items: center;
  }

  &__inner-text-wrapper {
    line-height: 1;
    margin-top: $spacer-1;
  }

  &__inner-txt {
    font-size: $ifp-fs-6;
    white-space: nowrap;
  }

  &__value {
    font-size: 5.2rem;
    font-weight: $fw-bold;
    margin: $spacer-0 $spacer-2;
  }

  &__sup {
    font-size: 0.6em;
  }

}

:host::ng-deep {
  .ifp-census__button {
    .ifp-btn {
      min-width: 190px;
      &--primary {
        background-color: $ifp-color-primary-blue;
      }
    }
  }

}

:host-context([dir="rtl"]) {
  .ifp-census__txt-wrapper {
    right: 0;
    left: auto;
  }
}

@include desktop-sm {
  .ifp-census {
    &__txt-wrapper {
      padding: $spacer-5;
    }
    &__value {
      font-size: 4.2rem;
    }

    &__txt-one,
    &__inner-sub-txt,
    &__inner-txt {
      font-size: $ifp-fs-5;
    }
  }
}

@include tablet {
  .ifp-census {
    &__inner-text-wrapper {
      display: block;
    }
  }
}

@include mobile-tablet {
  .ifp-census {
    &__txt-wrapper {
      padding: $spacer-4;
      width: 60%;
    }

    &__txt {
      font-size: $ifp-fs-6;
    }
  }
}


@include tablet-horizondal {
  .ifp-census {
    &__txt-wrapper {
      text-align: center;
      padding: $spacer-3;
      width: 100%;
      top: 40%;
    }

    &__txt {
      font-size: $ifp-fs-5;
      width: 100%;
      max-width: 360px;
      margin: $spacer-4 auto;
    }

    &__value {
      font-size: $ifp-fs-14;
    }

    &__txt-one,
    &__inner-sub-txt,
    &__inner-txt {
      font-size: $ifp-fs-4;
    }

    &__inner-text-wrapper {
      display: flex;
      justify-content: center;
    }

    &__txt-content-wrapper {
      margin-bottom: $spacer-4;
    }

    &__image {
      &--desktop {
        display: none;
      }

      &--mobile {
        display: block;
      }
    }
  }
}
