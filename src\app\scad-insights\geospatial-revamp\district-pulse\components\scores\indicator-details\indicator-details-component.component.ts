import { AfterViewInit, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit, Input, Output, EventEmitter, HostListener } from '@angular/core';
import { CommonModule, DatePipe, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';
import { StatisticsComponent } from '../statistics/statistics-component.component';
import { trigger, state, style, animate, transition } from '@angular/animations';

export interface IndicatorData {
  id: string;
  title: string;
  score: number;
  isActive: boolean;
}

interface StatisticData {
  icon: string;
  label: string;
  value: string;
  accessText?: string;
}

interface ChartData {
  title: string;
  percentage: number;
  level: string;
  hasInfo?: boolean;
}

@Component({
  selector: 'ifp-indicator-details',
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    StatisticsComponent,
    DatePipe,
    NgClass
  ],
  templateUrl: './indicator-details-component.component.html',
  styleUrl: './indicator-details-component.component.scss',
  animations: [
    trigger('slideAnimation', [
      transition('* => *', [
        style({ transform: '{{startPosition}}', opacity: 0 }),
        animate('400ms ease-out', style({ transform: 'translateX(0)', opacity: 1 }))
      ])
    ])
  ]
})
export class IndicatorDetailsComponent implements OnInit, AfterViewInit, OnDestroy {

  @Input() selectedIndicator: string | null = null;
  @Input() indicatorData: IndicatorData | undefined;
  @Output() closeDetails = new EventEmitter<void>();

  public detailsData: any = {};
  public areChildIndicatorsVisible = false;
  public activeIndicatorIndex = 0; // 0: Main, 1: Population, 2: Real Estate
  public slideDirection = 'translateX(0)';
  public totalIndicators = 3;
  public visibleIndicators = [true, false, false];

  constructor(
    private _cdr: ChangeDetectorRef,
    private _datePipe: DatePipe,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.loadDetailsData();
    // Initialize with only the first indicator visible
    this.visibleIndicators = [true, false, false];
  }

  ngAfterViewInit(): void { }

  ngOnDestroy(): void { }

  private loadDetailsData(): void {
    if (!this.selectedIndicator) return;

    switch (this.selectedIndicator) {
      case 'healthcare':
        this.detailsData = {
          icon: 'ifp-icon-health',
          charts: [
            {
              title: 'Accessibility',
              percentage: 95.37,
              level: 'High',
              hasInfo: true
            },
            {
              title: 'Availability',
              percentage: 97.56,
              level: 'High',
              hasInfo: true
            }
          ],
          statistics: [
            {
              icon: '../../../assets/icons/geospatial-ravamp/hospital_1.svg',
              label: 'Access to',
              value: '15 Hospitals'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/business-and-trade_1.svg',
              label: 'Access to',
              value: '89 Health centers'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/buildings.svg',
              label: 'Access to',
              value: '578 Clinics'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/medicine.svg',
              label: 'Access to',
              value: '1024 Pharmacies'
            }
          ]
        };
        break;

      case 'education':
        this.detailsData = {
          icon: 'ifp-icon-education',
          charts: [
            {
              title: 'School Coverage',
              percentage: 92.8,
              level: 'High',
              hasInfo: true
            },
            {
              title: 'Quality Index',
              percentage: 88.5,
              level: 'High',
              hasInfo: true
            }
          ],
          statistics: [
            {
              icon: '../../../assets/icons/geospatial-ravamp/school-icon.svg',
              label: 'Access to',
              value: '245 Schools'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/university.svg',
              label: 'Access to',
              value: '12 Universities'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/library.svg',
              label: 'Access to',
              value: '67 Libraries'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/training.svg',
              label: 'Access to',
              value: '89 Training Centers'
            }
          ]
        };
        break;

      case 'infrastructure':
        this.detailsData = {
          icon: 'ifp-icon-infrastructure',
          charts: [
            {
              title: 'Road Quality',
              percentage: 76.2,
              level: 'Mid',
              hasInfo: true
            },
            {
              title: 'Utilities Coverage',
              percentage: 84.7,
              level: 'High',
              hasInfo: true
            }
          ],
          statistics: [
            {
              icon: '../../../assets/icons/geospatial-ravamp/road.svg',
              label: 'Total',
              value: '2,450 km Roads'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/bridge.svg',
              label: 'Access to',
              value: '34 Bridges'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/utilities.svg',
              label: 'Access to',
              value: '156 Utilities'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/transport.svg',
              label: 'Access to',
              value: '28 Transport Hubs'
            }
          ]
        };
        break;

      case 'environment':
        this.detailsData = {
          icon: 'ifp-icon-environment',
          charts: [
            {
              title: 'Air Quality Index',
              percentage: 91.5,
              level: 'High',
              hasInfo: true
            },
            {
              title: 'Green Coverage',
              percentage: 87.8,
              level: 'High',
              hasInfo: true
            }
          ],
          statistics: [
            {
              icon: '../../../assets/icons/geospatial-ravamp/green-space-icon.svg',
              label: 'Access to',
              value: '89 Parks'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/tree.svg',
              label: 'Access to',
              value: '234 Green Spaces'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/air-quality-icon.svg',
              label: 'Air Quality',
              value: '91.5% Good'
            },
            {
              icon: '../../../assets/icons/geospatial-ravamp/water.svg',
              label: 'Water Quality',
              value: '94.2% Excellent'
            }
          ]
        };
        break;

      default:
        this.detailsData = {};
    }
  }

  onClose(): void {
    this.closeDetails.emit();
  }

  getChartCompletionWidth(percentage: number): number {
    return Math.min(Math.max(percentage, 0), 100);
  }

  getRemainingWidth(percentage: number): number {
    return 100 - this.getChartCompletionWidth(percentage);
  }

  // Get level color class
  getLevelColorClass(level: string): string {
    switch (level.toLowerCase()) {
      case 'high': return 'level-high';
      case 'mid': return 'level-mid';
      case 'low': return 'level-low';
      default: return '';
    }
  }

  // Toggle child indicators visibility
  toggleChildIndicators(): void {
    this.areChildIndicatorsVisible = !this.areChildIndicatorsVisible;
    
    // Always show all indicators when toggled
    if (this.areChildIndicatorsVisible) {
      // Show all indicators side by side
      this.visibleIndicators = [true, true, true]; 
    } else {
      // Only show the active indicator
      this.visibleIndicators = [false, false, false];
      this.visibleIndicators[this.activeIndicatorIndex] = true;
      console.log('Showing only active indicator:', this.activeIndicatorIndex, this.visibleIndicators);
    }
    
    this._cdr.detectChanges();
    console.log('Showing only active indicator:', this.visibleIndicators[0], this.visibleIndicators[1], this.visibleIndicators[2]);
  }

  // Switch between indicators (horizontal arrows)
  switchIndicator(): void {
    const prevIndex = this.activeIndicatorIndex;
    this.activeIndicatorIndex = (this.activeIndicatorIndex + 1) % this.totalIndicators;
    
    // Update visibility based on the current mode
    if (!this.areChildIndicatorsVisible) {
      // If child indicators are not visible, only show the active one
      this.visibleIndicators = [false, false, false];
      this.visibleIndicators[this.activeIndicatorIndex] = true;
    }
    
    // Determine slide direction
    this.slideDirection = 'translateX(100%)';
    this._cdr.detectChanges();
  }

  // Get animation params
  getAnimationParams(): any {
    return {
      value: this.activeIndicatorIndex,
      params: { startPosition: this.slideDirection }
    };
  }
}