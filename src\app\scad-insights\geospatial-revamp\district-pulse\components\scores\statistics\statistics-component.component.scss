.ifp-statistics-container {
  z-index: 2;
  padding: 0px 15px;
  width: 250px;
  background: #ffffff99;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  margin-top: 3px;
  cursor: pointer;
  
  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(2px);
  }
}

.poi-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  gap: 5px;
  padding: 5px 0;
  
  // Hide scrollbar but keep functionality
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  
  &::-webkit-scrollbar { /* WebKit */
    display: none;
  }

  .poi-elm {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: fit-content;
    flex-shrink: 0; // Prevents items from shrinking
    background-color: #FFF;
    border-radius: 5px;
    margin: 0; // Remove margin as we're using gap
    padding: 10px;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-1px);
    }

    img {
      width: 25px;
      height: 25px;
      margin-right: 5px;
      transition: transform 0.2s ease;
    }
    
    .poi-label {
      font-size: 12px;
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
      white-space: nowrap; // Prevent text wrapping
    }
    
    .poi-value {
      font-size: 12px;
      font-weight: 700;
      color: #007bff;
    }

    &.last-item {
      border-right: none;
    }

    &:hover img {
      transform: scale(1.1);
    }
  }
}

.indicator-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  
  .ifp-horizontal-icons{
    border: 1px solid #000;
    border-radius: 50px;
    position: relative;
    top: -3px;
    
    em {
      font-size: 10px!important;
      transition: all 0.2s ease;
      
      &:hover {
        color: #007bff;
        transform: scale(1.1);
      }
    }
  }
  
  .right-icons em {
    margin: 0px 5px;
  }
  
  .ifp-icon {
    cursor: pointer;
    font-size: 18px;
  }
  
  .left-icons em {
    font-size: 24px;
  }
  
  .right-icons {
    position: relative;
  }
}

.main-indicator-title {
  span{font-size: 12px;}
  margin-bottom: 5px;
}

// Responsive design
@media (max-width: 768px) {
  .ifp-statistics-container {
    width: 50%;
  }
  
  .poi-list {
    .poi-elm {
      width: 100px;
      padding: 8px;
      
      img {
        width: 20px;
        height: 20px;
      }
      
      .poi-label,
      .poi-value {
        font-size: 11px;
      }
    }
  }
}

@media (max-width: 480px) {
  .ifp-statistics-container {
    width: 80%;
  }
  
  .poi-list {
    height: 80px;
    
    .poi-elm {
      width: 80px;
      padding: 5px;
      
      img {
        width: 18px;
        height: 18px;
      }
      
      .poi-label,
      .poi-value {
        font-size: 10px;
      }
    }
  }
}