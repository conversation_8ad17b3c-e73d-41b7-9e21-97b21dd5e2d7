import { Component, computed, input, OnDestroy, signal, viewChild, model, output, OnChanges, inject, linkedSignal} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { GenAiDashboardQusCreateComponent, GenAiFormQuestion } from '../gen-ai-dashboard-qus-create/gen-ai-dashboard-qus-create.component';
import { GenAiDashboardFeedbackComponent } from '../gen-ai-dashboard-feedback/gen-ai-dashboard-feedback.component';
import { IfpModalComponent } from '../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { IfpRangeSliderComponent, sliderThemes } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-range-slider/ifp-range-slider.component";
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { DropdownOptionGenAi, GenAIQuestion, QuestionResponse } from '../gen-ai-dashboard-preview/gen-ai-dashboard-preview.component';
import { IfpMarkDownComponent } from "../../scad-insights/ifp-chat-bot/ifp-mark-down/ifp-mark-down.component";
import { questionDropDown } from '../constents/gen-ai-testing.constant';
import { IfpStarRatingComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-star-rating/ifp-star-rating.component";
import { GenAiDashboardRunPreviewComponent } from "../gen-ai-dashboard-run-preview/gen-ai-dashboard-run-preview.component";
import { IfpCardLoaderComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component";
import { loaderType } from 'src/app/scad-insights/core/constants/loader.constants';
import { IfpAiChartCardComponent } from 'src/app/scad-insights/ifp-chat-bot/ifp-ai-chart-card/ifp-ai-chart-card.component';
@Component({
  selector: 'ifp-gen-ai-dashboard-question',
  standalone: true,
  imports: [TranslateModule, NgClass, IfpButtonComponent, GenAiDashboardQusCreateComponent, GenAiDashboardFeedbackComponent, IfpModalComponent, IfpAiChartCardComponent, IfpTooltipDirective, IfpMarkDownComponent, IfpStarRatingComponent, GenAiDashboardRunPreviewComponent, IfpCardLoaderComponent],
  templateUrl: './gen-ai-dashboard-question.component.html',
  styleUrl: './gen-ai-dashboard-question.component.scss'
})
export class GenAiDashboardQuestionComponent implements OnDestroy, OnChanges{
  public mod = viewChild<IfpModalComponent>('modal');
  public submit = output<QuestionResponse>();
  public add = output<GenAiFormQuestion>();
  public ratingUpdate = output<{question_object_id:string, rating: number}>();
  public saveValue =  output<GenAiFormQuestion>();
  public delete =  output<string>();
  public addQuestionEvent = output();
  public response = input<Record<string, QuestionResponse>>();
  public addQuestionEnable =  input(true);
  public preview =  input(true);
  public enableDelete =  input(true);
  public previewEdit =  input(false);
  public qusEnable = input(false);
  public id = input(0);
  public feedback = input(true);
  public validation = input(false);
  public dropdown = input<Record<string,DropdownOptionGenAi[]>>({});
  public dropdownObject = input<Record<string,Record<string,DropdownOptionGenAi>>>({});
  public isSatisfied = model<null | boolean>(null);
  public statusValue = model(0);
  public feedbackValue = model('-');
    public loaderType = loaderType;
  public questionDropDown = questionDropDown;
  public getDropDownBadgeDataClassification = computed(() => {
    if (typeof this.question().data_classification != "string") {
      return this.dropdownObject()['data_classification'][ this.question().data_classification as number].display_name
    }
    return  this.question().data_classification as string;
});

public getDropDownBadgeDataGenClassification = computed(() => {
  if (typeof this.question().data_classification != "string") {
    return this.dropdownObject()['data_classification'][ this.question().ai_answer?.data_classification as number].display_name
  }
  return  this.question().ai_answer?.data_classification as string;
});

public getDropDownBadgeDataGensourceData = computed(() => {
  if (typeof this.question().source != "string") {
    return this.dropdownObject()['source_data'][ this.question().ai_answer?.source as number].display_name
  }
  return  this.question().ai_answer?.source as string;
});
public getDropDownBadgeDataType = computed(() => {
  if (typeof this.question().type != "string") {
    return this.dropdownObject()['reference_q_type'][ this.question().type as number].display_name
  }
  return  this.question().type as string;

});
public getDropDownBadgeDataComplexity = computed(() => {
  if (typeof this.question().complexity != "string") {
    return this.dropdownObject()['reference_q_complexity'][ this.question().complexity as number].display_name
  }
  return  this.question().complexity as string;

});
public getDropDownBadgeDataSource = computed(() => {
  if (typeof this.question().complexity != "string") {
    return this.dropdownObject()['source_data'][ this.question().source as number].display_name
  }
  return  this.question().source as string;

});

  public question=  model<GenAIQuestion>({
    object_id: '',
    question: '',
    type: '',
    complexity: '',
    source: '',
    data_classification: '',
    expected_answer: ''
  });

  public expand = signal(false);
  public qusEnableLocal = signal(false);
  public statusName = computed(() => {
    if (this.statusValue() < 33.33) {
      return 'Bad';
    } else if (this.statusValue() < 66.66 ){
      return 'Okay';
    }
    return 'Good';
  });
  public changeDetect = output<GenAiFormQuestion>();
  public changeDetectForQuestionChange = output<{event: GenAiFormQuestion, id: number}>();

  public buttonClass = buttonClass;
  public sliderTheme = sliderThemes;


  public buttonIconPosition= buttonIconPosition;
  public currentStatus = false;
  public evalAnsAccuracy = linkedSignal(() => this.dropdown()[questionDropDown.evalAnsAccuracy][0]);
  public evalAnsRelevance = linkedSignal(() => this.dropdown()[questionDropDown.evalAnsRelevance][0]);
  public evalAnsStyle = linkedSignal(() => this.dropdown()[questionDropDown.evalAnsStyle][0]);
  public selectedAccuracyIndex: number = 0;
  public selectedRelevanceIndex: number = 0;
  public selectedStyleIndex: number = 0;

  constructor(readonly _modal:IfpModalService) {}

  ngOnChanges(): void {
    this.qusEnableLocal.set(!(this.question()?.save ?? true));
  }


  openModal(value: boolean) {
    this.currentStatus =value;
    this.mod()?.createElement();
  }

  closeModal() {
    this.mod()?.removeModal();
    this._modal.removeAllModal();
  }

  addQuestion(event:{ans: GenAiFormQuestion}) {
    this.add.emit(event.ans);
    this.qusEnableLocal.set(!this.qusEnableLocal());
  }

  save(event:GenAiFormQuestion ) {
    this.saveValue.emit(event);
    this.qusEnableLocal.set(!this.qusEnableLocal());
  }
  edit() {
    this.qusEnableLocal.set(!this.qusEnableLocal());
  }

  rangeChange(event: number){
    this.statusValue.set(event);
    this.ratingUpdate.emit({
      question_object_id: this.question().object_id,
      rating:this.statusValue() ,
    });
  }


  submitSurvey(event: string) {
   const review =  {
      question_object_id: this.question().object_id,
      is_satisfied: this.isSatisfied() ?? false,
      rating:this.statusValue(),
      additional_feedback:event,
      metrics: {
        [questionDropDown.evalAnsAccuracy]: this.evalAnsAccuracy().id,
        [questionDropDown.evalAnsRelevance]: this.evalAnsRelevance().id,
        [questionDropDown.evalAnsStyle]: this.evalAnsStyle().id
      }
    }
    this.isSatisfied.set(this.currentStatus);
    this.submit.emit(review);
    this.closeModal();
  }

  onSelectEvalAccuracy(index: number) {
    this.selectedAccuracyIndex = index;
    this.evalAnsAccuracy.set(this.dropdown()[questionDropDown.evalAnsAccuracy][index]);
  }

  onSelectEvalRelevance(index: number) {
    this.selectedRelevanceIndex = index;
    this.evalAnsRelevance.set(this.dropdown()[questionDropDown.evalAnsRelevance][index]);
  }

  onSelectEvalStyle(index: number) {
    this.selectedStyleIndex = index;
    this.evalAnsStyle.set(this.dropdown()[questionDropDown.evalAnsStyle][index]);
  }

  ngOnDestroy(): void {
    this.closeModal();
  }
}


