@use "../../../../../../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-range {
  width: 100%;
  margin-inline-start: auto;
  position: relative;
  padding: $spacer-3 0;
  
  &__labels {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-2;
    padding: 0 $spacer-1;
  }
  
  &__label {
    font-size: $ifp-fs-3;
    font-weight: $fw-medium;
    color: $ifp-color-grey-4;
    
    &--min {
      color: #4A90E2; // Blue color
    }
    
    &--max {
      color: #FF6B35; // Orange/Red color
    }
  }
  
  &__track {
    position: relative;
    height: 8px;
    background: linear-gradient(to right, #4A90E2 0%, #7ED321 50%, #FF6B35 100%);
    border-radius: 4px;
    overflow: hidden;
  }
  
  &__tooltip {
    display: none;
    font-size: $ifp-fs-4;
    color: $ifp-color-white-global;
    font-weight: $fw-semi-bold;
    padding: $spacer-2 $spacer-3;
    background-color: $ifp-color-grey-6;
    border-radius: 7px;
    position: absolute;
    top: 100%;
    transform: translateX(-50%);
    margin-top: $spacer-3;
    z-index: 10;
    
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 4px solid $ifp-color-grey-6;
    }
  }
  
  &__input {
    display: block;
    width: 100%;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    position: relative;
    z-index: 2;
    cursor: grab;
    
    &:active {
      cursor: grabbing;
    }
    
    /* Chrome, Safari, Edge */
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 20px;
      height: 20px;
      background: #FFFFFF;
      border: 3px solid #4A90E2;
      border-radius: 50%;
      cursor: grab;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      transition: all 0.2s ease;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
      }
      
      &:active {
        cursor: grabbing;
        transform: scale(0.95);
      }
    }

    /* Firefox */
    &::-moz-range-thumb {
      width: 20px;
      height: 20px;
      background: #FFFFFF;
      border: 3px solid #4A90E2;
      border-radius: 50%;
      cursor: grab;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      transition: all 0.2s ease;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
      }
      
      &:active {
        cursor: grabbing;
        transform: scale(0.95);
      }
    }
    
    &::-moz-range-track {
      background: transparent;
      border: none;
    }
    
    &:focus {
      outline: none;
      
      &::-webkit-slider-thumb {
        border-color: #FF6B35;
        box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
      }
      
      &::-moz-range-thumb {
        border-color: #FF6B35;
        box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
      }
      
      & + .ifp-range__tooltip {
        display: inline-block;
      }
    }
  }
  
  &__dots-wrapper {
    width: 100%;
    height: 100%;
    display: none;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 $spacer-1;
    z-index: 1;
  }
  
  &__dot {
    width: 4px;
    min-width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  
  // Theme variations
  &--green {
    .ifp-range__track {
      background: linear-gradient(to right, #7ED321 0%, #4CAF50 100%);
    }
    
    .ifp-range__input {
      &::-webkit-slider-thumb {
        border-color: #4CAF50;
      }
      
      &::-moz-range-thumb {
        border-color: #4CAF50;
      }
      
      &:focus {
        &::-webkit-slider-thumb,
        &::-moz-range-thumb {
          box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
        }
      }
    }
    
    .ifp-range__label {
      &--min {
        color: #7ED321;
      }
      
      &--max {
        color: #4CAF50;
      }
    }
    
    .ifp-range__dots-wrapper {
      display: flex;
    }
  }
  
  &--steps {
    .ifp-range__dots-wrapper {
      display: flex;
    }
  }
  
  &--disable {
    opacity: 0.6;
    pointer-events: none;
    
    .ifp-range__input {
      cursor: not-allowed;
      
      &::-webkit-slider-thumb {
        display: none;
      }
      
      &::-moz-range-thumb {
        display: none;
      }
    }
  }
}

// RTL Support
:host-context([dir="rtl"]) {
  .ifp-range {
    &__track {
      background: linear-gradient(to left, #4A90E2 0%, #7ED321 50%, #FF6B35 100%);
    }
    
    &__input {
      direction: ltr;
      transform: rotate(180deg);
    }
    
    &__tooltip {
      transform: translateX(50%);
    }
    
    &--green {
      .ifp-range__track {
        background: linear-gradient(to left, #7ED321 0%, #4CAF50 100%);
      }
    }
  }
}
