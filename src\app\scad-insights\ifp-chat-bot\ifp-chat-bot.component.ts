import { SlaData, slaService } from './../core/services/sla/sla.service';
import { ChangeDetectorRef, Component, ElementRef, inject, OnD<PERSON>roy, OnInit, signal, ViewChild, WritableSignal } from '@angular/core';
import { IfpModalComponent } from '../ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { AsyncPipe, NgClass, SlicePipe, TitleCasePipe } from '@angular/common';
import { IfpButtonComponent } from '../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from '../core/constants/button.constants';
import { ChatBotApiService } from '../core/services/chat-bot/chat-bot.service';
import { StreamingService } from '../core/services/http/streaming.service';
import type {
  ThreadPreview,
  ThreadMessage,
  StreamMessage,
  UserMessageBody
} from '../core/interface/chatbot.interface';
import { aiResponseTypes, responseSettings } from './constants/ifp-ai.constants';
import { OutsideClickDirective } from '../core/directives/outsideClick.directive';
import { IfpSearchComponent } from '../ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { IfpAiChartCardComponent } from './ifp-ai-chart-card/ifp-ai-chart-card.component';
import { fadeInOut } from '../animation/fade.animation';
import { environment } from 'src/environments/environment';
import { cloneDeep } from 'lodash';
import { ToasterService } from '../core/services/tooster/ToastrService.service';
import { MarkdownModule, provideMarkdown } from 'ngx-markdown';
import { IfpMarkDownComponent } from './ifp-mark-down/ifp-mark-down.component';
import { IfpTooltipDirective } from '../core/directives/ifp-tooltip.directive';
import { IfpInfoComponent } from '../ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component';
import { ThemeService } from '../core/services/theme/theme.service';
import { CustomCardService } from '../core/services/create-dashboard/custom-card.service';
import { debounceTime, forkJoin, ObservableInput, Subject, Subscription } from 'rxjs';
import { DashboardService } from '../core/services/create-dashboard/dashboard.service';
import { IfpDashboardBuilderComponent } from '../../dashboard-builder/pages/ifp-dashboard-builder/ifp-dashboard-builder.component';
import { Router } from '@angular/router';
import { SubSink } from 'subsink';
import { AppendToBodyDirective } from '../core/directives/append-body.directive';
import { UsageDashboardLogService } from '../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpUnderMaintainanceComponent } from '../ifp-widgets/ifp-organism/ifp-under-maintainance/ifp-under-maintainance.component';
import { manitananceKeys } from '../core/constants/chart.constants';
import { DownLoadService } from '../core/services/download-service/download.service';
import { SafePipe } from '../core/pipes/safe.pipe';
import { IFPMsalService } from '../core/services/IFP-msal.service';
import { IfpDropdownComponent } from "../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { PanelDropdownOptions } from 'src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { ApiGenAiService } from '../core/services/api-gen-ai.service';
import { genAiTestingApi } from 'src/app/gen-ai-dashboard/constents/gen-ai-testing.constant';
import { DropdownInterfaceGenAi } from 'src/app/gen-ai-dashboard/gen-ai-dashboard-preview/gen-ai-dashboard-preview.component';



@Component({
  selector: 'ifp-chat-bot',
  templateUrl: './ifp-chat-bot.component.html',
  styleUrl: './ifp-chat-bot.component.scss',
  animations: [fadeInOut],
  imports: [IfpModalComponent, TranslateModule, FormsModule, AsyncPipe, IfpButtonComponent, NgClass, SlicePipe, OutsideClickDirective, IfpSearchComponent, IfpAiChartCardComponent,
    MarkdownModule, IfpMarkDownComponent, IfpTooltipDirective, IfpInfoComponent, IfpDashboardBuilderComponent, AppendToBodyDirective,
    TitleCasePipe, IfpUnderMaintainanceComponent, SafePipe, IfpDropdownComponent],
  providers: [provideMarkdown()]
})

export class IfpChatBotComponent implements OnInit, OnDestroy {
  @ViewChild('gpt') gpt!: IfpModalComponent;
  @ViewChild('chatBox') chatBox!: ElementRef;
  @ViewChild('dislikeRef') dislikeRef!: ElementRef;
  @ViewChild('outsideRef') outsideRef!: ElementRef;
  @ViewChild('aiRef') aiRef!: ElementRef;

  public showModal: boolean = false;
  public buttonClass = buttonClass;
  public isTopThreadsExpanded: boolean = true;
  public isRecentThreadsExpanded: boolean = true;
  public responseTypes: AiResponseCategory[] = aiResponseTypes;
  public selectedCategory: AiResponseCategory = this.responseTypes[0];
  public showCategoryList: boolean = false;
  public showTools: boolean = false;
  public searchThread!: string;
  public postion = buttonIconPosition;
  public isResponseLoading: boolean = false;
  public chartNodes: { node_id: string, content_type: string }[] = [];
  public isYesterdayThread: boolean = false;
  public previousSevenThred: boolean = false;
  public previousThirtyThred: boolean = false;
  public selectedLanguage: string = 'en';
  public expnadedKeys!: string;
  public showAllPrompt: boolean = false;
  public searchPrompt!: string;
  public aiSettings = responseSettings;
  private generateApiSubscription!: Subscription;
  public domains: any[] = [];
  public searchSub = new Subject<{ type: string, searchString: string }>();
  public selectedDomain: string = 'all';
  public buttonIconPosition = buttonIconPosition;
  private clonedPromptList: any[] = [];
  public promptCategory = ['Government Affairs', 'Agriculture', 'Economy', 'Forecasts'];
  public promptList: any[] = [];
  public env = environment;
  public genAiFeedback: string = '';
  public subs = new SubSink();
  public isExpanded: boolean = false;
  public feedbackModel: WritableSignal<boolean> = signal(false);
  public ratingData: number = 0;
  public feedBackSubmit: boolean = false;
  public selectedDataSource: string = responseSettings.scad;
  public responseType: string = responseSettings.short;
  public isChatSettingsOpen: WritableSignal<boolean> = signal(false);
  public auditReportAccess: WritableSignal<boolean> = signal(false);
  public genAiMaintanance: boolean = false;
  public currentThreadId: string = '';
  public allPromptsKey = {
    key: 'all',
    name: 'All Prompts'
  };

  public recentFilter: any = {
    last_7: 'Previous 7 Days',
    last_30: 'Previous 30 Days',
    today: 'Today',
    yesterday: 'Yesterday'
  };

  public versions: PanelDropdownOptions[] = [
  ];
  public versionsValue = {
    key: 'latest',
    value: 'latest',
    checked: false,
  };

  public _apiService = inject(ApiGenAiService);
  dropdownVersion(dropdown: PanelDropdownOptions) {
    this.versionsValue = dropdown;
    if (dropdown.key === 'latest' && !this.isSourceChanged) {
      this.selectedDataSource = this.aiSettings.tamm;
    } else if (dropdown.key !== 'latest' && this.selectedDataSource === this.aiSettings.tamm) {
      this.selectedDataSource = this.aiSettings.scad;
    }
  }


  /**
   * Flag to track if the thread is new.
   * Controls display between fresh chat UI or existing messages.
   */
  public isNew: boolean = true;

  /** User's question/query for a given thread. */
  public query: string = '';

  /** Current selected thread by user. Updated once user
   * selects thread from existing thread or on first AI server
   * response is received. Contains object_id which must be
   * passed to server if user sends messages from existing thread.
   * Else all messages will belong to separate threads.
   */
  public currentThread: ThreadPreview | null = null;

  /** List of all messages for a given thread. */
  public threadMessages: { [key: string]: { messages: ThreadMessage[], loading: boolean } } = {};

  /** Display current loading status of AI Server response.
   * Eg: Processing..., Analyzing... etc. only displayed during
   * "in-progress" StreamMessage events.
   */
  public currentStatus: string = '';

  /** Store list of threads, use to list recent threads.
   * Currently 5 most recents are shown as per business
   * logic.
   */
  public recentThreads: ThreadPreview[] = [];

  public topThreads: ThreadPreview[] = [
    {
      object_id: 't1',
      title: 'Hotel bookings in UAE'
    },
    {
      object_id: 't2',
      title: 'Inflation figures in Abu Dhabi'
    },
    {
      object_id: 't3',
      title: 'GDP data of UAE across years'
    },
    {
      object_id: 't4',
      title: 'Top 3 crops produced in UAE'
    },
    {
      object_id: 't5',
      title: 'Top 5 commodities exported from UAE'
    }
  ];

  public versionSwitchAccess = signal(false);
  public maintainanceData!: SlaData;
  public isReasonEnabled: boolean = false;
  public currentReason: string = '';
  public evaluatedEnabled = signal(false);
  public isTammAccess: WritableSignal<boolean> = signal(false);

  public recentThreadsToDisplay: any = this.recentThreads;
  public topThreadsToDisplay: ThreadPreview[] = JSON.parse(JSON.stringify(this.topThreads));
  private sessionId!: string;
  private streamRunning: boolean = false;
  public smartSuggestion: { suggestion: string, Related_Query: string }[] = [];
  public isSourceChanged = false;


  constructor(private _streamservice: StreamingService, private _apiservice: ChatBotApiService, private _toaster: ToasterService,
    public themeService: ThemeService, private _customService: CustomCardService, private _dashboardService: DashboardService, private _router: Router,
    private _translate: TranslateService, private _cdr: ChangeDetectorRef, private log: UsageDashboardLogService, public ifpSlaService: slaService, private _downloadService: DownLoadService,
    private _msalService: IFPMsalService) {
    this.themeService.defaultLang$.subscribe((lang) => {
      this.selectedLanguage = lang;
    });

    this.subs.add(this.searchSub
      .pipe(debounceTime(800))
      .subscribe((_value) => {
        if (_value.type == 'prompt') {
          this.searchPrompts();
        } else if (_value.type == 'thread') {
          this.callThreads();
        }
      }));
    this.checkAuditReportApproval();
  }


  ngOnInit(): void {
    this.callDropdownApi('ai_model');
    this.getPrompts();
    this.getAccess();
    this.checkTammUserAccess();
    this.checkVersionSwitch();
  }

  checkVersionSwitch() {
    this.subs.add(this.ifpSlaService.checkChatbotStatus().subscribe((data: { access: true }) => {
      this.versionSwitchAccess.set(data.access);
    }));
  }

  openModal() {
    if (this.showModal) {
      this.closeModal();
    } else {
      this.showModal = true;
      this.gpt.createElement();
      this.checkMaintanance();
      this.callThreads();
    }
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.GenAi, this.log.currentTime);
    (window as any)?.dataLayer?.push({
      'event': 'gen_ai_event',
      'gen-ai': 'gen-ai'
    });
  }

  callThreads() {
    this._apiservice.getRecentThreads(this.searchThread).subscribe(resp => {
      this.recentThreads = resp;
      this.recentThreadsToDisplay = this.recentThreads;
      const objectsArray = Object.keys(this.recentThreadsToDisplay);
      if (this.searchThread) {
        const foundKey = objectsArray.find(key => this.recentThreadsToDisplay[key].length > 0);
        if (foundKey) {
          this.expnadedKeys = foundKey;
        }
      } else {
        this.expnadedKeys = objectsArray[0];
      }
    });
  }

  generateResponse(query: string, reGenerate: boolean = false, id: string = '0', index: number = 0, isRunning: boolean = false) {
    // if (!query.trimStart()) {
    //   console.log("query trimstart")
    //   return;
    // }
    let abortController !: AbortController;
    if (!this.threadMessages[this.currentThreadId]) {
      this.threadMessages[this.currentThreadId] = { messages: [], loading: false };[];
    }
    console.log("isRunning", isRunning)
    if (isRunning) {
      console.log("this.threadMessages[this.currentThreadId].loading", this.threadMessages[this.currentThreadId].loading)
      abortController?.abort();
      this.streamRunning = false;
      this.generateApiSubscription.unsubscribe();
      this.threadMessages[this.currentThreadId].loading = false;
      return;
    }
    this.hideAllFeedbacks();
    this.threadMessages[this.currentThreadId].loading = true;
    if (query.trimStart() !== '') {
      // List user message
      const messageRole = 'USER' as const;
      const messageType = 'CNV' as const;
      const userMessage = {
        created_at: '',
        data: {
          content: query
        },
        deleted_at: null,
        id: reGenerate ? id : '0',
        object_id: '',
        role: messageRole,
        type: messageType,
        updated_at: '',
        hyperlink_dict: []
      };
      reGenerate ? this.threadMessages[this.currentThreadId].messages[index - 1] = userMessage : this.threadMessages[this.currentThreadId].messages.push(userMessage);
      let body: UserMessageBody = {
        action: reGenerate ? 'regenerate' : 'next', // regenerate
        reasoning: this.isReasonEnabled,
        message: {
          id: reGenerate ? id : null,
          content: query,
          type: this.selectedCategory.type,
          source: this.selectedDataSource,
          responseType: this.responseType
        }
      };
      this.scrollToBottom();
      if (this.currentThread?.object_id) {
        body = { ...body, conversation_id: this.currentThread.object_id };
      }
      this.streamRunning = true;
      this.generateApiSubscription = this._streamservice.connectToServerSentEventsPost(`${environment.genAiBaseUrl}/${environment.genAiChatVersion}/chats/?version=${this.versionsValue.key}`, body).
        subscribe({
          next: (event) => {
            const { event: eventType, data } = event.msg;
            abortController = event.ctrl;
            const messageData: StreamMessage = JSON.parse(data);
            if (eventType == 'message') {
              if (messageData.status == 'in-progress') {
                const { message } = messageData;
                this.currentStatus = message.content;
                // this.chartNodes = [{indicator_id: '6673', content_type: 'statistics-insights'}];
              }
              if (this.isReasonEnabled && messageData.status === 'reasoning') {
                this.currentReason = messageData.message.content;
              }
            } else if (eventType == 'end') {
              if (messageData.status == 'complete') {
                this.smartSuggestion = [];
                this.currentStatus = '';
                this.streamRunning = false;
                const { message } = messageData;
                this.smartSuggestion = messageData.Smart_Suggestion_list;
                if (this.currentThreadId == '') {
                  this.currentThreadId = messageData.chat_id;
                  this.threadMessages[this.currentThreadId] = { messages: [], loading: false };
                  this.threadMessages[this.currentThreadId].messages.push(userMessage);
                }
                const aiMessage = {
                  created_at: '',
                  data: {
                    content: message.content
                  },
                  deleted_at: null,
                  id: message.id,
                  object_id: '',
                  role: 'AI' as const,
                  type: 'CNV' as const,
                  updated_at: '',
                  chart_nodes: [] as any,
                  chart_desc: messageData.chart_desc,
                  responseType: this.selectedCategory.type,
                  show_viz: messageData.show_viz,
                  reason: this.currentReason,
                  hyperlink_dict: messageData.hyperlink_dict ?? []
                };
                this.currentReason = '';
                aiMessage.chart_nodes = [];
                if (messageData.show_viz) {
                  if (messageData?.custom_charts?.length) {
                    messageData?.custom_charts.map((x: { isCustom: boolean; }) => x.isCustom = true);
                    // messageData.custom_charts = messageData?.custom_charts.map((x: { isCustom: boolean; }) => x.isCustom = true);
                    aiMessage.chart_nodes = [...messageData.viz_data, ...messageData.custom_charts];
                  } else {
                    aiMessage.chart_nodes = messageData.viz_data;
                  }
                }
                this.threadMessages[this.currentThreadId].loading = false;
                // Insert or update AI message in threadMessages
                if (!this.threadMessages[messageData.chat_id]) {
                  this.threadMessages[messageData.chat_id] = { messages: [], loading: false };[];
                }
                if (reGenerate) {
                  this.threadMessages[messageData.chat_id].messages[index] = aiMessage;
                } else {
                  this.threadMessages[messageData.chat_id].messages.push(aiMessage);
                }
                // reGenerate ? this.threadMessages[index] = aiMessage : this.threadMessages.push(aiMessage);
                this.scrollToBottom();
                if (reGenerate) {
                  this.threadMessages[messageData.chat_id].messages[index].hide = false;
                }
                this.currentThread = {
                  object_id: messageData.chat_id,
                  title: query
                };
                // Update 'Recents'
                // const isExistingThread = this.recentThreads.some(
                //   (thread) => thread.object_id == this.currentThread?.object_id
                // );
                // if (!isExistingThread) {
                // if (this.recentThreads.length > 0 && this.recentThreads.length <= 4) {
                // this.recentThreadsToDisplay.today.unshift(this.currentThread);
                // } else {
                //   this.recentThreads.pop();
                //   this.recentThreads.unshift(this.currentThread);
                // }
                // }
                if (this.selectedCategory) {
                  aiMessage.responseType = aiMessage.chart_nodes?.length ? this.selectedCategory.type : 'general';
                  if (aiMessage.chart_nodes?.length <= 0) {
                    this.selectedCategory = this.responseTypes[0];
                  }
                  if (this.selectedCategory.type == 'dashboard') {
                    this.createDashboard(this.threadMessages[this.currentThreadId].messages.length - 1);
                  }
                }
                if ('' in this.threadMessages) {
                  delete this.threadMessages[''];
                  this.callThreads();
                  this._cdr.detectChanges();
                }
              }
              console.log("smartSuggestion", this.smartSuggestion)
            }
          },
          error: () => {
            this.checkMaintanance();
            this.streamRunning = false;
          }
        })
      // .subscribe(event => {
      //   const { event: eventType, data } = event.msg;
      //   abortController = event.ctrl;
      //   const messageData: StreamMessage = JSON.parse(data);
      //   if (eventType == 'message') {
      //     if (messageData.status == 'in-progress') {
      //       const { message } = messageData;
      //       this.currentStatus = message.content;
      //       // this.chartNodes = [{indicator_id: '6673', content_type: 'statistics-insights'}];
      //     }
      //   } else if (eventType == 'end') {
      //     if (messageData.status == 'complete') {
      //       this.currentStatus = '';
      //       const { message } = messageData;
      //       const aiMessage = {
      //         created_at: '',
      //         data: {
      //           content: message.content
      //         },
      //         deleted_at: null,
      //         id: message.id,
      //         object_id: '',
      //         role: 'AI' as const,
      //         type: 'CNV' as const,
      //         updated_at: '',
      //         chart_nodes: [] as any,
      //         chart_desc: messageData.chart_desc,
      //         responseType: this.selectedCategory.type,
      //         show_viz: messageData.show_viz
      //       };
      //       aiMessage.chart_nodes = [];
      //       if (messageData.show_viz) {
      //         if (messageData?.custom_charts?.length) {
      //           messageData?.custom_charts.map((x: { isCustom: boolean; }) => x.isCustom = true);
      //           // messageData.custom_charts = messageData?.custom_charts.map((x: { isCustom: boolean; }) => x.isCustom = true);
      //           aiMessage.chart_nodes = [...messageData.viz_data, ...messageData.custom_charts];
      //         } else {
      //           aiMessage.chart_nodes = messageData.viz_data;
      //         }
      //       }
      //       this.isResponseLoading = false;
      //       reGenerate ? this.threadMessages[index] = aiMessage : this.threadMessages.push(aiMessage);
      //       this.scrollToBottom();
      //       if (reGenerate) {
      //         this.threadMessages[index].hide = false;
      //       }
      //       this.currentThread = {
      //         object_id: messageData.chat_id,
      //         title: query
      //       };
      //       // Update 'Recents'
      //       // const isExistingThread = this.recentThreads.some(
      //       //   (thread) => thread.object_id == this.currentThread?.object_id
      //       // );
      //       // if (!isExistingThread) {
      //       // if (this.recentThreads.length > 0 && this.recentThreads.length <= 4) {
      //       // this.recentThreadsToDisplay.today.unshift(this.currentThread);
      //       // } else {
      //       //   this.recentThreads.pop();
      //       //   this.recentThreads.unshift(this.currentThread);
      //       // }
      //       // }
      //       if (this.selectedCategory) {
      //         aiMessage.responseType = aiMessage.chart_nodes?.length ? this.selectedCategory.type : 'general';
      //         if (aiMessage.chart_nodes?.length <= 0) {
      //           this.selectedCategory = this.responseTypes[0];
      //         }
      //         if (this.selectedCategory.type == 'dashboard') {
      //           this.createDashboard(this.threadMessages.length - 1);
      //         }
      //       }
      //     }
      //   }
      // }, _error => {
      //   this.checkMaintanance();
      // });
      this.isNew = false;
      this.query = '';
    }
  }

  getAccess() {
    this.subs.add(
      this.ifpSlaService.checkTestingTools().subscribe((data: { access: true }) => {
        this.evaluatedEnabled.set(data.access);
      }));
  }

  checkTammUserAccess() {
    this.subs.add(
      this.ifpSlaService.checkTammAccess().subscribe({
        next: () => {
          this.isTammAccess.set(true),
            this.selectedDataSource = this.aiSettings.tamm;
        },
        error: () => this.isTammAccess.set(false)
      })
    );
  }


  getPrompts() {
    this._streamservice.getPrompts(`${environment.genAiBaseUrl}/v2/prompt`).subscribe((resp: any) => {
      if (this.selectedLanguage) {
        const domainKey = `name_${this.selectedLanguage}`;
        this.domains = resp.map((x: { domain: any; }) => `${x.domain[domainKey]}`);
        this.domains = [...new Set(this.domains)];
        this.promptList = resp;
        this.clonedPromptList = cloneDeep(this.promptList);
      }

    });
  }


  getThreadResponse(thread: ThreadPreview) {
    if (this.currentThreadId == '' && this.streamRunning && !this.recentThreadsToDisplay['today'].find((x: { title: string; }) => x.title == 'New Chat')) {
      this.recentThreadsToDisplay['today'].unshift({ object_id: '', title: 'New Chat', created_at: new Date() });
    }
    this.currentThreadId = thread.object_id;
    this.threadMessages[this.currentThreadId] = { messages: thread.object_id != '' ? [] : this.threadMessages[''].messages, loading: this.threadMessages?.[this.currentThreadId]?.loading ?? false };
    if (thread.object_id == '') {
      this.threadMessages[this.currentThreadId].messages = this.threadMessages[''].messages;
      this.threadMessages[this.currentThreadId].messages.map(x => x.id = x.object_id);
      this.scrollToBottom();
    } else {
      this._apiservice.getThread(thread.object_id).subscribe(resp => {
        const { messages } = resp;
        this.threadMessages[this.currentThreadId].messages = messages.filter(x => x.data.content != '');
        this.threadMessages[this.currentThreadId].messages.map(x => x.id = x.object_id);
        this.scrollToBottom();
      });
    }
    this.query = '';
    this.isNew = false;
    this.currentThread = thread;
    this.isExpanded = false;
  }

  startNewThread() {
    this.currentThreadId = '';
    this.isNew = true;
    this.query = '';
    this.currentThread = null;
    this.threadMessages[this.currentThreadId] = { messages: [], loading: false };
    this.showAllPrompt = false;
    this.selectedCategory = this.responseTypes[0];
    this.filterPrompt(this.allPromptsKey.key);
    this.callThreads();
  }

  closeModal() {
    this.gpt.removeModal();
    this.query = '';
    this.showModal = !this.showModal;
    this.currentThread = null;
    this.isExpanded = false;
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
  }

  toggleCategory() {
    this.showCategoryList = !this.showCategoryList;
  }

  selectCategory(item: AiResponseCategory, afterMessage: boolean, index: number = 0, responseType: string = 'input') {
    if (responseType == 'input') {
      this.selectedCategory = item;
    }
    this.showCategoryList = false;
    this.threadMessages[this.currentThreadId].messages.map((msg) => {
      msg.showTools = false;
    });
    if (afterMessage && item.type != 'dashboard') {
      this.threadMessages[this.currentThreadId].messages[index].responseType = item.type;
    }
    if (item.type == 'dashboard' && this.threadMessages[this.currentThreadId].messages[index]) {
      this.createDashboard(index);
    }
  }

  callDropdownApi(name: string) {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.dropdownType + name).subscribe((data: DropdownInterfaceGenAi) => {
      const modelValues: PanelDropdownOptions[] = [];
      data.options.forEach(element => {
        modelValues.push({
          key: element.value,
          value: element.display_name,
          checked: false,
        })
      });
      this.versions = modelValues;
      this.versionsValue = modelValues[0];
    }));
  }


  searchThreads(keyword: string) {
    this.searchThread = keyword;
    this.searchSub.next({ type: 'thread', searchString: this.searchThread });
  }

  toggleTopThreadsAccordian() {
    this.isTopThreadsExpanded = !this.isTopThreadsExpanded;
  }

  toggleRecentThreadsAccordian() {
    this.isRecentThreadsExpanded = !this.isRecentThreadsExpanded;
  }

  toggleAiToolsDropdown(message: ThreadMessage) {
    const isSelected = cloneDeep(message.showTools);
    this.threadMessages[this.currentThreadId].messages.map((msg) => {
      msg.showTools = false;
    });
    message.showTools = isSelected ? false : true;
  }

  checkInputKeyword(flag: boolean) {
    const disabledValues = ['', undefined];
    return !flag ? !disabledValues.includes(this.query.trimStart()) : disabledValues.includes(this.query.trimStart());
  }

  copyText(textToCopy: any) {
    navigator.clipboard.writeText(textToCopy).then(() => {
      this._toaster.success('Text copied to clipboard');
    }).catch(_err => {
      this._toaster.error('Failed to copy text');
    });
  }

  regenerate(index: number) {
    const query = this.threadMessages[this.currentThreadId].messages[index - 1]?.data.content;
    const id = this.threadMessages[this.currentThreadId].messages[index].id;
    this.threadMessages[this.currentThreadId].messages[index].hide = true;
    this.generateResponse(query, true, id, index);
  }

  loopObject() {
    return Object.keys(this.recentThreadsToDisplay);
  }

  assignSelectedThread(key: string) {
    this.expnadedKeys = this.expnadedKeys == key ? '' : key;
  }

  scrollToBottom() {
    setTimeout(() => {
      if (this.chatBox) {
        this.chatBox.nativeElement.scrollTop = this.chatBox.nativeElement.scrollHeight;
      }
    }, 300);
  }

  createDashboard(index: number) {
    if (this.threadMessages[this.currentThreadId].messages[index].dashbordId) {
      this.threadMessages[this.currentThreadId].messages[index].responseType = this.responseTypes[3].type;
      return;
    }

    const apis: ObservableInput<any>[] = [];
    if (this.threadMessages[this.currentThreadId].messages[index]?.chart_nodes?.length > 0) {
      this.threadMessages[this.currentThreadId].messages[index]?.chart_nodes.forEach((element: { isCustom: boolean; data: number[]; category: string[] }) => {
        if (element.isCustom) {
          const customData = element.data.map((dt: number, j: number) => ({
            category: element.category[j],
            value: dt
          }));
          apis.push(this._customService.uploadCustomData(customData));
        }
      });
      forkJoin(apis).subscribe({
        next: async next => {
          let customIndex: number = -1;
          const customCardData: any[] = [];
          const indicatorData: any[] = [];
          this.threadMessages[this.currentThreadId].messages[index]?.chart_nodes.forEach((chart: {
            yAxisLabel: any;
            type: any;
            color: any;
            name: string; node_id: any; isCustom: any;
          }) => {
            if (chart.isCustom) {
              const customId: number = this.generateRandomNumber();
              customIndex = customIndex + 1;
              customCardData.push({
                group: null,
                id: customId,
                nodeRequest: false,
                properties: {
                  Xaxis: { checked: true, disabled: false, key: 'category', name: 'category' },
                  Yaxis: [{ checked: true, disabled: false, key: 'value', name: 'value' }],
                  chartType: chart.type,
                  cols: 3,
                  dataObjectId: next[customIndex].dataId,
                  fileName: 'Custom chart',
                  cardDescription: chart.yAxisLabel,
                  id: customId,
                  isOpen: true,
                  key: 'custom_card',
                  rows: 9,
                  title: chart.name ?? 'Custom',
                  type: 'column',
                  x: 0,
                  y: 0,
                  lineColors: [{
                    index: 0,
                    color: chart.color
                  }],
                  dataLabel: false
                }
              });
            } else {
              indicatorData.push({
                group: null,
                id: chart.node_id,
                nodeRequest: false,
                properties: {
                  cols: 3,
                  id: chart.node_id,
                  key: 'official_statistics',
                  rows: 11,
                  screener: false,
                  title: chart?.name ?? '',
                  chartType: chart.type,
                  type: 'scad',
                  x: 0,
                  y: 0,
                  lineColors: [{
                    index: 0,
                    color: chart.color
                  }],
                  dataLabel: false
                }
              });
            }
          });

          const dashboardPayload = {
            name: 'Untitled',
            nodes: {
              custom_card: customCardData,
              official_statistics: indicatorData
            }
          };
          const lightThemeFile = await this._streamservice.convertToFile('../../../assets/images/dashboard-builder/dashboard_light.png');
          const darkThemeFile = await this._streamservice.convertToFile('../../../assets/images/dashboard-builder/dashboard_dark.png');
          const logo = await this._streamservice.convertToFile('../../../assets/images/bayaan-logo-blue.png');
          this._dashboardService.createDashboard(dashboardPayload, logo, lightThemeFile, darkThemeFile, false).subscribe(resp => {
            this.threadMessages[this.currentThreadId].messages[index].dashbordId = resp.data;
            this.threadMessages[this.currentThreadId].messages[index].responseType = this.responseTypes[3].type;
            this.scrollToBottom();
          });
        },
        error: error => {
          console.log('error', error);
        }
      });
    }
  }

  generateRandomNumber(): number {
    return Math.floor((1000 + Math.random()) * 9000);
  }


  onViewAllPrompt() {
    this.isNew = true;
    this.isExpanded = false;
    this.showAllPrompt = !this.showAllPrompt;
    this.filterPrompt(this.allPromptsKey.key);
  }

  /**
   *
   * used to open and close feedback popup
   */

  feedbackOpen(event: MouseEvent, message: ThreadMessage) {
    const targetElement = event.target as HTMLElement;
    const rect = targetElement.getBoundingClientRect();
    message.top = rect.top;
    message.left = rect.left;
    message.parentWidth = targetElement.offsetWidth;
    message.showPopUp = true;
    this._cdr.detectChanges();
  }


  openDBuilder(dId: string) {
    const url = this._router.serializeUrl(this._router.createUrlTree(['/store/dashboard-builder'], { queryParams: { id: dId, mode: 'edit', tab: 'Dashboards' } }));
    window.open(url, '_blank');
  }

  filterPrompt(domain: string) {
    this.selectedDomain = domain;
    this.searchPrompt = '';
    this.promptList = domain == this.allPromptsKey.key ? this.clonedPromptList : this.clonedPromptList.filter(x => x.domain[`name_${this.selectedLanguage}`] == this.selectedDomain);
  }

  onSearchPrompt(keyword: string) {
    this.searchPrompt = keyword;
    this.searchSub.next({ type: 'prompt', searchString: this.searchPrompt });
  }

  searchPrompts() {
    this.promptList = this.clonedPromptList.filter(item => {
      return item[`prompt_${this.selectedLanguage}`].toLowerCase().includes(this.searchPrompt.toLowerCase()) && (this.selectedDomain == this.allPromptsKey.key ? true : item.domain[`name_${this.selectedLanguage}`] == this.selectedDomain);
    });
  }

  openGenAiTool() {
    this.closeModal();
    this._router.navigateByUrl('/gen-ai-testing-dashboard');
  }

  promptClick(prompt: string) {
    this.smartSuggestion = [];
    this.query = prompt;
    this.showAllPrompt = false;
    this.generateResponse(this.query);
  }

  checkResponseType(category: AiResponseCategory, message: ThreadMessage): boolean {
    if (category.type === 'text') {
      return true;
    } else if (category.type === 'chart' && message.chart_nodes?.length > 0) {
      return true;
    } else if (category.type === 'dashboard' && message.chart_nodes?.length > 1) {
      return true;
    }
    return category.type !== 'chart' && category.type !== 'dashboard';
  }

  openFeedbackModel() {
    this.feedbackModel.set(!this.feedbackModel());
  }

  scrolled(_event: any) {
    this.hideAllFeedbacks();
  }

  hideAllFeedbacks() {
    if (this.threadMessages[this.currentThreadId]?.messages?.some(x => x.showPopUp)) {
      this.threadMessages[this.currentThreadId]?.messages?.map(x => x.showPopUp = false);
    }
  }

  cancelFeedbackModel() {
    this.feedbackModel.set(false);
    this.genAiFeedback = '';
    this.ratingData = 0;
    this._cdr.detectChanges();
  }


  // feedback section //

  postChatFeedback(message: ThreadMessage, liked: string) {
    const payload = {
      feedback_type: 'Others',
      liked: liked == 'like' ? true : false,
      content: message.feedback,
      chat_id: message.id
    };
    this._streamservice.postMethodRequest(`${environment.genAiBaseUrl}/${environment.genAiChatVersion}/feedback/ai-message/`, payload).subscribe({
      next: _next => {
        message.liked = liked;
        message.showPopUp = false;
      },
      error: error => {
        this._toaster.error(error.error.message);
        message.showPopUp = false;
      }
    });
  }

  outsideClick(event: any) {
    if (!this.dislikeRef?.nativeElement?.contains(event?.target) && this.outsideRef?.nativeElement.innerText !== event?.target.innerText) {
      this.threadMessages[this.currentThreadId].messages.map(x => x.showPopUp = false);
    }

  }

  aiOutsideClick(event: any) {
    if (!this.aiRef?.nativeElement?.contains(event?.target)) {
      this.threadMessages[this.currentThreadId].messages.map(x => x.showTools = false);
    }
  }

  submitGenAiFeedback() {
    if (this.ratingData <= 3 && (!this.genAiFeedback || this.genAiFeedback == '')) {
      // this.feedBackSubmit = true;
      this._toaster.error(this._translate.instant('Please provide your feedback'));
      return;
    }
    const payload = {
      rating: this.ratingData,
      content: this.genAiFeedback
    };
    // this.feedBackSubmit = false;
    this._streamservice.postMethodRequest(`${environment.genAiBaseUrl}/${environment.genAiChatVersion}/feedback/general/`, payload).subscribe({
      next: _next => {
        this._toaster.success(this._translate.instant('Feedback submitted succesfully'));
        this.genAiFeedback = '';
        this.ratingData = 0;
        this.feedbackModel.set(false);
        this._cdr.detectChanges();
      },
      error: error => {
        this._toaster.error(error.error.message);
      }
    });
  }

  openSettings() {
    this.isChatSettingsOpen.set(true);
  }

  closeSettings() {
    this.isChatSettingsOpen.set(false);
  }

  setDataSource(dataSource: string) {
    this.selectedDataSource = dataSource;
    this.isSourceChanged = true;
    if (this.selectedDataSource == this.aiSettings.tamm) {
      this.isReasonEnabled = false;
    }
  }

  setResponseType(type: string) {
    this.responseType = type;
  }

  checkMaintanance() {
    this.ifpSlaService.checkMaintanance().subscribe(resp => {
      if (resp.maintenance_modules?.length) {
        this.maintainanceData = resp;
        this.genAiMaintanance = resp.maintenance_modules.includes(manitananceKeys.genAi);
      }
    });
  }

  onDownloadAuditReport() {
    this.subs.add(
      this._apiservice.downloadAuditReport().subscribe({
        next: (value) => {
          const matches = (value.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
          const nameValue = matches ? matches[1] : 'ai_messages_export';
          this._downloadService.downloadFiles(value.body, nameValue);
        },
        error: (error) => {
          this._toaster.error(error?.error?.message);
        }
      })
    )
  }

  checkAuditReportApproval() {
    this.auditReportAccess.set(this._apiservice.auditReportaApprovals.includes(this._msalService?.getLoginData?.account?.username))
  }




  openIndicator(link: string) {
    window.open(link, '_blank')
  }

  enableLinks(message: any) {
    message.loadMore = !message.loadMore;
  }


  ngOnDestroy(): void {
    this.gpt.removeModal();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.subs.unsubscribe();
  }

}

interface AiResponseCategory {
  type: string;
  description: string;
  icon: string;
}

