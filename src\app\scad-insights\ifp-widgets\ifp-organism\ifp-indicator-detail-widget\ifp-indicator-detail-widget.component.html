<div class="ifp-node" #fullScreen *ngIf="!isDashboardCard">
  <div class="ifp-node__card-left">
    <div class="ifp-node__left-actions" id="chartCard">
      <div class="ifp-node__group-one">
        <h2 class="ifp-node__tiltle">{{name | translate}}
          <em class="ifp-icon ifp-icon-verifyed-tick"
            *ngIf="data?.content_classification_key === classifications.officialStatistics"
            [appIfpTooltip]="indicatorType.officialStatics | translate" [extraSpaceTop]="20"></em>
          <em class="ifp-icon ifp-icon-conical-flask"
            *ngIf="data?.content_classification_key === classifications.innovativeStatistics"
            [appIfpTooltip]="indicatorType.innovativeStatics | translate" [extraSpaceTop]="20"></em>
        </h2>
      </div>
      <p class="ifp-node__subtitle" *ngIf="subtitle">{{(subtitle| quotRemove) | translate}}</p>
      <div class="ifp-node__filters" *ngIf="data?.filterPanel">
        <ng-container *ngFor="let filters of data.filterPanel.properties; let i=index">
          <app-ifp-dropdown [showTitle]="true" *ngIf="!filters.isDisabled && !isShowDropDown"
            [isMulti]="checkValue() || checkIndex() === i" class="ifp-search-filter__dropdown" [limit]="optionsLimit"
            [placeHolder]="filters.label" [dropDownItems]="filters.options" ngClass="ifp-node__filter-item"
            (dropDownItemMultiClicked)="applyFilter($event,filters.label);" [multiDefaultSelect]="true"
            [isBehaviourAutoSwitch]="true" [defaultValue]="filters.default" [chartType]="chartType"
            [path]="filters.path" [selectAllBox]="checkValue() || checkIndex() === i" [isEmitEventAll]="true"
            [searchEnable]="true" [formDisable]="true" [selectedValues]="filterKeys[i]?.value "
            [selectedValue]="filterKeys[i]?.value.toString()"
            [selectAll]="filterKeys[i]?.value?.length == filters.options?.length"></app-ifp-dropdown>
        </ng-container>
        <app-ifp-dropdown *ngIf="chartType === 'pie' && enableDateFilter" [showTitle]="true" [isMulti]="false"
          class="ifp-search-filter__dropdown" [limit]="optionsLimit" [placeHolder]="'Period Selection' | translate"
          [dropDownItems]="customTimePeriodOptions" ngClass="ifp-node__filter-item"
          (dropDownItemMultiClicked)="changePeriod($event);" [multiDefaultSelect]="true"
          [isBehaviourAutoSwitch]="true"></app-ifp-dropdown>
      </div>

      <div class="ifp-node__rating" *ngIf="ratingValues?.length > 0">
        <ng-container *ngFor="let rating of ratingValues">
          <ifp-rating *ngIf="rating.id !== chartConstants.RATINGSEPARATOR" [heading]="rating?.header_title"
            [title]="formatTitle(rating)" [value]="rating.value |  customNumber:rating.valueFormat"
            [subValueColor]="rating.value < 0 ? (invertColor ? '#1c9452' :'#FF001E') : (invertColor ?'#FF001E' :'#1c9452')"
            [subValue]="(rating.percentage)" class="ifp-node__value"
            [arrowType]="rating.value < 0 ? (rating.invertArrows ? 'up' : 'down') :  (rating.invertArrows ? 'down' : 'up')"></ifp-rating>
          <div *ngIf="rating.id === chartConstants.RATINGSEPARATOR" class="ifp-node__vertical-line"></div>
        </ng-container>
      </div>
      <div class="ifp-node__chart">
        <ng-container *ngIf="!loaderChart; else chartLoad">
          <!-- <ifp-highcharts *ngIf="isVisble" #chartRef [data]="chartData" [chartName]="'lineChart'" [chartClass]="''" [format]="format" [height]="250"></ifp-highcharts> -->
          <div>
            <app-ifp-analytic-line-chart
              *ngIf="isVisble && (chartType === 'line' || chartType === 'column' || chartType === 'bar') && chartType !==chartConstants.SUNBURST_TYPE"
              [chartData]="chartData" [isRangeSelect]="isRangeSelector" #chartComponent [yaxisLabel]="yAxisLabel"
              (rangeValueUpdated)="valueRangeUpdated($event)" (zoomOutDisabled)="disableZoomout($event)"
              [tooltipQuarterLabel]="quarterTooltipLabel" [tooltipYearOnLabel]="yearTooltipLabel"
              [chartFilterOgData]="foracstTooltip" [toolTipFormat]="toolTipFormat" [xAxisLabelType]="xAxisLabelType"
              [xAxisCatogory]="chartCatogory" [isYearly]="isYearly" [maxXaxisValue]="maxYaxisValue" [isCoi]="isCoi"
              [removeRangePrecntage]="removePercentageCalculation" [xAxisLabelUpdates]="xAxisLabelsUpdateString"></app-ifp-analytic-line-chart>
            <app-ifp-circular-bar-chart #chartComponent [chartData]="circularChartData" [chartCategory]="chartCatogory"
              *ngIf="chartType === 'circular' && isVisble" [tooltipQuarterLabel]="quarterTooltipLabel"
              [tooltipYearOnLabel]="yearTooltipLabel" [chartFilterOgData]="foracstTooltip" [yAxisLabelName]="yAxisLabel"
              [isDatalabel]="isDatalabel"></app-ifp-circular-bar-chart>
            <app-ifp-pie-chart *ngIf="isVisble && chartType === 'pie'" [pieChartSeries]="pieChartSeriesData"
              #chartComponent [chartCategory]="chartCatogory" [tooltipQuarterLabel]="quarterTooltipLabel"
              [tooltipYearOnLabel]="yearTooltipLabel" [selectedPeriod]="pieSelectedPeriod"
              [yAxisLabelName]="yAxisLabel"></app-ifp-pie-chart>
            @if (chartType ===chartConstants.SUNBURST_TYPE) {
            <app-ifp-sunburst-chart #chartComponent [chartData]="sunburstSeriesData"></app-ifp-sunburst-chart>
            }

          </div>
          <div *ngIf="chartType === 'table'" class="ifp-node__table">
            <app-ifp-table [tableData]="tableData" [isPrint]="true"></app-ifp-table>
          </div>
          <div class="ifp-analysis__selectors">
            <div class="ifp-analysis__range-selector" *ngIf="chartType === 'line' && chartData?.length">
              <div class="ifp-analysis__range-selector-label">
                {{'Range Selector' | translate}}
              </div>
              <app-ifp-toggle-button [analyticClass]="analyticsClasses.chartRangeSelector"
                (toggleChange)="getRangeSelector($event)"></app-ifp-toggle-button>
            </div>
            <app-ifp-month-selector *ngIf="data?.indicatorFilters?.length > 0 && chartType !== 'pie'"
              [filter]="data?.indicatorFilters[0]?.options" class="ifp-node__month-selector"
              (filterSelected)="applyPeriodFilter($event)"></app-ifp-month-selector>
          </div>

          <div class="ifp-node__disclaimer"
            *ngIf="((data.domain === chartConstants.LABOUR_FORCE_EN || data.domain === chartConstants.LABOUR_FORCE_AR ) && disclaimerDetails?.length > 0) || data?.thresholdValue">
            <h4 class="ifp-node__disclaimer-title">{{'Disclaimer' | translate}}</h4>
            <ul class="ifp-node__disclaimer-wrapper"
              [ngClass]="{'ifp-node__disclaimer-wrapper--bullet': data?.thresholdValue}">
              @for (disclaimer of disclaimerDetails; track disclaimer) {
              <li class="ifp-node__desc">
                {{disclaimer | translate}}
              </li>
              }

            </ul>
            <!-- <p class="ifp-node__desc" *ngFor="let disclaimer of disclaimerDetails"></p> -->
          </div>

        </ng-container>
        <ng-template #chartLoad>
          <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
        </ng-template>
      </div>
      <div class="ifp-node__txt-icon" *ngIf="data?.publication_date || data?.updated || data.data_source">
        <ifp-icon-text [icon]="'ifp-icon-calender'"
          [text]=" data?.content_classification_key === classifications.officialStatistics ? data?.updated :  getFormatedDate( data?.publication_date || data?.updated || data.data_source)"
          [key]="'Updated date'"></ifp-icon-text>
        <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" *ngIf="data.data_source"
          [text]="data.data_source" [key]="'Source'"></ifp-icon-text>
        <div class="ifp-node__meta" (click)="downloadMetaData()"
          *ngIf="data?.metaData && (data.metaData | json) !== '{}'">
          <img src="../../../../assets/images/xls.png" class="ifp-node__meta-img"> <span>{{'Metadata and methodology' |
            translate}}</span>
        </div>
      </div>
      @if (security) {
      <ifp-tag class="ifp-node__data-tag" [isBoxView]="true" [background]="'transparent'" [tagName]="security.name"
        [color]="security.color ?? ''" [infoHead]="('Data classification' | translate) + ': ' + security.name"
        [info]="security.description ?? ''"></ifp-tag>
      }
      @if (data?.footnote) {
        <ifp-indicator-disclaimer class="ifp-node__footnote" [data]="data?.footnote"></ifp-indicator-disclaimer>
      }

      @if (chartData?.length || pieChartSeriesData?.length || sunburstSeriesData.length) {
      <ifp-chart-insight [indicatorId]="id" [nodeTitle]="name" class="ifp-node__insights"></ifp-chart-insight>
      }
    </div>
    <div class="ifp-chart-toolbar"
      [ngClass]="{'ifp-chart-toolbar--disabled' : chartData?.length <=0 && pieChartSeriesData.length <= 0 && !sunburstSeriesData.length}">

      <div  *ngIf="relatedSV?.length" class="ifp-chart-toolbar__tool-item"
        [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'relatedProducts'}">
        <div class="ifp-chart-toolbar__custom-button" (click)="toolbarAction = 'relatedProducts'">
          <img [src]="toolbarAction === 'relatedProducts' ? '../../../../assets/icons/download-icons/Link.svg' : '../../../../assets/icons/download-icons/Link-b.svg'"
               alt="Related Products" class="ifp-chart-toolbar__link-icon">
        </div>
      </div>

      <div class="ifp-chart-toolbar__tool-item" [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'download'}">
        <ifp-button class="ifp-whats-new-card__btn" [buttonColor]="toolbarAction === 'download' ? 'blue' :'black'" [buttonClass]="buttonClass.icon"
          [iconClass]="'ifp-icon-download'" (ifpClick)="toolbarAction = 'download'"></ifp-button>
      </div>

      <div class="ifp-chart-toolbar__tool-item"
        [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'notification'}">
        <ifp-button class="ifp-whats-new-card__btn"
          [buttonColor]="toolbarAction === 'notification' ? 'blue' :'black'" [buttonClass]="buttonClass.icon"
          [iconClass]="'ifp-icon-notification'" (ifpClick)="toolbarAction = 'notification'"></ifp-button>
      </div>
      <div class="ifp-chart-toolbar__tool-item">
        <ifp-button [tooltipValue]="(!myAppsStatus ? 'Add to my bookmarks' : 'Remove from my bookmarks' )| translate"
          class="ifp-whats-new-card__btn" [buttonColor]="myAppsStatus ? 'blue' :'black' " [event]="myAppsStatus "
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-plus-square'" (ifpClick)="addDataMyApps($event)"
          [analyticClass]="myAppsStatus ? analyticsClasses.removeMyApps : analyticsClasses.addToMyApps "></ifp-button>
      </div>
      <div class="ifp-chart-toolbar__tool-item" *ngIf="data.enableCompare == 'true'"
        [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'compare'}">
        <ifp-button  class="ifp-whats-new-card__btn"
          [buttonColor]="toolbarAction === 'compare' && chartData?.length ? 'blue' :'black' "
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-app-settings'"
          (ifpClick)="toolbarAction = 'compare'"></ifp-button>
      </div>
      <div class="ifp-chart-toolbar__tool-item"
        [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'settings'}">
        <!-- <img src="../../../../assets/images/settings-icon.svg" (click)="toolbarAction = 'settings'" width="22"> -->
        <ifp-button  class="ifp-whats-new-card__btn"
          [buttonColor]="toolbarAction === 'settings' && chartData?.length ? 'blue' :'black' "
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-settings'"
          (ifpClick)="toolbarAction = 'settings'"></ifp-button>
      </div>

      <div class="ifp-chart-toolbar__tool-item">
        <!-- <img src="../../../../assets/images/save.svg" (click)="print()" width="22"> -->
        <ifp-button class="ifp-whats-new-card__btn" [buttonColor]="'black'"
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-print'" (ifpClick)="print()"></ifp-button>
      </div>

      <div class="ifp-chart-toolbar__tool-item"
        [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'comment'}">
        <ifp-button  class="ifp-whats-new-card__btn"
          [buttonColor]="toolbarAction === 'comment' ? 'blue' :'black' " [buttonClass]="buttonClass.icon"
          [iconClass]="'ifp-icon-comment'" (ifpClick)="toolbarAction = 'comment'"></ifp-button>
      </div>

      <div class="ifp-chart-toolbar__tool-item">
        <ifp-button  class="ifp-whats-new-card__btn" [buttonColor]="'black'"
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-fullscreen'" (ifpClick)="fullscreen()"></ifp-button>
      </div>

      <div class="ifp-chart-toolbar__tool-item" *ngIf="chartType === 'line' || chartType === 'column' ">
        <ifp-button  class="ifp-whats-new-card__btn" [buttonColor]="'black'"
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-zoom-in'" (ifpClick)="zoom('in')"></ifp-button>
      </div>
      <div class="ifp-chart-toolbar__tool-item" *ngIf="chartType === 'line' || chartType === 'column' ">
        <ifp-button  class="ifp-whats-new-card__btn" [buttonColor]="'black'"
          [buttonClass]="(!isZoomoutDisabled ? buttonClass.disabled : '') +' '+ buttonClass.icon"
          [iconClass]="'ifp-icon-zoom-out'" (ifpClick)="zoom('out')"></ifp-button>
      </div>
    </div>
  </div>
  <div class="ifp-node__card-right">
    <!-- <span class="">
      <svg xmlns="http://www.w3.org/2000/svg" width="9.689" height="11.305" viewBox="0 0 9.689 11.305">
        <path id="Polygon_103" data-name="Polygon 103" d="M5.652,0,11.3,9.689H0Z" transform="translate(9.689) rotate(90)"/>
      </svg>
    </span> -->

    <div class="ifp-chart-toolbar__action-box"
      *ngIf="chartData?.length || pieChartSeriesData?.length || sunburstSeriesData.length">
      <ng-container *ngIf="toolbarAction === 'settings'">
        <div class="ifp-chart-toolbar__header-wrapper">
        <h4 class="ifp-chart-toolbar__right-title ifp-chart-toolbar__right-title--bold">{{'Settings' | translate}}</h4>
        </div>
        <div class="ifp-chart-toolbar__switch-icons">
          <app-ifp-tab [tabData]="tabData" [isSmall]="true" (selectedTabEvent)="changeChart($event.event.name)"
            [selectedTab]="chartType" [selectionType]="'name'" class="ifp-chart-toolbar__switch-view"></app-ifp-tab>
        </div>

        <ng-container
          *ngIf="chartType === 'line' || chartType === 'column' || chartType === 'bar' || chartType === 'circular' || chartType === 'pie' || chartType === chartConstants.SUNBURST_TYPE">
          <div class="ifp-chart-toolbar__checkbox-outer">
            <input type="checkbox" [checked]="isToolTip" id="tooltip" class="ifp-chart-toolbar__checkbox"
              (change)="changeTooltip($event)">
            <label for="tooltip" class="ifp-chart-toolbar__checkbox-label">{{'Tooltip' | translate}}</label>
          </div>
          <div class="ifp-chart-toolbar__checkbox-outer" *ngIf=" chartType !== 'pie'">
            <input type="checkbox" id="dataLabels" (change)="changeDataLabel($event)" checked="true"
              class="ifp-chart-toolbar__checkbox">
            <label for="dataLabels" class="ifp-chart-toolbar__checkbox-label">{{'Data Labels' | translate}}</label>
          </div>
          <div class="ifp-chart-toolbar__checkbox-outer"
            *ngIf=" chartType !== 'pie' && chartType !== chartConstants.SUNBURST_TYPE">
            <input type="checkbox" id="priceValue" [checked]="isPreciseValue" (change)="changePreciseLabel($event)"
              class="ifp-chart-toolbar__checkbox">
            <label for="priceValue" class="ifp-chart-toolbar__checkbox-label">{{'Precise Value' | translate}}</label>
          </div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="toolbarAction === 'comment'">
        <div class="ifp-chart-toolbar__header-wrapper">
        <h4 class="ifp-chart-toolbar__right-title ifp-chart-toolbar__right-title--bold">{{'My Comments' | translate}}
        </h4>
        </div>
        <p class="ifp-chart-toolbar__right-desc">{{'Comments are only visible to you' | translate}}</p>
        <app-ifp-comments [insightData]="insightData" (addComment)="addInsight($event)"
          (editComment)="updateInsight($event)" (deleteComment)="deleteInsight($event)"></app-ifp-comments>
      </ng-container>
      <ng-container *ngIf="toolbarAction === 'notification'">
        <!-- <h4 class="ifp-chart-toolbar__right-title ifp-chart-toolbar__right-title--bold">{{'Notifications' | translate}}</h4>
        <p class="ifp-chart-toolbar__right-desc">Manage your notifications settings</p>
        <div class="ifp-chart-toolbar__switch-sec">
          <app-ifp-toggle-button (toggleChange)="addNotification($event)" class="ifp-chart-toolbar__switch" [enable]="isNotificationEnabled"></app-ifp-toggle-button>
          <p class="ifp-chart-toolbar__label">Enable notification</p>
        </div>
        <div class="ifp-chart-toolbar__switch-sec">
          <app-ifp-toggle-button (toggleChange)="setEmailNotifStatus($event)" class="ifp-chart-toolbar__switch" [enable]="isEmailEnabled"></app-ifp-toggle-button>
          <p class="ifp-chart-toolbar__label">Receive notifications via e-mail</p>
        </div> -->
        <app-ifp-notification-settings [options]="notificationOptions" [id]="data.id"></app-ifp-notification-settings>
      </ng-container>
      <ng-container *ngIf="toolbarAction === 'compare'">
        <app-ifp-compare-list [benchMarks]="benchMark" (compare)="selectedCompare($event)"
          (setReset)="setReset($event)"></app-ifp-compare-list>
      </ng-container>
      <ng-container *ngIf="toolbarAction === 'download'">
        <div class="ifp-chart-toolbar__header-wrapper">
          <h4 class="ifp-chart-toolbar__right-title">{{'Download' | translate}}</h4>
        </div>
        <div class="ifp-chart-toolbar__download-wrapper" [ngClass]="{'disabled': !tncState}">
          <div class="ifp-chart-toolbar__download-item" (click)="download('pdf')">
            <img src="../../../../assets/images/pdf-icon.svg" alt="PDF" class="ifp-chart-toolbar__download-icon">
          </div>
          <div class="ifp-chart-toolbar__download-item" (click)="download('XL')">
            <img src="../../../../assets/images/xls-icon.svg" alt="XLS" class="ifp-chart-toolbar__download-icon">
          </div>
          <div class="ifp-chart-toolbar__download-item" (click)="download('png')">
            <img src="../../../../assets/images/img-icon.svg" alt="PNG" class="ifp-chart-toolbar__download-icon">
          </div>
          @if (data?.filterPanel?.properties?.length > 0) {
          <div class="ifp-chart-toolbar__download-item" (click)="downloadCustomXl()">
            <img src="../../../../../assets/images/filter-excel.svg" class="ifp-chart-toolbar__download-icon">
          </div>
          }
        </div>
        <div class="ifp-chart-toolbar__terms">
          <app-ifp-check-box class="ifp-chart-toolbar__tnc-checkbox" [hideLabel]="true" [checkedData]="tncState"
            (checked)="termsResponse($event)" [disabled]="tncState"></app-ifp-check-box>
          <p class="ifp-chart-toolbar__tnc-text">{{'I accept the ' | translate}} <span class="ifp-link"
              (click)="showTnC()">{{'terms and conditions ' | translate}}</span> {{'for downloading the documents' |
            translate}}</p>
        </div>
      </ng-container>
      <ng-container *ngIf="toolbarAction === 'relatedProducts'">
        <div class="ifp-chart-toolbar__related-products">
          <div class="ifp-chart-toolbar__header-wrapper">
            <h4 class="ifp-chart-toolbar__right-title ifp-chart-toolbar__right-title--bold">{{'Related SVs' | translate}}</h4>
          </div>

          <!-- <div class="ifp-chart-toolbar__search-container">
            <input type="text"
                   class="ifp-chart-toolbar__search-input"
                   placeholder="Search Products"
                   [(ngModel)]="relatedProductsSearchTerm"
                   (input)="onRelatedProductsSearch()">
            <button class="ifp-chart-toolbar__search-button">
              <em class="ifp-icon ifp-icon-search"></em>
            </button>
          </div> -->

          <div class="ifp-chart-toolbar__products-list">
            <div class="ifp-chart-toolbar__product-card" [appIfpTooltip]="product.title || ''"
            *ngFor="let product of filteredRelatedProducts"
                 (click)="selectRelatedProduct(product)">
              <em class="ifp-icon ifp-icon-verifyed-tick ifp-chart-toolbar__product-tick"></em>
              <span class="ifp-chart-toolbar__product-text">
                <!-- {{product.tittle || product.title || product.name || product.label || 'Product ' + (product.id || 'Unknown')}} -->
                {{product.title}}
              </span>
              <em class="ifp-icon ifp-icon-right-arrow ifp-chart-toolbar__product-arrow"></em>
            </div>

            <div class="ifp-chart-toolbar__no-results" *ngIf="filteredRelatedProducts.length === 0">
              <span>{{'No products found' | translate}}</span>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<app-ifp-modal #modalSla [modalClass]="'ifp-modal__pdf-table'">
  <div class="ifp-scenario__download-wrapper">
    <div class="lds-spinner">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>

    <div #downloadPrint>
      <app-ifp-pdf-template>
        <div class="ifp-node__left-actions" id="chartCard">
          <div class="ifp-node__group-one">
            <h2 class="ifp-node__tiltle">{{name | firstLetter}}
              <em class="ifp-icon ifp-icon-verifyed-tick"
                *ngIf="data?.content_classification_key === classifications.officialStatistics"></em>
              <em class="ifp-icon ifp-icon-conical-flask"
                *ngIf="data?.content_classification_key === classifications.innovativeStatistics"></em>
            </h2>
          </div>
          <p *ngIf="subtitle" class="ifp-node__subtitle">{{subtitle | firstLetter}}</p>
          <div class="ifp-node__filters" *ngIf="data?.filterPanel">
            <app-ifp-dropdown *ngFor="let filters of data.filterPanel.properties; let i=index"
              [isMulti]="checkValue() || checkIndex() === i" class="ifp-search-filter__dropdown" [limit]="optionsLimit"
              [placeHolder]="filters.label" [dropDownItems]="filters.options" ngClass="ifp-node__filter-item"
              (dropDownItemMultiClicked)="applyFilter($event,filters.label)" [multiDefaultSelect]="true"
              [showTitle]="true" [isDownloadDropdownHide]="true"></app-ifp-dropdown>
            <app-ifp-dropdown *ngIf="chartType === 'pie'" [showTitle]="true" [isMulti]="false"
              class="ifp-search-filter__dropdown" [limit]="optionsLimit" [placeHolder]="'Date Period' | translate"
              [dropDownItems]="customTimePeriodOptions" ngClass="ifp-node__filter-item"
              (dropDownItemMultiClicked)="changePeriod($event);" [multiDefaultSelect]="true"
              [isBehaviourAutoSwitch]="true"></app-ifp-dropdown>
          </div>
          <div class="ifp-node__rating" *ngIf="ratingValues?.length > 0">
            <ng-container *ngFor="let rating of ratingValues">
              <ifp-rating *ngIf="rating.id !== chartConstants.RATINGSEPARATOR" [heading]="rating?.header_title"
                [title]="formatTitle(rating)" [value]="rating.value |  customNumber:rating.valueFormat"
                [subValueColor]="rating.value < 0 ? (invertColor ? '#1c9452' :'#FF001E') : (invertColor ?'#FF001E' :'#1c9452')"
                [subValue]="(rating.percentage)" class="ifp-node__value"
                [arrowType]="rating.value < 0 ? (rating.invertArrows ? 'up' : 'down') :  (rating.invertArrows ? 'down' : 'up')"></ifp-rating>
              <div *ngIf="rating.id === chartConstants.RATINGSEPARATOR" class="ifp-node__vertical-line"></div>
            </ng-container>
          </div>
          <div class="ifp-node__chart">
            <ng-container *ngIf="!loaderChart; else chartLoad">
              <!-- <ifp-highcharts *ngIf="isVisble" #chartRef [data]="chartData" [chartName]="'lineChart'" [chartClass]="''" [format]="format" [height]="250"></ifp-highcharts> -->
              @if (chartType !== 'table') {
              <div class="ifp-node__chart-template">
                <app-ifp-analytic-line-chart
                  *ngIf="isVisble && (chartType === 'line' || chartType === 'column' || chartType === 'bar') && chartType !==chartConstants.SUNBURST_TYPE"
                  [chartData]="chartData" [isRangeSelect]="isRangeSelector" #chartComponent [yaxisLabel]="yAxisLabel"
                  (rangeValueUpdated)="valueRangeUpdated($event)" (zoomOutDisabled)="disableZoomout($event)"
                  [tooltipQuarterLabel]="quarterTooltipLabel" [tooltipYearOnLabel]="yearTooltipLabel"
                  [chartFilterOgData]="foracstTooltip" [toolTipFormat]="toolTipFormat" [xAxisLabelType]="xAxisLabelType"
                  [xAxisCatogory]="chartCatogory" [isYearly]="isYearly" [maxXaxisValue]="maxYaxisValue"
                  [isPrint]="true"></app-ifp-analytic-line-chart>
                <app-ifp-circular-bar-chart #chartComponent [chartData]="circularChartData"
                  [chartCategory]="chartCatogory" *ngIf="chartType === 'circular' && isVisble"
                  [tooltipQuarterLabel]="quarterTooltipLabel" [tooltipYearOnLabel]="yearTooltipLabel"
                  [chartFilterOgData]="foracstTooltip" [yAxisLabelName]="yAxisLabel"
                  [isDatalabel]="isDatalabel"></app-ifp-circular-bar-chart>
                <app-ifp-pie-chart *ngIf="isVisble && chartType === 'pie'" [pieChartSeries]="pieChartSeriesData"
                  #chartComponent [chartCategory]="chartCatogory" [tooltipQuarterLabel]="quarterTooltipLabel"
                  [tooltipYearOnLabel]="yearTooltipLabel" [selectedPeriod]="pieSelectedPeriod"
                  [yAxisLabelName]="yAxisLabel"></app-ifp-pie-chart>
                @if (chartType ===chartConstants.SUNBURST_TYPE) {
                <app-ifp-sunburst-chart #chartComponent [chartData]="sunburstSeriesData"></app-ifp-sunburst-chart>
                }
              </div>
              }

              <div *ngIf="chartType === 'table'" class="ifp-node__table">
                <app-ifp-table [tableData]="tableData"></app-ifp-table>
              </div>
            </ng-container>
            <ng-template #chartLoad>
              <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
            </ng-template>
          </div>
        </div>
      </app-ifp-pdf-template>
    </div>
  </div>
</app-ifp-modal>

<app-ifp-modal #tncModal [modalClass]="'ifp-modal__template-certificate'">
  <app-ifp-tnc-modal (termsResponse)="termsResponse($event)" [isAccepted]="tncState"
    [isLanguage]="false"></app-ifp-tnc-modal>
</app-ifp-modal>






<!-- ## for dashboard ##  -->
@if (isDashboardCard) {
<div class="ifp-node__chart">
  <ng-container *ngIf="!loaderChart; else chartLoad">
    <!-- <ifp-highcharts *ngIf="isVisble" #chartRef [data]="chartData" [chartName]="'lineChart'" [chartClass]="''" [format]="format" [height]="250"></ifp-highcharts> -->
    <div>
      <app-ifp-analytic-line-chart
        *ngIf="isVisble && (chartType === 'line' || chartType === 'column' || chartType === 'bar') && chartType !==chartConstants.SUNBURST_TYPE"
        [chartData]="chartData" [isRangeSelect]="isRangeSelector" #chartComponent [yaxisLabel]="yAxisLabel"
        (rangeValueUpdated)="valueRangeUpdated($event)" (zoomOutDisabled)="disableZoomout($event)"
        [tooltipQuarterLabel]="quarterTooltipLabel" [tooltipYearOnLabel]="yearTooltipLabel"
        [chartFilterOgData]="foracstTooltip" [toolTipFormat]="toolTipFormat" [xAxisLabelType]="xAxisLabelType"
        [xAxisCatogory]="chartCatogory" [isYearly]="isYearly" [maxXaxisValue]="maxYaxisValue" [height]="300"
        [isDashboardCard]="true"></app-ifp-analytic-line-chart>
      <app-ifp-circular-bar-chart #chartComponent [chartData]="circularChartData" [chartCategory]="chartCatogory"
        *ngIf="chartType === 'circular' && isVisble" [tooltipQuarterLabel]="quarterTooltipLabel"
        [tooltipYearOnLabel]="yearTooltipLabel" [chartFilterOgData]="foracstTooltip" [yAxisLabelName]="yAxisLabel"
        [height]="300" [isDashboardCard]="true" [isDatalabel]="isDatalabel"></app-ifp-circular-bar-chart>
      <app-ifp-pie-chart *ngIf="isVisble && (chartType === 'pie' || chartType == 'doughnut')"
        [pieChartSeries]="pieChartSeriesData" #chartComponent [chartCategory]="chartCatogory"
        [tooltipQuarterLabel]="quarterTooltipLabel" [tooltipYearOnLabel]="yearTooltipLabel"
        [selectedPeriod]="pieSelectedPeriod" [yAxisLabelName]="yAxisLabel" [height]="300"
        [isDonut]="chartType == 'doughnut' ? true: false" [isDashboardCard]="true"></app-ifp-pie-chart>
      @if (chartType ===chartConstants.SUNBURST_TYPE) {
      <app-ifp-sunburst-chart [height]="300" #chartComponent [chartData]="sunburstSeriesData"
        [isDashboardCard]="true"></app-ifp-sunburst-chart>
      }
      @if (chartType === 'table') {
      <div class="ifp-node__table">
        <app-ifp-table [tableData]="tableData"></app-ifp-table>
      </div>
      }
    </div>
  </ng-container>
  <ng-template #chartLoad>
    <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
  </ng-template>
</div>
}


<app-ifp-modal #filterModal [modalClass]="'ifp-modal__template-certificate'">
  <ifp-ifp-custom-xl-filter [allChartsData]="data" [isApi]="false" [filters]="data.filterPanel"
    class="ifp-modal__template-certificate" (dismissModel)="closeFilterModel($event)" [timeUnit]="timeUnit"
    [tableFields]="data.tableFields"></ifp-ifp-custom-xl-filter>
</app-ifp-modal>
