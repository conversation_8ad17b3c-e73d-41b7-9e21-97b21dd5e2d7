.ifp-filters-container {
  width: 50%;
  z-index: 4;
  position: absolute;
  right: 0px;
  top: 60px;
}

.ifp-filters-container .filters-menu {
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: center;
  background-color: #ffffff99;
  border-radius: 10px 0px 0px 10px;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  visibility: visible;
}

.filters-menu.hidden {
  transform: translateX(100%);
  opacity: 0;
  pointer-events: none;
}

.filters-menu.visible {
  transform: translateX(0);
  opacity: 1;
  pointer-events: auto;
}

.open-filters-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  right: 0px;

  .ifp-icon {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
  }

  &--rotate {
    .ifp-icon {
      transform: rotate(180deg);
    }
  }

  &:hover {
    transform: translateY(-1px);
  }
}

.open-filters-icon.visible {
  opacity: 1;
  visibility: visible;
}

.open-filters-icon.hidden {
  opacity: 0;
  visibility: hidden;
}

.select-container {
  flex: 1;
  position: relative;
  height: 33px;
}

.filter-icons {
  color: #000;
  height: 33px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }
}

.filter-icons:first-child {
  border-right: 1px solid #FFF;
  height: 33px;
}

.border-revearse:first-child {
  border-right: none;
}

.border-revearse:last-child {
  border-right: 1px solid #FFF;
}

.ifp-filters-container .open-filters-icon {
  width: 20px;
  padding: 7px 3px 7px 7px;
  border-radius: 10px 0px 0px 10px;
  border-right: 1px solid #FFF;
  color: #000;
  cursor: pointer;
  box-sizing: content-box;
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.9);
}

.filters-container .selection-level {
  font-size: 1.4rem;
  width: 140px;
  font-weight: bold;
  position: absolute;
  top: 31px;
  left: 15px;
  color: #000;
  z-index: 3;
}

.filter-icon-left {
  .ifp-icon {
    transform: rotate(180deg);
  }
}

.loading-indicator {
  padding: 12px;
  text-align: center;
  font-size: 1.2rem;
  color: #FFF;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .ifp-filters-container {
    width: 70%;
  }
  
  .filters-menu.visible {
    width: 100%;
  }
}
