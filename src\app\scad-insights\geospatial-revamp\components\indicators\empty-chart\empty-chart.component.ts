import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  OnChanges,
  OnDestroy,
  OnInit,
  Input,
  SimpleChanges,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';


@Component({
  selector: 'ifp-empty-chart', 
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './empty-chart.component.html',
  styleUrl: './empty-chart.component.scss'
})
export class EmptyChartComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

  @Input() isCustomize: boolean = false;

  ngOnChanges(changes: SimpleChanges) {

  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }

}

