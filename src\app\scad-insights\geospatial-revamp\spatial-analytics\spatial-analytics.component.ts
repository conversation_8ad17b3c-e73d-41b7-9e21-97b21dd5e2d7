import { AfterViewInit, Component, OnD<PERSON>roy, OnInit, ElementRef, ViewChild, ChangeDetectorRef, HostListener } from '@angular/core';
import { IfpSectionComponent } from "../../ifp-widgets/ifp-molecules/ifp-section/ifp-section.component";
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { CommonService } from '../common.service';
import { CensusServiceService } from '../census-service.service';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { geoMapKeys } from '../geospatial.contants';
import { slaService } from '../../core/services/sla/sla.service';

@Component({
  selector: 'ifp-spatial-analytics',
  standalone: true,
  imports: [
    IfpSectionComponent,
    CommonModule,
    RouterLink,
    RouterLinkActive,
    TranslateModule
  ],
  templateUrl: './spatial-analytics.component.html',
  styleUrl: './spatial-analytics.component.scss'
})
export class SpatialAnalyticsComponent implements OnInit, AfterViewInit, OnDestroy {

  private subscription = new Subscription();
  @ViewChild('geospatialRevampMap') private mapDiv!: ElementRef;
  @ViewChild('cercleIcon') private cercleIcon!: ElementRef;
  @ViewChild('arrowIcon') private arrowIcon!: ElementRef;

  private view: any;

  public selectedItem: number | null = 1;
  public domains: any = geoMapKeys.domains;
  public userAccessPermission: any = {};
  public panelItems: string[] = ['', 'Census', ''];
  public loading: boolean = true;
  public summaryData: any[] = [];

  public lastQuarter: string = '';
  public lastYear: number = 0;
  public lastQuarterData: any;
  public screenWidth: number = window.innerWidth;
  public hoveredIconIndex: number | null = null;
  public language: string = 'en';
  public theme: string = 'light';
  public mapUnderMaintenanace: boolean = false;
  public cmsMapUnderMaintenanaceKey: string = 'geo_spatial_map';


  constructor(
    private commonService: CommonService,
    private gisSharedService: CensusServiceService,
    private cdr: ChangeDetectorRef,
    public themeService: ThemeService,
    private _slaService: slaService
  ) { }

  ngOnInit(): void {
    this.loadDomainsPermission();
    this.loadFiltersData();
    this.themeService.defaultLang$.subscribe((lang: string) => {
      this.language = lang;
    });
    this.themeService.defaultTheme$.subscribe((theme: any) => {
      this.theme = theme;
    });
    this._slaService.checkMaintanance().subscribe((data: any) => {
      if (data && data.maintenance_mode) {
        this.mapUnderMaintenanace = data.maintenance_modules[0] == this.cmsMapUnderMaintenanaceKey;
      }
    })
  }

  private loadFiltersData() {
    const sub = this.commonService.getAllFilters()
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe({
        next: (data) => {
          this.lastQuarterData = this.commonService.getLastQuarterAndYear(data.quarters);
          this.lastYear = this.lastQuarterData.YEAR_CODE;
          // force Q3 as the base Quarters
          this.lastQuarter = this.lastQuarterData.QUARTER_CODE == 'Q4' ? 'Q3' : this.lastQuarterData.QUARTER_CODE;

          this.loadSummary();
        },
        error: (error) => {
          console.error('Error loading filters data:', error);
        }
      });
    this.subscription.add(sub);
  }

  private loadDomainsPermission() {
    this.loading = true;
    const sub = this.commonService.getDomainsPermissions()
      .pipe(finalize(() => { this.loading = false; }))
      .subscribe({
        next: (data: any) => {
          this.userAccessPermission = data || {};
          this.gisSharedService.setGeoMapAccessPermission(this.userAccessPermission);
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading permissions:', error);
          this.userAccessPermission = {};
        }
      });
    this.subscription.add(sub);
  }

  private loadSummary() {
    this.loading = true;
    let defaultQueryParams = geoMapKeys.defaultQueryParams;
    defaultQueryParams.YEAR_CODE = [this.lastYear.toString()];
    defaultQueryParams.QUARTER_CODE = [this.lastQuarter];

    const sub = this.commonService.getSummary(defaultQueryParams)
      .pipe(finalize(() => { this.loading = false; }))
      .subscribe({
        next: (data: any) => {
          console.log("summaryData", data.summary);
          this.summaryData = data.summary || [];
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading summary:', error);
          this.summaryData = [];
        }
      });

    this.subscription.add(sub);
  }

  ngAfterViewInit(): void {
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  selectItem(index: number): void {
    if (this.panelItems[index]) {
      this.selectedItem = index;
    }
  }

  selectedIndex: number | null = null;

  selectSection(index: number) {
    this.selectedIndex = index;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.screenWidth = event.target.innerWidth;
  }

  calculateLeftPosition(i: number): string {
    const basePercentage = 5;
    const maxStep = this.screenWidth < 1200 ? 25 : 20; // Different steps based on screen size
    return (basePercentage + i * maxStep) + '%';
  }

  onHover(index: number, isHovered: boolean) {
    this.hoveredIconIndex = isHovered ? index : null;
  }


  getFilteredData(): any[] {
    if (!this.summaryData || !Array.isArray(this.summaryData) || !this.userAccessPermission) {
      return [];
    }

    return this.summaryData
      .filter(item => {
        switch (Number(item.DOMAIN_ID)) {
          case 1:
            return this.userAccessPermission.populationAccess === true;
          case 2:
            return this.userAccessPermission.labourForceAccess === true;
          case 3:
            return this.userAccessPermission.realEstateAccess === true;
          default:
            return false;
        }
      })
      .sort((a: any, b: any) => {
        const domainA = Number(a.DOMAIN_ID) || 0;
        const domainB = Number(b.DOMAIN_ID) || 0;
        return domainA - domainB;
      });
  }



  hasAccessToDomain(domainId: number): boolean {
    if (!this.userAccessPermission) return false;

    switch (domainId) {
      case 1:
        return this.userAccessPermission.populationAccess === true;
      case 2:
        return this.userAccessPermission.labourForceAccess === true;
      case 3:
        return this.userAccessPermission.realEstateAccess === true;
      default:
        return false;
    }
  }

  formatNumber(num: number): string {
    if (num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0';
    }
  }
}