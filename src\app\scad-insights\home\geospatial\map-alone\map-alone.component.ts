import {AfterViewInit, Component, OnDestroy, OnInit, Input, OnChanges, SimpleChanges} from '@angular/core';
import WebMap from "@arcgis/core/WebMap";
import MapView from "@arcgis/core/views/MapView";
import {SharedService} from "../../geo-services/shared.service";
import {MapServiceService} from "../map-service.service";
import {ThemeService} from "../../../core/services/theme/theme.service";
import {SubSink} from "subsink";
import {OfficialService} from "../official.service";
import Expand from "@arcgis/core/widgets/Expand";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-map-alone',
  templateUrl: './map-alone.component.html',
  styleUrls: ['./map-alone.component.scss'],
  standalone: true
})
export class MapAloneComponent implements AfterViewInit, OnInit, On<PERSON>estroy, OnChanges {

  subsink: SubSink = new SubSink();
  _view: any;
  @Input() getCMSObject: any;
  esriToken: any;

  private lightBasemap = {
    portalItem: {
      id: '358ec1e175ea41c3bf5c68f0da11ae2b'
    }
  };

  private darkBasemap = {
    portalItem: {
      id: 'c50de463235e4161b206d000587af18b'
    }
  };

  constructor(
    private mapViewService: MapServiceService,
    private sharedService: SharedService,
    private themeService: ThemeService,
    private officialService: OfficialService,
    private _translate: TranslateService
  ) {
  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {
    this.subsink.add(
      this.sharedService.generateTokenByBackend().subscribe((data: any) => {
        this.esriToken = data.access_token;
      })
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    //@ts-ignore
    const cmsObject = changes.getCMSObject;
    if (cmsObject && cmsObject.currentValue) {
      this.createMap(cmsObject.currentValue[0]);
    }
  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
  }

  async createMap(cmsObject: any) {
    const layersToAuthenticate: any = [];
    cmsObject.modules.forEach((module: any) => {
      if(module.id === 4654) {
        this.mapViewService.dotDensityModule = module;
      }
      module.layers.forEach((layer: any) => {
        layersToAuthenticate.push(layer.endpoint);
      })
    });
    let otherLayersToAuth: any = [];
    cmsObject.commonLayers.map((obj: any) => {
      otherLayersToAuth.push(obj.endpoint);
    });
    const hiddenNames = ["Hospitals", "Clinics", "Sports Club"];
    const filteredNames = otherLayersToAuth.filter((name: any) => !hiddenNames.includes(name));
    this.mapViewService.setupEsriConfig(layersToAuthenticate, this.esriToken);
    this.mapViewService.setupEsriConfig(filteredNames, this.esriToken);
    this.lightBasemap.portalItem.id = cmsObject.baseMapLightPortal;
    this.darkBasemap.portalItem.id = cmsObject.baseMapDarkPortal;
    const initialWebMap = new WebMap({
      basemap: this.lightBasemap
    });
    this._view = new MapView({
      map: initialWebMap,
      container: "mapAlone",
      center: [54.110943, 24.416687],
      // center: [54.410943, 24.476687],
      // center: [54.410943, 24.476687],
      zoom: 7,
      // @ts-ignore
      animate: true,
      ui: {
        components: [""]
      }
    });

    this.subsink.add(
      this.themeService.defaultTheme$.subscribe((theme: any) => {
        if(theme === 'light') {
          this._view.map.basemap = this.darkBasemap;
        } else {
          this._view.map.basemap = this.lightBasemap;
        }
      })
    );

    await this._view.when(() => {
      this.mapViewService.setupClickHandler(this._view, (feature: any) => {
        this.handleFeatureClick(feature);
      });
    });
    this.mapViewService.view = this._view;
    this.mapViewService.createFeatureLayerToMapDistricts('https://arcgisdev.scad.gov.ae/server/rest/services/IFP2/MapInsights_population_Static_20230831_official_Dev/MapServer/6', 1, 'Districts', true);
    this.addingDotDensityLayersToTheMap();

    // adding widget to the map
    this.mapViewService.createMaximizeWidget('top-right');
    this.mapViewService.createGroupLayers(cmsObject.commonLayers);
    this.createCustomLayerlistWidget(this._view.map, 'bottom-right');
    // this.mapViewService.createSearchWidget('top-left');
    this.mapViewService.createBookmarkWidget('top-left');
    this.mapViewService.createMetadataWidget('top-left');
    this.mapViewService.createMeasurementWidget('top-right', 'top-left');
    this.mapViewService.createBaseMapGalleryWidget('top-left');
    this.mapViewService.createZoomWidget('bottom-left');
  }
  addingDotDensityLayersToTheMap() {
    const layers = this.mapViewService.dotDensityModule.layers;
    layers.forEach((layer: any) => {
      if(layer.name === 'Dot Density') {
        this.mapViewService.createDotdensityFeatureLayer(layer.endpoint, layer.name);
        // this.mapViewService.createChoroplethFeatureLayer(layer.endpoint);
      }
    });
  }

  handleFeatureClick(feature: any) {
    const districtIdFromClick = feature.attributes.DISTRICT_ID;
    let districtName = '';
    let whereClause = '';
    if(feature.geometry != null || feature.geometry != undefined) {
      districtName = feature.attributes.DISTRICTNAMEENG;
      whereClause = "DISTRICT_ID = "+districtIdFromClick+"";
      // this.mapViewService.drawPolygonToMap(feature.geometry.rings[0]);
      this.mapViewService.drawPolygonToMapFromDistrictId(districtIdFromClick);
    } else {
      districtName = "Abu Dhabi";
      whereClause = "1=1";
      this.mapViewService.view.graphics.removeAll();
    }

    this.officialService.createOfficialFiguresByGender(whereClause).then((resutls: any) => {
      const figures = resutls.features[0].attributes;
      this.officialService.setPopByGender(figures);
    });
    this.officialService.createOfficialFigures(whereClause).then((resutls: any) => {
      const figures = resutls.features[0].attributes;
      figures.districtName = districtName;
      this.officialService.setPopByNationals(figures);
    });
  }

  createCustomLayerlistWidget(map: any, position: any) {
    var customLayerDiv = document.createElement('div');
    // @ts-ignore
    customLayerDiv.appendChild(document.getElementById('customContent'));
    // customLayerDiv.appendChild(titleDiv);
    // customLayerDiv.appendChild(nationalityDiv);
    customLayerDiv.id = "customLayersContainer";
    const myGroupLayers = this.mapViewService.getGrouplayers(map);
    let iconUrl = '';
    let allGroupLayers = myGroupLayers[0].layers._items;
    allGroupLayers.forEach((layer: any) => {
      if(layer.type === "feature" && layer.renderer && layer.renderer.symbol) {
        let symbol = layer.renderer.symbol;
        if(symbol.type === "picture-marker") {
          iconUrl = symbol.url;
        }
      }
      const div = document.createElement('div');
      div.className = 'checkbox-container';
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = layer.visible;

      const label = document.createElement('label');
      label.innerHTML = layer.title;
      label.style.flex = "2";

      let img = document.createElement('img');
      img.src = iconUrl;
      img.alt = 'Thumbnail';
      img.width = 15;

      checkbox.addEventListener('change', (event) => {
        layer.visible = checkbox.checked;
      });

      div.appendChild(img);
      div.appendChild(label);
      div.appendChild(checkbox);
      customLayerDiv.appendChild(div);
    });
    const layerListExpand = new Expand({
      view: this._view,
      content: customLayerDiv,
      expanded: false,
      expandIconClass: 'bi bi-layers fs-5',
      // expandIcon: 'bi bi-layers fs-5',
      expandTooltip: this._translate.instant('Layer list')
    });
    this._view.ui.add(layerListExpand, {
      position: position
    });
  }

  filterNationality() {
    this.mapViewService.filterOnLayer('Official Population Overview');
  }

}
