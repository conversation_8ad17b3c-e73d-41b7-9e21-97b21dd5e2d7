<div class="hierarchical-multiselect" [ngClass]="{'open': isMenuOpen}">

  <!-- Dropdown trigger -->
  <div class="select-trigger" (click)="toggleMenu()">
    <span>Filters</span>
    <span> <i class="arrow-down"></i> </span>  
  </div>

  <!-- Dropdown menu -->
  <div class="options-container" *ngIf="isMenuOpen">

    <div class="accordion-list">

      <!-- Filter accordions --> 
      <div class="filter-accordion">
        <div>

          <!-- Filters  -->
          <div class="filter-header" (click)="toggleFilter(1)">
            <div class="filter-title">
              <span class="filter-name"> Gender </span>
            </div>
            <i class="expand-icon" [class.expanded]="expandedFilters.includes(1)">▼</i>
          </div>
       

          <!-- Filters Options --> 
          <div class="filters-container" *ngIf="expandedFilters.includes(1)">
            <label class="filter-item">Male</label>
            <label class="filter-item">Female</label>
          </div>

        </div>
      </div>
    </div>
  </div> 
</div>