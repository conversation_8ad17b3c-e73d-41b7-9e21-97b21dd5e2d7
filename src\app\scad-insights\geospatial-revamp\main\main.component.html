<div class="geospatial-revamp-container">
  @if (breadCrumb && breadCrumb.length && selectedMapButton !== geoMapKeys.customize) {
  <geo-breadcrumbs (resetDomainFilter)="resetDomainFilter($event)" [pageData]="breadCrumb"></geo-breadcrumbs>
  }

  <div class="ifp-geospatial__btn-wrapper"  [ngClass]="{'ifp-geospatial__btn-wrapper__ar': defaultLang === 'ar'}">
    <div [ngClass]="{'ifp-geospatial__custom-btn--unactive' : selectedMapButton === geoMapKeys.preview}"
      class="ifp-geospatial__custom-btn"
      [ngClass]="{'ifp-geospatial__custom-btn--active' : selectedMapButton === geoMapKeys.customize}"
      (click)="toggleCustomizeMode(geoMapKeys.customize)">
      <!-- {{geoMapKeys.customize | translate}} -->
      {{ defaultLang == 'en' ? 'Customize' : 'تخصيص' }}
    </div>

    @if(!isCustomizeOpen) {
    <div class="ifp-geospatial__custom-btn">
      <!-- <label for="preview-btn"> {{geoMapKeys.preview | translate}}</label> -->
      <label for="preview-btn">  {{ defaultLang == 'en' ? 'Preview' : 'معاينة' }}</label>
      <input (change)="togglePreviewMode(isPreviewOpen ? '' : geoMapKeys.preview)" type="checkbox" id="preview-btn"
        class="ifp-geospatial__custom-btn--checkbox">
    </div>
    }
  </div>

  <!-- Loading Indicator (Only show when lastYear and lastQuarter are not available) -->
  @if (filterLoading || indicatorsLoading) {
  <div class="loading-indicator">
    <em class="spinner"></em>
  </div>
  }

  <!-- Main content is only rendered when lastYear and lastQuarter are available -->
  @if (!filterLoading && !indicatorsLoading) {
  <!-- Filters Component with Animation -->
  @if (selectedMapButton !== geoMapKeys.customize) {
  <div class="filters-wrapper" [class.filters-wrapper--hidden]="selectedMapButton === geoMapKeys.preview"
    [class.filters-wrapper--visible]="selectedMapButton !== geoMapKeys.preview">
    <ifp-filters #filterCmp [filtersData]="filtersData" [lastYear]="lastYear" [lastQuarter]="lastQuarter"
      [filterObject]="filterObject" [filtersBackupAfterCustomize]="filtersBackupAfterCustomize"
      (filterChange)="filterChanged($event)" (filterReset)="filterReset($event)"
      (updateSelectionLevel)="updateSelectionLevel($event)" (domainChanged)="domainChanged($event)">
    </ifp-filters>
  </div>
  }

  <!-- Indicators Component with Animation -->
  <!-- <div class="indicators-wrapper"

          > -->
  <ifp-geo-indicators [class.indicators-wrapper--hidden]="selectedMapButton === geoMapKeys.preview"
    [class.indicators-wrapper--visible]="selectedMapButton !== geoMapKeys.preview" #indicatorCmp
    [indicatorsList]="indicatorsList" [indicatorsListValues]="indicatorsListValues" [leftIndicators]="leftIndicators"
    [rightIndicators]="rightIndicators" [leftCenterIndicators]="leftCenterIndicators"
    [rightCenterIndicators]="rightCenterIndicators" [lastYear]="lastYear" [lastQuarter]="lastQuarter"
    [filterObject]="filterObject" (actionChange)="actionChange($event)"
    (regionChangedFromChart)="regionChangeFromChart($event)"
    (triggerResetFilter)="resetFilterComponent($event)">
  </ifp-geo-indicators>
  <!-- </div> -->



  @if(!isMapLoadingComplete){
  <ifp-loading-map [loaderText]=" defaultLang == 'en' ? 'Loading Map' : 'جاري تحميل الخريطة'"></ifp-loading-map>
  }
  <!-- Map Component -->
  <div
    [ngClass]="{'full-screen': selectedMapButton == geoMapKeys.preview, 'map-container--preview': selectedMapButton == geoMapKeys.preview}">
    <ifp-map-component #mapCmp [filtersData]="filtersData" [isPreviewOpen]="isPreviewOpen"
      [isCustomizeOpen]="isCustomizeOpen" (filterChange)="filterChanged($event)" (regionChanged)="regionChange($event)"
      (regionChangedFromMap)="regionChangedFromMap($event)" (districtsChange)="districtsChange($event)"
      (districtChangedFromMap)="districtChangedFromMap($event)" (communitiesChange)="communityChange($event)"
      (communityChangedFromMap)="communityChangedFromMap($event)">
    </ifp-map-component>
  </div>


  }
</div>