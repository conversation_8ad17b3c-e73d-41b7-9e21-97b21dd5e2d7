@use "../../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1201;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: $spacer-3;
  &__overlay{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1200;
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    &--transparent {
      backdrop-filter: none;
      -webkit-backdrop-filter: blur(30px);
      background-color: $ifp-color-black-16;
    }
  }
  &__bg-grey {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background-color: $ifp-color-black;
    opacity: .3;

  }
  &__actions-btn {
    position: absolute;
    right: 29px;
    top: 29px;
    cursor: pointer;
  }

  &--alert-box {
    top: auto;
    right: auto;
    bottom: 32px;
    left: 50%;
    transform: translateX(-50%);
  }
  &--mr-0 {
    margin: $spacer-0;
  }
}

::ng-deep .ifp-modal app-ifp-modal-template {
  position: relative;
}
