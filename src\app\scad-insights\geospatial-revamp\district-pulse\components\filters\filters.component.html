<div #filtersContainer class="ifp-filters-container">
    <div
        (click)="onToggleFilters()"
        [class.visible]="isMenuHidden"
        [class.hidden]="!isMenuHidden"
        class="open-filters-icon"
        [ngClass]="arrowRotationClass"
    >
        <em class="ifp-icon ifp-icon-double-arrow1"></em>
    </div>

    <!-- Filters menu panel -->
    <div 
        class="filters-menu"
        [class.hidden]="isMenuHidden"
        [class.visible]="!isMenuHidden"
        [attr.aria-hidden]="isMenuHidden"
    >
        <!-- Close button inside the panel -->
        <div (click)="onToggleFilters()" class="filter-icons">
            <em class="ifp-icon ifp-icon-double-arrow1"></em>
        </div>

        <!-- Filter components -->
        <div class="select-container">
            <ifp-regions-filter
                [menuId]="regionMenuId"
                [openMenu]="openMenu"
                (toggleDropdownMenu)="toggleMenu($event)">
            </ifp-regions-filter>
        </div>

        <div class="select-container">
            <ifp-districts-filter
                [menuId]="districtMenuId"
                [openMenu]="openMenu"
                (toggleDropdownMenu)="toggleMenu($event)">
            </ifp-districts-filter>
        </div>

        <div class="select-container">
            <ifp-custom-filter
                [menuId]="otherMenuId"
                [openMenu]="openMenu"
                (toggleDropdownMenu)="toggleMenu($event)">
            </ifp-custom-filter>
        </div>

        <!-- Reset button -->
        <div class="filter-icons">
            <em class="ifp-icon ifp-icon-reset"></em>
        </div>
    </div>
</div>