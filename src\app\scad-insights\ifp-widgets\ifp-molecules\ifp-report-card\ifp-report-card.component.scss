@use "../../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-report-card {
  background-color: $ifp-color-section-white;
  border: 1px solid $ifp-color-grey-3;
  border-radius: 15px 15px 0 0;
  &__banner {
    border-radius: 15px;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__head {
    position: relative;
    height: 200px;
    margin: -1px -1px 0 -1px;
    border-radius: 15px;
    overflow: hidden;
    &::before {
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.24);
    }
    ifp-analysis-card-header {
      position: relative;
      z-index: 1;
    }
  }

  ifp-analysis-card-header {
    position: absolute;
    top: 16px;
    left: 16px;
    right: 16px;
  }

  &__button-sec {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
  }

  &__tags-wrapper {
    display: flex;
    align-items: center;
    gap: 8px; // Space between tags
    flex-wrap: wrap; // Allow wrapping if needed
  }

  &__dropdown-tag {
    font-size: 12px; // Smaller font size for the dropdown tag
  }
  &__doc-sec {
    display: block;
    margin: $spacer-0 (-$spacer-3) (-$spacer-3);
    width: calc(100% + 32px);

    // Special styling when dropdown functionality is active (with tags)
    &--with-dropdown {
      margin: 28px -16px -16px;
      border-top: 1px solid #e5e7eb; // Light gray border
    }
  }
  .ifp-analysis {
    padding: $spacer-3;
    &__substitle {
      min-height: 0;
      margin-top: $spacer-0;
    }
  }
  &__btn-footer {
    display: flex;
    align-items: center;
    margin-inline-start: auto;
  }
  &__cross {
    margin-inline-end: $spacer-2;
  }
  &--custom {
    cursor: pointer;
  }
}
:host::ng-deep {
  .ifp-report-card__cross,
  .ifp-report-card__download {
    .ifp-btn {
      font-size: $ifp-fs-6;
    }
  }

  // Target the specific dropdown tag to match security tag padding
  .ifp-report-card__dropdown-tag {
    .ifp-tag {
      padding: 6px 8px !important;
    }
  }
}
:host-context([dir="rtl"]) {
  .ifp-report-card {
    &__cross {
      margin-left: 0;
      margin-right: auto;
    }
  }
}
