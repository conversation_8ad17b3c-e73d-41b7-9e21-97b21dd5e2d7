import { AfterViewInit, Component, OnDestroy, OnInit, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CensusServiceService } from '../../../../../../census-service.service';

import MapView from '@arcgis/core/views/MapView';
import Expand from '@arcgis/core/widgets/Expand';
import { TranslateService } from '@ngx-translate/core';
import GroupLayer from "@arcgis/core/layers/GroupLayer.js";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import SimpleRenderer from "@arcgis/core/renderers/SimpleRenderer";
import { CommonService } from '../../../../../../common.service';
import {SubSink} from "subsink";
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

@Component({
  selector: 'ifp-poi',
  standalone: true,
  imports: [],
  template: `<div #poiNode></div>`,
  styleUrl: './poi.component.scss'
})
export class PoiComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

  subsink: SubSink = new SubSink();

  @Input() view!: MapView;
  public isCustomizeOpen: boolean = false;
  private expand: Expand | undefined;
  public pois: any;
  public language: string = 'en';

  constructor(
    private gisSharedService: CensusServiceService,
    private _translate: TranslateService,
    private themeService: ThemeService,
    private commonService: CommonService,
  ) {

  }

  ngOnInit(): void {
    this.subsink.add(
      this.themeService.defaultLang$.subscribe((lang: string) => {
        this.language = lang;
      }),
      this.gisSharedService._censusPoiData$.subscribe((pois: any) => {
        if(pois.length > 0) {
          this.pois = pois;
          this.createGroupLayersAndAddToMap(pois);
        }
      })
    ); 

    this.commonService._isCustomizeOpen$.subscribe((_isCustomizeOpen : boolean) => {
      this.isCustomizeOpen = _isCustomizeOpen;
      this.toggleExpand();
    });

  }

  toggleExpand() {
    if (this.isCustomizeOpen &&  this.expand) {
      this.destroyWidget();  
    } else {
      //this.initializeWidget();  
    }
    this.isCustomizeOpen = !this.isCustomizeOpen;  
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['view'] && !changes['view'].firstChange) {
      // this.initializeWidget();
    }
  }

  ngAfterViewInit(): void {
    
  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
  }

  initializeWidget() {
    
    if (!this.view) return; 

    if (this.expand) return;

    if(this.view){
      var customLayerDiv = document.createElement('div'); 
      customLayerDiv.id = "customLayersContainerPoi";
      const myGroupLayers = this.gisSharedService.getGrouplayers(this.view.map);
      let iconUrl = '';
      let allGroupLayers = myGroupLayers[0].layers._items;
      allGroupLayers.forEach((layer: any) => {
        if(layer.type === "feature" && layer.renderer && layer.renderer.symbol) {
          let symbol = layer.renderer.symbol;
          if(symbol.type === "picture-marker") {
            iconUrl = symbol.url;
          }
        }
        const div = document.createElement('div');
        div.className = 'checkbox-container';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = layer.visible;

        const label = document.createElement('label');
        label.innerHTML = this._translate.instant(layer.title);
        label.style.flex = "2";

        let img = document.createElement('img');
        img.src = iconUrl;
        img.alt = 'Thumbnail';
        img.width = 15;

        checkbox.addEventListener('change', (event) => {
          layer.visible = checkbox.checked;
        });

        div.appendChild(img);
        div.appendChild(label);
        div.appendChild(checkbox); 
        customLayerDiv.appendChild(div);
      });

      this.expand = new Expand({
        view: this.view,
        content: customLayerDiv,
        expandIconClass: 'esri-icon-layers',
        group: 'top-right',
        expandTooltip: this.language == 'en' ? 'Point of Interest' : 'نقاط الاهتمام',
        collapseTooltip: this.language == 'en' ? 'Collapse' : 'تصغير',
      } as __esri.ExpandProperties);

      this.view.ui.add(this.expand, {position: 'bottom-left', index: 1});

      this.expand?.watch('expanded', (isExpanded) => {
        if (isExpanded) {
          this.gisSharedService.registerWidget(this.expand!); // The `!` asserts that `expand` is not undefined
        }
      });
      
      
    }
  }

  private destroyWidget() {
    if (this.expand) {
      this.view.ui.remove(this.expand);
      this.expand.destroy();
      this.expand = undefined; 
    }
  }

  async createGroupLayersAndAddToMap(pois: any) {
    const otherGroupLayer = new GroupLayer({
      title: "Point of Interest",
      layers: [],
    });
    this.view.map.layers.add(otherGroupLayer);
    const promises = pois.map((poi: any) => {
      const iconUrl = `data:image/svg+xml;base64,${poi.icon}`;
      this.createFeatureLayerToMapPoi(poi.url, 1, this.language == 'en' ? poi.name : poi.name_ar, false, iconUrl, otherGroupLayer);  
    });
    await Promise.all(promises);
    this.initializeWidget();
  }

  createFeatureLayerToMapPoi(url: string, opacity: any, layerName: any, visible: any, icon: any, groupLayer: any) {

    const iconSymbol = new PictureMarkerSymbol({
      url: icon,
      width: "12px",
      height: "12px",
    });

    const renderer = new SimpleRenderer({
      symbol: iconSymbol
    });

    const featureLayer = new FeatureLayer({
      url: url,
      outFields: ['*'],
      opacity: opacity,
      visible: visible,
      title: layerName,
      renderer: renderer
    });
    groupLayer.layers.push(featureLayer);
  }

}
