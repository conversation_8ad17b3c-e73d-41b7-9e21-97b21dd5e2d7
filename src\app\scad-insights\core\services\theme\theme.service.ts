import { Injectable, signal, WritableSignal } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { headerKeys } from '../../constants/header.constants';
@Injectable({
  providedIn: 'root'
})
export class ThemeService {

  container = document.getElementsByTagName('html')[0];
  defaultTheme:string | null = localStorage.getItem(headerKeys.appearence) ? localStorage.getItem(headerKeys.appearence) : 'light';
  defaultTheme$ = new BehaviorSubject(this.defaultTheme);

  defaultFontSize: string | null = localStorage.getItem(headerKeys.fontSize) ? localStorage.getItem(headerKeys.fontSize) : 'md';
  defaultFontSize$ = new BehaviorSubject(this.defaultFontSize);

  defaultCursorStyle: string | null= localStorage.getItem(headerKeys.cursor) ? localStorage.getItem(headerKeys.cursor) : 'type1';
  defaultCursorStyle$ = new BehaviorSubject(this.defaultCursorStyle);

  defaultLang: string = 'en';
  defaultLang$ = new BehaviorSubject(this.defaultLang);

  startJourny$ = new Subject<{data:any, status: boolean}>();

  public isStartJourny:boolean=false;
  public fontChange = new Subject();

  public geoConfig: WritableSignal<Record<string, any>> = signal({
    opacity: this.getGeoOpacity()
  });

  getGeoOpacity() {
    if (localStorage.getItem('INDICATOR_SETTINGS')) {
      const settings = JSON.parse(localStorage.getItem('INDICATOR_SETTINGS') ?? '');
      return settings?.opacity;
    } else {
      return 60;
    }
  }


  changeFontSize(size: string | null) {
    // const this.container = document.getElementsByTagName('html')[0];
    if (this.container) {
      if (size) {
        this.defaultFontSize = size;
        this.defaultFontSize$.next(size);
        if (size === 'sm') {
          this.container.classList.remove('ifp-font-md');
          this.container.classList.remove('ifp-font-lg');
          this.container.classList.add('ifp-font-sm');
        } else if (size === 'md') {
          this.container.classList.remove('ifp-font-sm');
          this.container.classList.remove('ifp-font-lg');
          this.container.classList.add('ifp-font-md');
        } else {
          this.container.classList.remove('ifp-font-sm');
          this.container.classList.remove('ifp-font-md');
          this.container.classList.add('ifp-font-lg');
        }
        localStorage.setItem(headerKeys.fontSize, size);
      }
    } else {
      this.defaultFontSize = size;
      this.defaultFontSize$.next(size);
      localStorage.setItem(headerKeys.fontSize, 'md');
    }
    this.fontChange.next(size);
  }

  // changeCursorStyle
  changeCursorStyle(type: string | null) {
    if (this.container) {
      if (type) {
        this.defaultCursorStyle = type;
        this.defaultCursorStyle$.next(type);
        if (type === 'type1') {
          this.container.classList.remove('ifp-cursor-2');
          this.container.classList.remove('ifp-cursor-3');
          this.container.classList.add('ifp-cursor-1');
        } else if (type === 'type2') {
          this.container.classList.remove('ifp-cursor-1');
          this.container.classList.remove('ifp-cursor-3');
          this.container.classList.add('ifp-cursor-2');
        } else {
          this.container.classList.remove('ifp-cursor-1');
          this.container.classList.remove('ifp-cursor-2');
          this.container.classList.add('ifp-cursor-3');
        }
        localStorage.setItem(headerKeys.cursor, type);
      } else {
        this.defaultCursorStyle = 'type1';
        this.defaultCursorStyle$.next('type1');
        localStorage.setItem(headerKeys.cursor, 'type1');
      }
    }
  }

  changeTheme(theme: string | null) {
    this.defaultTheme = theme;
    this.defaultTheme$.next(theme);
    const containerTag = document.body;
    if (containerTag) {
      if (theme) {
        if (theme === 'light') {
          containerTag.classList.remove('ifp-dark-theme');
          containerTag.classList.add('ifp-light-theme');
        } else {
          containerTag.classList.remove('ifp-light-theme');
          containerTag.classList.add('ifp-dark-theme');
          // ifpColors.red = '#a34040';
        }
        localStorage.setItem(headerKeys.appearence, theme);
      } else {
        this.defaultTheme = theme;
        this.defaultTheme$.next(theme);
        localStorage.setItem(headerKeys.appearence, 'light');
      }
    }
  }

  changeLanguage(lang: string) {
    document.cookie = `lang=${lang}; path=/;`;
    this.defaultLang = lang;
    this.defaultLang$.next(lang);
    const containerTag = document.body;
    localStorage.setItem(headerKeys.lang, lang);
    if (lang === 'en') {
      containerTag.removeAttribute('dir');
    } else {
      containerTag.setAttribute('dir', 'rtl');
    }
  }

  getDefaultSettings() {
    return {settings: [
      {
        name: 'fontSize',
        value: 'md'
      },
      {
        name: 'cursor',
        value: 'type1'
      },
      {
        name: 'theme',
        value: 'light'
      },
      {
        name: 'lang',
        value: 'en'
      }
    ]};
  }

  setDefaultSettings(data: any) {
    const defaultSettings = {
      appearance: (data?.theme && data.theme !== 'undefined') ? data.theme : 'light',
      fontSize: (data?.fontSize && data.fontSize !== 'undefined') ? data.fontSize : 'md',
      cursor: (data?.cursor && data.cursor !== 'undefined') ? data.cursor : 'type1',
      lang: (data?.lang && data.lang !== 'undefined') ? data.lang : 'en'
    };

    this.defaultCursorStyle = defaultSettings.cursor;
    this.defaultCursorStyle$.next(defaultSettings.cursor);
    localStorage.setItem(headerKeys.cursor, defaultSettings.cursor);

    this.defaultFontSize = defaultSettings.fontSize;
    this.defaultFontSize$.next(defaultSettings.fontSize);
    localStorage.setItem(headerKeys.fontSize, defaultSettings.fontSize);

    this.defaultTheme = defaultSettings.appearance;
    this.defaultTheme$.next(defaultSettings.appearance);
    localStorage.setItem(headerKeys.appearence, defaultSettings.appearance);
    localStorage.setItem('defaultSettings', JSON.stringify(defaultSettings));

    this. changeLanguage( defaultSettings.lang);
  }


}
