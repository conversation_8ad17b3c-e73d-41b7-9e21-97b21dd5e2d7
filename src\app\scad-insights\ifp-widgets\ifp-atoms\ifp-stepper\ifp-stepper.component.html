<div class="ifp-stepper__wrapper" [ngClass]="{'ifp-stepper--adv': advance, 'ifp-stepper--blue':blueTheme(), 'ifp-stepper--gradient': gradientTheme()}">
  @for (item of stepData; track item; let index=$index) {
  <div class="ifp-stepper__item " [ngClass]="{'ifp-stepper--completed': completed >= index+1, 'ifp-stepper--optional': stepData?.[index+1]?.optional || item.optional, 'ifp-stepper--pending': pending == index+1}">
    <div class="ifp-stepper__counter" (click)="stepClick(item)">
      @if (blueTheme()) {
         <span class="ifp-stepper__count">{{item.counter}}</span>
      } @else {
         @if ( pending === index+1 && completed === index) {
       @if (showCount()) {
            <span class="ifp-stepper__count">{{item.counter}}</span>
        }@else {
    <em class="ifp-stepper__icon ifp-icon {{icon}}"></em>
        }
      } @else {
        @if (icon) {
          <em class="ifp-stepper__icon ifp-icon {{icon}}"></em>
        } @else {
          <span class="ifp-stepper__count">{{item.counter}}</span>
        }
      }
      <em class="ifp-stepper__tick ifp-icon ifp-icon-tick"></em>
      }
     
      
    </div>
   @if (item?.subName) {
    <div class="ifp-stepper__sub-name">{{item?.subName ?? '' | translate}}  {{item.stepCount}}</div>
   }
    <div class="ifp-stepper__name">{{item.name | translate}} @if(count){ {{item.stepCount}} }</div>
  </div>
}
</div>
