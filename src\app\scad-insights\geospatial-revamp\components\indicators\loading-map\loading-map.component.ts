import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  OnChanges,
  OnDestroy,
  OnInit,
  Input,
  SimpleChanges,
  inject,
  input,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'ifp-loading-map',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './loading-map.component.html',
  styleUrl: './loading-map.component.scss'
})
export class LoadingMapComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

  @Input() isCustomize: boolean = false;
  // @Input() isLoading: boolean = true;

  public loaderText = input('');

  ngOnChanges(changes: SimpleChanges) {

  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }

}

