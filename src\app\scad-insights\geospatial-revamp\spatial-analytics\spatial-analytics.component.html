@if(!mapUnderMaintenanace) {
<div class="geospatial-revamp-container">
  <!-- <div class="geospatial-revamp-heading">
    <h2 class="ifp-card-heading">Spatial Analytics</h2>
  </div> -->
  
  <div class="geospatial-revamp-header">
    <h1>{{ 'GeoRevampCensusTitle' | translate}} </h1>
    <p>{{ 'GeoRevampCensusOverview' | translate}}</p>
  </div>
  <div class="geospatial-revamp-body">
    <div class="revamp-box-container" *ngIf="getFilteredData().length > 0; else loadingTemplate">
      <div *ngFor="let item of getFilteredData(); let i = index">
        <a [routerLink]="['/geospatial', item.DOMAIN_ID]">
          <div class="geospatial-revamp-box" 
            [ngClass]="{'pop-box': item.DOMAIN_ID == 1, 'lab-box': item.DOMAIN_ID == 2, 'estate-box': item.DOMAIN_ID == 3}" 
            [ngStyle]="{'left': calculateLeftPosition(i)}"
            (mouseenter)="onHover(i, true)" 
            (mouseleave)="onHover(i, false)">
            <div class="geospatial-revamp-box-content">
                <img *ngIf="item.DOMAIN_ID == 1" src="../../../../assets/icons/geospatial-ravamp/geospatial-revamp-pop.svg" alt="" />
                <img *ngIf="item.DOMAIN_ID == 2" src="../../../../assets/icons/geospatial-ravamp/geospatial-revamp-pop.svg" alt="" />
                <img *ngIf="item.DOMAIN_ID == 3" src="../../../../assets/icons/geospatial-ravamp/buildings.svg" alt="" />
                <div class="geospatial-revamp-box-text">
                  <p [ngStyle]="{'color': '#4b452d'}">{{ language == 'ar' ? item.AR : item.EN }}</p> 
                  <div class="value-container">
                    @if(item.DOMAIN_ID === 3) {
                      <p class="value-number" [ngStyle]="{'color': '#4b452d'}">{{ formatNumber(item.VALUE) }}</p>
                    } @else {
                      <p class="value-number">{{ formatNumber(item.VALUE) }}</p> 
                    }
                    <span class="value-label">
                      @if(item.DOMAIN_ID === 1) {
                        {{ language == 'ar' ? 'السكان' : 'Residents' }}
                      } @else if(item.DOMAIN_ID === 2) {
                        {{ language == 'ar' ? 'القوى العاملة' : 'Employees' }}
                      } @else if(item.DOMAIN_ID === 3) {
                        {{ language == 'ar' ? 'المباني' : 'Buildings' }}
                      }
                    </span>
                  </div> 
                </div>
            </div>
            <div class="geospatial-revamp-box-checkbox">           
              <img 
                class="arrow-icon"
                [ngClass]="'show'"
                src="../../../../assets/icons/geospatial-ravamp/geospatial-revamp-arrow.svg" alt="" 
              />
            </div>
          </div>
        </a>
      </div>
    </div> 
     
    <ng-template #loadingTemplate>
      <div class="geospatial-revamp-body">
        <div class="revamp-box-container">
          <!-- Show message if no data available due to permissions -->
          @if(summaryData.length > 0 && getFilteredData().length === 0) {
            <div class="no-access-message">
              <p>{{ 'NoAccessToDataMessage' | translate }}</p>
            </div>
          } @else {
            <div class="loading">
              <p>{{ 'GeoCensusDataLoadingIndicator' | translate }}</p>
            </div>
          }
        </div>
      </div>
    </ng-template> 
 
  </div>
</div>
} @else {
  <div class="map-maintenanace">
    <div class="map-maintenanace-content" [ngClass]="{'dark-theme' : (themeService.defaultTheme$ | async) === 'dark'}">
      <h3>{{ 'GeoRevampMaintenanceTitle' | translate}}</h3>
      <span>{{ 'GeoRevampMaintenanceBio' | translate}}
      </span>
    </div>
    @if ((themeService.defaultTheme$ | async) === 'dark') {
      <img src="../../../../assets/images/mapUnavailabilitydarkmode.jpg" alt=""> 
    }@else{
      <img src="../../../../assets/images/mapUnavailabilitylightmode.jpg" alt=""> 
    }
  </div>
}

