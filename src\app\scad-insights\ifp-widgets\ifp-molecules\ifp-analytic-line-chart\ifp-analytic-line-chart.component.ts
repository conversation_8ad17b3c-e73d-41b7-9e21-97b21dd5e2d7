import { TranslateService } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import {
  CommonModule,
  DatePipe,
  DecimalPipe,
  SlicePipe,
} from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import * as Highcharts from 'highcharts';
import { Options } from 'highcharts';
import HighchartsMore from 'highcharts/highcharts-more';
import { Subscription } from 'rxjs';
import { HighchartsChartModule } from 'highcharts-angular';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpNoDataComponent } from '../ifp-no-data/ifp-no-data.component';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { numberChartDatePipe } from 'src/app/scad-insights/core/pipes/numberChart.pipe';
import { FilterService } from 'src/app/scad-insights/core/services/filter/filter.service';
import { LineBreakPipe } from 'src/app/scad-insights/core/pipes/lineBreak.pipe';

// declare let require: any;
// require('highcharts/modules/exporting')(Highcharts);

@Component({
  selector: 'app-ifp-analytic-line-chart',
  templateUrl: './ifp-analytic-line-chart.component.html',
  styleUrls: ['./ifp-analytic-line-chart.component.scss'],
  imports: [HighchartsChartModule, CommonModule, IfpNoDataComponent],
  providers: [numberChartDatePipe, LineBreakPipe]
})
export class IfpAnalyticLineChartComponent implements OnChanges, OnInit {
  @Input() chartData: any = [];
  @Input() yaxisLabel!: string;
  @Input() xAxisLabel!: string;
  @Input() isRangeSelect: boolean = false;
  @Input() height: number | string = 400;
  @Input() chartFilterOgData: any = [];
  @Input() isRender: boolean = true;
  @Input() isDatalabel: boolean = true;
  @Input() tooltipQuarterLabel: string = '';
  @Input() tooltipYearOnLabel: string = '';
  @Input() isYearly: boolean = false;
  @Input() isDashboardCard: boolean = false;
  @Input() xAxisLabelType: any = 'datetime';
  @Input() xAxisCatogory: any = [];
  @Input() maxXaxisValue: any = '';
  @Input() toolTipFormat: string = 'number_1.0-1';
  @Input() dataLabelColor: string = '#000000';
  @Input() isMultiYaxis: boolean = false;
  @Input() spacingBottom: number = 15;
  @Input() spacingTop: number = 10;
  @Input() spacingLeft: number = 10;
  @Input() isCoi: boolean = false;
  @Input() spacingRight: number = 10;
  @Input() isPrint: boolean = false;
  @Input() enableAnimation = true;
  @Input() legend = true;
  @Input() hideSuffix: boolean = false;
  @Input() removeRangePrecntage = false;
  @Input() width!: number | null;
  @ViewChild('chart') chartContainer!: any;
  @Output() rangeValueUpdated = new EventEmitter();
  @Output() zoomOutDisabled = new EventEmitter();
  @Input() isBold: boolean = true;
  @Input() disableDarkModeSwitch: boolean = false;
  @Input() yAxisLineWidth: number = 0;
  @Input() plotBandsY: any;
  @Input() plotLinesY: Record<string, any>[] = [];
  @Input() tickIntervalY!: number | undefined;
  @Input() showGridLineX: boolean = true;
  @Input() enableCustomTooltip: boolean = false;
  @Input() customTooltipData!: { title: string; description: string }[];
  @Input() roundOffDecimels: number = 2;
  @Input() isCFD: boolean = false;
  @Input() dataLebelOverflow: Highcharts.DataLabelsOverflowValue = 'allow';
  @Input() minYaxisValue: number | null | undefined = null;
  @Input() maxYaxisValue: number | null | undefined = null;
  @Input() preciseValue: boolean = true;
  @Input() xAxisLabelUpdates: Record<string, any> = {};
  // eslint-disable-next-line @typescript-eslint/naming-convention
  Highcharts: typeof Highcharts = Highcharts;
  chart!: Highcharts.Chart;
  isFullscreen: boolean = false;
  chartOptions!: Options;
  public axisGroup!: Element;
  public clickSubscription: Subscription[] = [];
  public updateFlag = false;
  public rangeSelectedValue: any;
  public isDarkTheme!: boolean;
  public rangeIndex: number = 0;
  public axisTitleColor: string = ifpColors.primaryGrey;
  chartXaxis: any;
  selectedPoints: any = [];
  public chartInstance!: Highcharts.Chart;
  isToolTip: boolean = true;
  public dataMax!: number;
  public isSeriesIndex: any = undefined;
  public plotBand: Record<string, any> = {
    line1: null,
    line2: null,
  };

  constructor(
    private _cdr: ChangeDetectorRef,
    public _number: DecimalPipe,
    private _translate: TranslateService,
    private themeService: ThemeService,
    private _shortNumber: numberChartDatePipe,
    private datePipe: DatePipe,
    private slicePipe: SlicePipe,
    private _filterService: FilterService,
    private _lineBreakPipe: LineBreakPipe
  ) {


    HighchartsMore(Highcharts); // Register the highcharts-more module
  }

  ngOnInit(): void {
    if (!this.disableDarkModeSwitch) {
      this.themeService.defaultTheme$.subscribe((val) => {
        if (val == 'dark') {
          this.isDarkTheme = true;
          this.dataLabelColor = '#ffffff';
          this.axisTitleColor = ifpColors.white;
        } else {
          this.isDarkTheme = false;
          this.dataLabelColor = '#000000';
          this.axisTitleColor = ifpColors.primaryGrey;
        }
        this.createChart();
      });
    }

  }

  ngOnChanges(_changes: SimpleChanges): void {
    if (this.chartXaxis) {
      this.chartXaxis.setExtremes(null, null);
    }
    this.selectedPoints = [];
    this.rangeValueUpdated.emit(undefined);
    if (this.isPrint) {
      this.preciseValue = false;
    }
    this.createChart();
  }

  createChart() {
    if (this.isDashboardCard) {
      this.isDatalabel = this.chartData[0]?.isDatalabel;
      this.preciseValue = [true, false].includes(this.chartData[0]?.isPrecise)
        ? this.chartData[0]?.isPrecise
        : false;
    }
    const that = this;
    this.chartOptions = {
      chart: {
        panning: {
          enabled: true,
          type: 'xy',
        },
        events: {
          load: (event: Event | any) => {
            const axis = event.target?.['xAxis']?.[0];
            that.chartXaxis = axis;
          },
        },
        height: that.height,
        spacing: that.chartData[0]?.spacing
          ? that.chartData[0]?.spacing
          : [15, 10, 10, 10],
      },
      credits: {
        enabled: false,
      },
      tooltip: {
        enabled: that.isDashboardCard ? false : that.isToolTip,
        animation: false,
        hideDelay: 0,
        outside: false,
        backgroundColor: ifpColors.blackLight,
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: ifpColors.white,
          zIndex: 9999,
        },
        useHTML: true,
        className: `ifp-chart-tooltip ${that.enableCustomTooltip ? 'ifp-chart-tooltip--custom' : ''}`,
        formatter: function (): any {
          const currentIndex = this.point?.index;
          const chartData: any = this;
          let legendSymbol: any;
          let monthData: any;
          let yearData: any;
          let actualData: any;
          let hoverData = that.chartFilterOgData[this.point?.series?.index]
            ? that.chartFilterOgData[this.point?.series?.index][currentIndex]
            : '';
          if (that.isCoi) {
            if (!hoverData) {
              hoverData = {
                [chartConstants.ACTUAL_VALUE]: '',
              };
            }
            hoverData[chartConstants.ACTUAL_VALUE] =
              that.chartData[this.point.series?.index].actualValue[
              this.point?.index
              ];
            hoverData.MonthLabel =
              that.chartData[this.point.series?.index].monthValues[
              this.point?.index
              ];
            hoverData.YearLabel =
              that.chartData[this.point.series?.index].yearValues[
              this.point?.index
              ];
          }
          let tooltipHead =
            that.xAxisLabelType != chartConstants.xAxisCatogory
              ? that.datePipe.transform(
                new Date(chartData.x),
                !that.isYearly ? 'MMM yyyy' : 'yyyy'
              )
              : chartData.x;
          if (Math.abs(hoverData?.MonthLabel) >= 0) {
            monthData = `${that.tooltipQuarterLabel} : ${that._number.transform(
              hoverData.MonthLabel,
              '1.2-2'
            )}`;
          }
          if (Math.abs(hoverData?.YearLabel) >= 0) {
            yearData = `${that.tooltipYearOnLabel} : ${that._number.transform(
              hoverData.YearLabel
            )}`;
          }
          if (hoverData?.[chartConstants.ACTUAL_VALUE]) {
            actualData = `${chartConstants.ACTUAL_VALUE
              } : ${that._number.transform(
                hoverData[chartConstants.ACTUAL_VALUE]
              )}`;
          }
          if (this.point.series?.legendItem?.symbol && !that.enableCustomTooltip) {
            legendSymbol = `<svg width='20' height='20' class="ifp-chart-tooltip__legend">${this.point.series.legendItem.symbol.element.outerHTML
              ? this.point.series.legendItem.symbol.element.outerHTML
              : ''
              }</svg><div class="ifp-chart-tooltip__mark"><p class="ifp-chart-tooltip__mark-outer"><span class="ifp-chart-tooltip__mark-head">${this.point.series.name
                ? that.slicePipe.transform(this.point.series.name, 0, 50) +
                (this.point.series.name?.length > 50 ? '...' : '')
                : ''
              }</span><span class="ifp-chart-tooltip__mark-value">${chartData.y !== undefined
                ? parseFloat(chartData.y.toFixed(that.roundOffDecimels)).toLocaleString('en-AE')
                : ''
              }</span></p><p class="ifp-chart-tooltip__mark-outer">${monthData ? monthData : ''
              }</p><p class="ifp-chart-tooltip__mark-outer">${yearData ? yearData : ''
              }</p>
            <p class="ifp-chart-tooltip__mark-outer">${actualData ? actualData : ''
              }</p></div>`;
          }
          if (that.enableCustomTooltip && that.customTooltipData.length) {
            tooltipHead = `<strong>${that.customTooltipData[currentIndex].title} :</strong><br>${this.point.options.y?.toFixed(that.roundOffDecimels)}`;
            legendSymbol = that.customTooltipData[currentIndex]?.description ?? '';
          }

          const toolTipCnt = ` <span class="ifp-chart-tooltip__value">${legendSymbol ? that._lineBreakPipe.transform(legendSymbol) : ''
            }</span>`;
          if (that.enableCustomTooltip) {
            return `<div class="ifp-chart-tooltip__wrapper"> <span class="ifp-chart-tooltip__header">${tooltipHead}</span>${toolTipCnt}</div>`;
          }
          return legendSymbol
            ? `<div class="ifp-chart-tooltip__wrapper"> <span class="ifp-chart-tooltip__header">${tooltipHead}</span>${toolTipCnt}</div>`
            : '';
        },
      },
      legend: {
        rtl: this._translate.currentLang === 'ar',
        itemStyle: {
          fontSize: that.isPrint && that.isBold ? '15px' : '14px', // set the desired font size here,
          fontFamily: 'Noto Sans',
          fontWeight: that.isPrint && that.isBold ? '800' : '600',
        },
        enabled: [true, false].includes(that.chartData[0]?.legendPositions?.isLegend) ? that.chartData[0]?.legendPositions?.isLegend : that.legend,
        align: that.chartData[0]?.legendPositions?.Pos ? that.chartData[0]?.legendPositions?.Pos : 'center'
      },
      plotOptions: {
        line: {
          lineWidth: 3,
        },
        series: {
          dataLabels: {
            enabled: that.isDatalabel,
            inside: false,
            crop: false,
            color: that.dataLabelColor,
            overflow: this.dataLebelOverflow,
            allowOverlap: that.chartData[0]?.type.includes('') !== 'line' ? true : false,
            // format: '{point.y:.1f}',p
            formatter: function () {
              if ((that.preciseValue && this.y) || (this.y && this.y < 10)) {
                return parseFloat(this.y.toFixed(that.roundOffDecimels));
              }
              // const value = Math.abs(this.y ? this.y : 0);
              let returnValue: any = this.y;
              // const fractionSize = 1;
              // const abs = Math.abs(value);
              // const rounder = Math.pow(10, fractionSize);
              // const powers = [{ key: 'Q', value: Math.pow(10, 15) }, { key: 'T', value: Math.pow(10, 12) }, { key: 'B', value: Math.pow(10, 9) }, { key: 'M', value: Math.pow(10, 6) }, { key: 'k', value: 1000 }];
              // for (const element of powers) {
              //   let reduced = abs / element.value;
              //   reduced = Math.round(reduced * rounder) / rounder;
              //   if (reduced >= 1) {
              //     returnValue = reduced;
              //     break;
              //   }
              // }
              if (that.decimalCount(returnValue) > 2) {
                // returnValue = that.decimalPipe.transform(value, '1.1-2');
                returnValue = returnValue.toFixed(2);
              }
              // return this.y && this.y < 0 ? `-${returnValue}` : returnValue;
              returnValue = that._shortNumber.transform(
                returnValue,
                this.series.dataMax,
                // one decimal for CDF chart only
                that.isCFD ? 1 : 2
              );
              return returnValue;
            },
            style: {
              fontSize: that.isPrint && that.isBold ? '15px' : '14px',
              fontFamily: 'Noto Sans',
            },
          },
          allowPointSelect: false,
          point: {
            events: {
              click: (event: any) => {
                if (that.rangeIndex == event.point.index && this.isSeriesIndex == event.point.series.index) {
                  return;
                }
                if (that.isRangeSelect) {
                  that.selectedPoints = that.selectedPoints?.length >= 2 ? [] : that.selectedPoints;

                  // const data = {
                  //   xAxis: event.point.category,
                  //   value: event.point.y
                  // };
                  const data = {
                    color: ifpColors.grey, // Set the color of the plot line
                    value:
                      that.xAxisLabelType == chartConstants.xAxisDateTime
                        ? event.point.category
                        : that.xAxisCatogory.findIndex(
                          (x: any) => x == event.point.category
                        ), // Set the value where the plot line should appear
                    width: 3, // Set the width of the plot line
                    dashStyle: 'dash',
                    point: event.point.y,
                    index: event.point.index,
                  };
                  // if ((that.selectedPoints[0]?.index != event.point.index || that.selectedPoints.length <= 0) && this.isSeriesIndex != event.point.series.index) {
                  that.selectedPoints.push(data);
                  // }
                  setTimeout(() => {
                    that.selectedPoints.sort(
                      (a: { value: number }, b: { value: number }) =>
                        a?.value - b?.value
                    );
                    if (!this.removeRangePrecntage) {
                      that.rangeSelectedValue =
                        ((that.selectedPoints[1]?.point -
                          that.selectedPoints[0]?.point) /
                          Math.abs(that.selectedPoints[0]?.point)) *
                        100;
                    } else {
                      that.rangeSelectedValue =
                        that.selectedPoints[1]?.point -
                        that.selectedPoints[0]?.point;
                    }
                    that.rangeValueUpdated.emit({
                      value: that.rangeSelectedValue,
                      flag: Number.isNaN(that.rangeSelectedValue)
                        ? 'inprogress'
                        : 'completed',
                    });
                    that.updateFlag = true;
                    that.createChart();
                    that._cdr.detectChanges();
                  }, 300);
                }
                that.rangeIndex = event.point.index;
                this.isSeriesIndex = event.point.series.index;
              },
            },
          },
          animation: this.enableAnimation
            ? {
              duration: 1000,
              easing: 'linear',
            }
            : false,
        },
        arearange: {
          zones: [
            {
              value: 0,
              fillColor: '#ffffff',
            },
          ],
        },
      },
      title: {
        text: '',
      },
      xAxis: {
        plotLines: that.selectedPoints,
        tickInterval: that.chartData[0]?.type === 'bar' ? (that.xAxisCatogory?.length > 6 ? 3 : 0) : 0,
        type: that.xAxisLabelType,
        maxPadding: that.chartData[0]?.type === 'line' ? 0.04 : 0,
        dateTimeLabelFormats: {
          day: !that.isYearly ? '%e %b %Y' : '%Y', // Customize the date format as per your requirement
        },
        title: {
          text: that.xAxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: that.isPrint ? '800' : '500',
            color: this.isMultiYaxis ? this._filterService.getColor(0) : this.axisTitleColor
          }
        },
        categories:
          that.xAxisLabelType == chartConstants.xAxisCatogory
            ? that.xAxisCatogory
            : '',
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',
        gridLineWidth: !that.isRangeSelect && that.showGridLineX ? 50 : 0,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        labels: {
          formatter: (value: any) => {
            const format = value.dateTimeLabelFormat; // or get this dynamically
            const date = this.xAxisLabelType == chartConstants.xAxisDateTime ? new Date(value.value) : value.value;
            return this.xAxisLabelUpdates[value.value] ?? (this.xAxisLabelType == chartConstants.xAxisDateTime ? this.formatDateByStrftime(date, format) : date);
          },
          style: {
            fontSize: that.isPrint && that.isBold ? '15px' : '14px',
            color: that.isPrint ? '#000000' : this.axisTitleColor,
            lineHeight: '14px',
            fontWeight: that.isPrint && that.isBold ? '800' : '600',
            fontFamily: 'Noto Sans',
          },
          align: that.chartData[0]?.xAxisPositions?.xAxisPos
            ? that.chartData[0].xAxisPositions.xAxisPos
            : '',
        }
      },
      yAxis: [
        {
          visible: true, // Hide the y-axis
          gridLineDashStyle: 'Dash',
          gridLineWidth: 1,
          lineWidth: this.isMultiYaxis ? 2 : that.yAxisLineWidth,
          lineColor: this.isMultiYaxis ? this._filterService.getColor(0) : '#D9DCDD',
          offset: 30,
          title: {
            text: this.isMultiYaxis
              ? this.chartData[0]?.yAxisTitle
              : that.yaxisLabel,
            style: {
              fontSize: that.isBold ? '15px' : '14px',
              fontWeight: that.isPrint && that.isBold ? '800' : '600',
              color: this.isMultiYaxis
                ? this._filterService.getColor(0)
                : this.axisTitleColor,
            },
          },
          labels: {
            formatter: (value: any) => {
              this.dataMax = value.axis.dataMax;
              const number = this._number
                .transform(+value.value, '1.0-2')
                ?.replaceAll(',', '');

              return !that.hideSuffix ? this._shortNumber.transform(number, this.dataMax) : number;
            },
            style: {
              fontSize: that.isPrint && that.isBold ? '15px' : '14px',
              color: this.isMultiYaxis
                ? this._filterService.getColor(0)
                : this.axisTitleColor,
              fontFamily: 'Noto Sans',
              fontWeight: that.isPrint && that.isBold ? '800' : '600',
            },
          },
          plotBands: that.plotBandsY?.length ? that.plotBandsY : [{ visible: false }],
          plotLines: that.plotLinesY?.length ? that.plotLinesY : [{ visible: false }],
          tickInterval: that.tickIntervalY ?? undefined,
          min: that.minYaxisValue,
          max: that.maxYaxisValue
        },
        {
          visible:
            this.isMultiYaxis && this.chartData.length > 1 ? true : false, // Hide the y-axis
          gridLineDashStyle: 'Dash',
          gridLineWidth: 1,
          lineWidth: this.isMultiYaxis ? 2 : 0,
          lineColor: this._filterService.getColor(1),
          offset: 60,
          title: {
            text: this.isMultiYaxis
              ? this.chartData[1]?.yAxisTitle
              : that.yaxisLabel,
            style: {
              fontSize: that.isBold ? '15px' : '14px',
              fontWeight: that.isPrint && that.isBold ? '800' : '600',
              color: this._filterService.getColor(1),
            },
          },
          labels: {
            formatter: (value: any) => {
              this.dataMax = value.axis.dataMax;
              const number = this._number
                .transform(+value.value, '1.0-2')
                ?.replaceAll(',', '');
              return this._shortNumber.transform(number, this.dataMax);
            },
            style: {
              fontSize: '14px',
              color: this._filterService.getColor(1),
              fontWeight: that.isPrint && that.isBold ? '800' : '600',
            },
          },
          opposite: true,
          plotBands: that.plotBandsY?.length ? that.plotBandsY : [{ visible: false }],
          plotLines: that.plotLinesY?.length ? that.plotLinesY : [{ visible: false }],
          tickInterval: that.tickIntervalY ?? undefined
        },
        {
          visible:
            this.isMultiYaxis && this.chartData.length > 2 ? true : false, // Hide the y-axis
          gridLineDashStyle: 'Dash',
          gridLineWidth: 1,
          lineWidth: this.isMultiYaxis ? 2 : 0,
          lineColor: this._filterService.getColor(2),
          offset: 120,
          title: {
            text: this.isMultiYaxis
              ? this.chartData[2]?.yAxisTitle
              : that.yaxisLabel,
            style: {
              fontSize: that.isBold ? '15px' : '14px',
              fontWeight: that.isPrint && that.isBold ? '800' : '600',
              color: this._filterService.getColor(2),
            },
          },
          labels: {
            formatter: (value: any) => {
              this.dataMax = value.axis.dataMax;
              const number = this._number
                .transform(+value.value, '1.0-2')
                ?.replaceAll(',', '');
              return this._shortNumber.transform(number, this.dataMax);
            },
            style: {
              fontSize: that.isBold ? '14px' : '14px',
              color: this._filterService.getColor(2),
            },
          },
          opposite: false,
          plotBands: that.plotBandsY?.length ? that.plotBandsY : [{ visible: false }],
          plotLines: that.plotLinesY?.length ? that.plotLinesY : [{ visible: false }],
          tickInterval: that.tickIntervalY ?? undefined
        },
        {
          visible:
            this.isMultiYaxis && this.chartData.length > 3 ? true : false, // Hide the y-axis
          gridLineDashStyle: 'Dash',
          gridLineWidth: 1,
          lineWidth: this.isMultiYaxis ? 2 : 0,
          lineColor: this._filterService.getColor(3),
          offset: 180,
          title: {
            text: this.isMultiYaxis
              ? this.chartData[3]?.yAxisTitle
              : that.yaxisLabel,
            style: {
              fontSize: that.isBold ? '15px' : '14px',
              fontWeight: that.isBold ? '800' : '600',
              color: this._filterService.getColor(3),
            },
          },
          labels: {
            formatter: (value: any) => {
              this.dataMax = value.axis.dataMax;
              const number = this._number
                .transform(+value.value, '1.0-2')
                ?.replaceAll(',', '');
              return this._shortNumber.transform(number, this.dataMax);
            },
            style: {
              fontSize: that.isBold ? '14px' : '14px',
              color: this._filterService.getColor(3),
            },
          },
          opposite: true,
          plotBands: that.plotBandsY?.length ? that.plotBandsY : [{ visible: false }],
          plotLines: that.plotLinesY?.length ? that.plotLinesY : [{ visible: false }],
          tickInterval: that.tickIntervalY ?? undefined
        },
      ],
      series: this.chartData,
      exporting: {
        enabled: false
      }
    };

    if (this.width && this.chartOptions.chart) {
      this.chartOptions.chart['width'] = this.width;
    }
    if (!this.isMultiYaxis && this.chartOptions.chart) {
      this.chartOptions.chart.marginRight = 30;
    }
    // if (!this.isDashboardCard && this.chartOptions.chart) {
    //   this.chartOptions.chart.spacingBottom=100;
    // }
  }

  formatDateByStrftime(date: Date, format: string): string {
    return format
      .replace(/%Y/g, date.getUTCFullYear().toString()) // 4-digit year
      .replace(/%y/g, date.getUTCFullYear().toString().slice(-2)) // 2-digit year
      .replace(/%b/g, date.toLocaleString('en-US', { month: 'short' })) // Jan
      .replace(/%B/g, date.toLocaleString('en-US', { month: 'long' }))  // January
      .replace(/%m/g, ('0' + (date.getUTCMonth() + 1)).slice(-2)) // 01-12
      .replace(/%d/g, ('0' + date.getUTCDate()).slice(-2)) // 01-31
      .replace(/%e/g, date.getUTCDate().toString()) // 1-31 (no leading 0)
      .replace(/%H/g, ('0' + date.getUTCHours()).slice(-2))
      .replace(/%M/g, ('0' + date.getUTCMinutes()).slice(-2));
  }



  getFormattedValue(labelData: any, dataMax: number) {
    let value: any = labelData;
    if (this.decimalCount(value) > 2) {
      value = value.toFixed(2);
    }
    value = this._shortNumber.transform(value, dataMax, 2);
    return value;
  }

  zoom(type: string) {
    const currentMin = this.chartXaxis.min;
    const currentMax = this.chartXaxis.max;
    if (currentMin !== null && currentMax !== null) {
      const newMin =
        type == 'in'
          ? currentMin + (currentMax - currentMin) * 0.065
          : currentMin - (currentMax - currentMin) * 0.065;
      const newMax =
        type == 'in'
          ? currentMax - (currentMax - currentMin) * 0.065
          : currentMax + (currentMax - currentMin) * 0.065;
      this.chartXaxis.setExtremes(newMin, newMax);
    }
  }

  fullscreen(_value: any = '') {
    this.chartXaxis.chart.fullscreen.toggle();
  }

  toggleToolTip(event: any) {
    this.isToolTip = event;
    this.createChart();
  }

  showFullValue(event: any) {
    this.preciseValue = event;
    this.createChart();
  }

  toggleDataLabel(event: any) {
    this.isDatalabel = event;
    this.createChart();
  }

  decimalCount(number: number) {
    const numberAsString = number.toString();
    if (numberAsString.includes('.')) {
      return numberAsString.split('.')[1].length;
    }
    return 0;
  }

  charData(event: Highcharts.Chart) {
    this.chartInstance = event;
  }
}
