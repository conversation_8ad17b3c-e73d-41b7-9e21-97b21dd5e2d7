@use "../../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}

:host ::ng-deep {
  .customMarkdown{
    h2,
    h4 {
      font-weight: $fw-semi-bold;
      margin: $spacer-0 $spacer-0 $spacer-2;
    }
    p {
      margin-top: $spacer-0;

    }
    li {
      text-align: start;
    }
    ul {
      margin-left: $spacer-0;
      padding-left:  $spacer-0;
      margin-right: $spacer-6;
      transform: translateX($spacer-6);
    }
       strong {
        color: $ifp-color-black;
      }
  }
 .hidden {
  display: none;
 }
 .btn-default[data-handler="bootstrap-markdown-cmdUrl"] {
  display: none;
 }
 .fa {
  font-size: 14px; /* Adjust icon size */
}
textarea {
  background: $ifp-color-white;
  border-bottom:0px;
}
.md-editor {
  border-radius: 10px;
  overflow: hidden;
  &.active  {
    display: block;
    border: 1px solid #ddd;
        border-color: none;
        outline: 0;
       -webkit-box-shadow: none;
        box-shadow:none;
  }
  &textarea {
    font-family: inherit;
    border-radius: 10px;
  }

}

}
.ifp-mark-down-editor {
  &__preview {
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    padding: $spacer-2 $spacer-3 $spacer-3;
    background-color: $ifp-color-white;
    margin-top: $spacer-3;
  }
  &__preview-text {

    max-height: 200px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
    position: relative;
  }
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    padding: $spacer-2 $spacer-2 $spacer-3;
  }
}
