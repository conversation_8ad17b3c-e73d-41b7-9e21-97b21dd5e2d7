import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Output, ElementRef, OnDestroy, OnInit, ViewChild, Input, SimpleChanges, viewChild, inject } from '@angular/core';
import { CommonService } from '../../common.service';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { CommonModule, DatePipe, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { geoMapKeys } from '../../geospatial.contants';
import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDragPlaceholder, CdkDropList, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { InsightChartComponent } from './insight-chart/insight-chart.component';
import { EmptyChartComponent } from './empty-chart/empty-chart.component';
import { LoadingChartComponent } from './loading-chart/loading-chart.component';
import { GridsterModule } from 'angular-gridster2';
import { IfpDropdownComponent } from '../../../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpButtonComponent } from '../../../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpRangeSliderComponent } from '../../../ifp-widgets/ifp-molecules/ifp-range-slider/ifp-range-slider.component';
import { CensusServiceService } from 'src/app/scad-insights/geospatial-revamp/census-service.service';
import { cloneDeep, isArray } from 'lodash';
import { ActivatedRoute, Router } from '@angular/router';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';


interface Security {
  id: number;
  name: string;
  label: string;
}

interface IndicatorList {
  title: string;
  indicator_value: string;
  domain_id: number;
  card_position: string; // Or 'left' | 'right' | 'leftCenter' | 'rightCenter'
  light_icon: string;
  dark_icon: string;
  security: Security; // Or 'public' | 'private'
}


type IndicatorsList = Record<string, IndicatorList>;

@Component({
  selector: 'ifp-geo-indicators',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    InsightChartComponent,
    GridsterModule,
    TranslateModule,
    NgClass, CdkDrag, CdkDragHandle,
    CdkDropList,
    CdkDragPlaceholder,
    IfpDropdownComponent,
    IfpButtonComponent,
    IfpRangeSliderComponent,
    EmptyChartComponent,
    LoadingChartComponent,
    DatePipe
  ],
  templateUrl: './indicators-component.component.html',
  styleUrl: './indicators-component.component.scss'
})
export class IndicatorsComponent implements OnInit, AfterViewInit, OnDestroy {

  private readonly _themeService: ThemeService = inject(ThemeService);

  @Output() regionChangedFromChart = new EventEmitter();

  @ViewChild('leftList') leftList!: any;
  @ViewChild('rightList') rightList!: any;


  @ViewChild('leftScrollDiv') leftScrollDiv!: ElementRef;
  @ViewChild('customOneScroll') customOneScroll!: ElementRef;
  @ViewChild('customTwoScroll') customTwoScroll!: ElementRef;
  @ViewChild('rightScrollDiv') rightScrollDiv!: ElementRef;
  @ViewChild('leftIndicatorsDiv') leftIndicatorsDiv!: ElementRef;
  @ViewChild('rightIndicatorsDiv') rightIndicatorsDiv!: ElementRef;


  @Input() filterObject: any = {};
  @Input() lastQuarter: string = '';
  @Input() lastYear: number = 0;
  @Input() indicatorsList: IndicatorsList = {};
  @Input() indicatorsListValues: any = {};

  @Input() leftIndicators: any = {};
  @Input() rightIndicators: any = {};
  @Input() leftCenterIndicators: any = {};
  @Input() rightCenterIndicators: any = {};

  @Output() actionChange = new EventEmitter();
  @Output() triggerResetFilter = new EventEmitter();
  @Output() CustomizeHide = new EventEmitter();


  public isActionMode: string = '';
  public geoMapKeys = geoMapKeys;

  public defaultQueryParams = geoMapKeys.defaultQueryParams;

  public filteredLeftIndicators: any = {};
  public filteredRightIndicators: any = {};
  public filteredLeftCenterIndicators: any = {};
  public filteredRightCenterIndicators: any = {};
  private leftIndicatorBackup: any = {};
  public rightIndicatorBackup: any = {};
  public leftCenterIndicatorsBackup: any = {};
  public rightCenterIndicatorsBackup: any = {};



  public scrollAmount = 100;
  public backUpOpacity: any;
  public jobAgeGroup: any = [];

  public leftIndicatorCards: string[] = ['left'];
  public rightIndicatorCards: string[] = ['right'];
  public leftIndicatorCardsOne: string[] = [];
  public rightIndicatorCardsOne: string[] = [];
  public housingUnitCompareChartData: any = [];
  public deletedCards: Record<string, {
    leftCard: boolean, rightCard: boolean,
    leftCardSecond: boolean, rightCardSecond: boolean
  }> = {};

  public insightChartStats: any = [];
  public filteredLeftChartStats: any = {};
  public filteredLeftCenterChartStats: any = {};
  public filteredRightChartStats: any = {};
  public filteredRightCenterChartStats: any = {};
  // backup for stats
  public filteredLeftChartStatsBackup: any = {};
  public filteredLeftCenterChartStatsBackup: any = {};
  public filteredRightChartStatsBackup: any = {};
  public filteredRightCenterChartStatsBackup: any = {};

  public eodPopulation: number = 0;
  public totalPopulation: number = 0;
  public totalPopulation15years: number = 0;
  public totalNonCitizinPopulation: number = 0;
  public totalBuildings: number = 0;
  public totalUnits: number = 0;
  public totalJobSeekersEmirati: number = 0;
  public saveUsed: boolean = false;

  private subscription = new Subscription();

  title: string = geoMapKeys.abudhabiEmirate;
  loading: boolean = true;
  loadingChartStats: boolean = true;
  piechartLoading = false;
  barChartLoading: boolean = false;

  public domain_id: number = geoMapKeys.defaultDomain;
  public bgOpacity: number = 60;
  public cardsBg: string = '255, 255, 255';
  public buttonClass = buttonClass;
  public userAccessPermission: any = {};
  public canScrollLeftUp: boolean = false;
  public canScrollRightUp: boolean = false;
  public filterApplied: boolean = false;
  public defualtFilter: any;

  public isLeftAtTop: boolean = true;
  public isRightAtTop: boolean = true;
  public isLeftAtBottom: boolean = false;
  public isRightAtBottom: boolean = false;
  public language: string = 'en';

  public geoConfig: Record<string, any> = {
    opacity: 60
  }

  constructor(
    private commonService: CommonService,
     private themeService: ThemeService,
    private _cdr: ChangeDetectorRef,
    private gisSharedService: CensusServiceService,
    private _datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router
  ) {this.themeService.defaultLang$.subscribe((lang: string) => {
      this.language = lang;
    }) } 

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      this.domain_id = Number(params.get('domain_id'));
    });
    this.gisSharedService._geoMapAccessPermission$.subscribe((userAccessPermission: any) => {
      this.userAccessPermission = userAccessPermission;
    });
    const localStorageData = this.commonService.getLocalStorageData();
    const storedData = localStorageData ? JSON.parse(localStorageData) : {};
    this.filteredLeftChartStats = cloneDeep(storedData?.leftIndicators ?? this.filteredLeftChartStats);
    this.filteredRightCenterChartStats = cloneDeep(storedData?.centerLeftIndicators ?? this.filteredRightCenterChartStats);
    this.filteredLeftCenterChartStats = cloneDeep(storedData?.centerRightIndicators ?? this.filteredLeftCenterChartStats);
    this.filteredRightChartStats = cloneDeep(storedData?.rightIndicators ?? this.filteredRightChartStats);
    this.bgOpacity = storedData?.opacity ?? this.bgOpacity;
    this.defualtFilter = cloneDeep(geoMapKeys.defaultQueryParams)
    this.changeDomain(this.domain_id);
  }


  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void { }


  /**
  *
  * @param data Load population & laber force indicators
  */
  private loadPopulationSummary(data: any) {
    this.loading = true;
    this.loadingChartStats = true;
    const popSub = this.commonService.getPopulationSummary(data)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe({
        next: (data: any) => {
          this.getFiltredSummary(data);
          this.loadingChartStats = false;
          this.commonService.setChartData(data.summary);
        },
        error: (error) => {
          console.error('Error loading popluations:', error);
        }
      });
    if (this.filterObject) {
      // this.commonService.isCompareEnabled = this.filterObject?.QUARTER_CODE?.length > 1;
      this.commonService.isCompareEnabled = this.filterObject.YEAR_CODE?.length > 1;
    }
    this.subscription.add(popSub);
  }


  /**
   *
   * @param data  laber force indicators
   */
  private loadLaberForceSummary(data: any) {
    this.loading = true;
    this.loadingChartStats = true;
    const popSub = this.commonService.getLaborForceSummary(data)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      ?.subscribe({
        next: (data: any) => {
          this.getFiltredSummary(data);
          this.loadingChartStats = false;
          this.commonService.setChartData(data.summary);

        },
        error: (error) => {
          console.error('Error loading laber force data :', error);
        }
      });

    if (this.filterObject) {
      // this.commonService.isCompareEnabled = this.filterObject.QUARTER_CODE?.length > 1;
      this.commonService.isCompareEnabled = this.filterObject.YEAR_CODE?.length > 1;
    }
    this.subscription.add(popSub);
  }


  /**
   *
   * @param data Load real estate indicators
   */
  private loadRealStateSummary(data: any) {
    this.loading = true;
    this.loadingChartStats = true;
    const reSub = this.commonService.getRealEstateSummary(data)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      ?.subscribe({
        next: (data: any) => {
          this.getFiltredSummary(data);
          this.loadingChartStats = false;
          this.commonService.setChartData(data.summary);
        },
        error: (error) => {
          console.error('Error loading real state:', error);
        }
      });

    if (this.filterObject) {
      // this.commonService.isCompareEnabled = this.filterObject.QUARTER_CODE?.length > 1;
      this.commonService.isCompareEnabled = this.filterObject.YEAR_CODE?.length > 1;
    }

    this.subscription.add(reSub);
  }


  /**
   *
   * @param data get filtered data based on indicators and current domain
   */
  public getFiltredSummary(data: any) {

    // this.filteredRightChartStats[this.domain_id] = [];
    // this.filteredLeftChartStats[this.domain_id] = [];
    // this.filteredRightCenterChartStats[this.domain_id] = [];
    // this.filteredLeftCenterChartStats[this.domain_id] = [];

    if (this.domain_id != 3) {
      this.totalPopulation = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.TOTAL)?.POPULATION;
      this.eodPopulation = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.EOAD)?.POPULATION;
      this.totalNonCitizinPopulation = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_NON_CITIZEN)?.POPULATION;
      this.totalPopulation15years = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.POPULATION_OVER_15)?.POPULATION;
      this.totalJobSeekersEmirati = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.TOTAL_JOB_SEEKERS_EMIRATI)?.POPULATION;

      // Hide Top 10 Nationalities and Top 3 Relegons except for user with permission
      if (!this.userAccessPermission.nationalityAccess) {
        data.summary = data.summary.filter((x: { INDICATOR: string; }) => x.INDICATOR != this.indicatorsListValues.POPULATION_BY_NATIONALITY);
      }
      if (!this.userAccessPermission.religionAccess) {
        data.summary = data.summary.filter((x: { INDICATOR: string; }) => x.INDICATOR != this.indicatorsListValues.POPULATION_BY_RELIGION);
      }

    } else {
      this.totalBuildings = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.BUILDING_TOTAL)?.VALUE;
      this.totalUnits = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsListValues.UNIT_TOTAL)?.VALUE;
    }
    // group data based on indicator
    const groupedData = data.summary.reduce((acc: any, item: any) => {
      const { INDICATOR } = item;
      if (!acc[INDICATOR]) {
        acc[INDICATOR] = [];
      }
      acc[INDICATOR].push(item);
      return acc;
    }, {});

    if (this.domain_id == 2) {
      groupedData['TOTAL_EMP'] = groupedData['TOTAL']

    }

    this.insightChartStats = Object.keys(groupedData).map(indicator => ({
      indicator,
      data: groupedData[indicator]
    }));


    // get right indicators
    if (this.filteredRightChartStatsBackup[this.domain_id]?.length > this.filteredRightChartStats[this.domain_id]?.length) {
      const rightIndicators = this.filteredRightChartStatsBackup[this.domain_id]?.length ? this.filteredRightChartStatsBackup[this.domain_id] : this.rightIndicators;
      this.filteredRightIndicators[this.domain_id] = rightIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));
    } else {
      const rightIndicators = this.filteredRightChartStats[this.domain_id]?.length ? this.filteredRightChartStats[this.domain_id] : this.rightIndicators;
      this.filteredRightIndicators[this.domain_id] = rightIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));
    }

    // if (Object.keys(this.filteredRightChartStatsBackup).length && Array.isArray(this.filteredRightChartStatsBackup[this.domain_id])) {
    //   this.filteredRightIndicators[this.domain_id] = this.filteredRightChartStatsBackup[this.domain_id];
    // } else {
    //   this.filteredRightIndicators[this.domain_id] = this.rightIndicators;
    // }


    // get left indicators
    if (this.filteredLeftChartStatsBackup[this.domain_id]?.length > this.filteredLeftChartStats[this.domain_id]?.length) {
      const leftIndicators = this.filteredLeftChartStatsBackup[this.domain_id]?.length ? this.filteredLeftChartStatsBackup[this.domain_id] : this.leftIndicators;
      this.filteredLeftIndicators[this.domain_id] = leftIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));
    } else {
      const leftIndicators = this.filteredLeftChartStats[this.domain_id]?.length ? this.filteredLeftChartStats[this.domain_id] : this.leftIndicators;
      this.filteredLeftIndicators[this.domain_id] = leftIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));
    }

    // if (Object.keys(this.filteredLeftChartStats).length && Array.isArray(this.filteredLeftChartStats[this.domain_id])) {
    //   this.filteredLeftIndicators[this.domain_id] = this.filteredLeftChartStats[this.domain_id];
    // } else {
    //   this.filteredLeftIndicators[this.domain_id] = this.leftCenterIndicators;
    // }



    if (Object.keys(this.filteredLeftCenterChartStats).length && Array.isArray(this.filteredLeftCenterChartStats[this.domain_id])) {
      this.filteredLeftCenterIndicators[this.domain_id] = this.filteredLeftCenterChartStats[this.domain_id];
    } else {
      this.filteredLeftCenterIndicators[this.domain_id] = this.leftCenterIndicators;
    }

    // get right center indicators
    // if (this.filteredRightCenterChartStatsBackup[this.domain_id]?.length > this.filteredRightCenterChartStats[this.domain_id]?.length) {
    //   const rightCenterIndicators = this.filteredRightCenterChartStatsBackup[this.domain_id]?.length ? this.filteredRightCenterChartStatsBackup[this.domain_id] : this.rightCenterIndicators;
    //   this.filteredRightCenterIndicators[this.domain_id] = rightCenterIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));
    // }
    // else {
    //   const rightCenterIndicators = this.filteredRightCenterChartStats[this.domain_id]?.length ? this.filteredRightCenterChartStats[this.domain_id] : this.rightCenterIndicators;
    //   this.filteredRightCenterIndicators[this.domain_id] = rightCenterIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));
    // }
    if (Object.keys(this.filteredRightCenterChartStats).length && Array.isArray(this.filteredRightCenterChartStats[this.domain_id])) {
      this.filteredRightCenterIndicators[this.domain_id] = this.filteredRightCenterChartStats[this.domain_id];
    } else {
      this.filteredRightCenterIndicators[this.domain_id] = this.rightCenterIndicators;
    }


    // get left indicators

    // Apply filtering to the left and right indicators
    // if (this.filteredLeftChartStats[this.domain_id]?.length) {
    //   this.filteredLeftIndicators[this.domain_id] = this.filterIndicators(this.filteredLeftIndicators[this.domain_id], this.filteredLeftChartStats[this.domain_id]);
    // }

    // if (this.filteredLeftCenterChartStats[this.domain_id]?.length) {
    //   this.filteredLeftCenterIndicators[this.domain_id] = this.filterIndicators(this.filteredLeftCenterIndicators[this.domain_id], this.filteredLeftCenterChartStats[this.domain_id]);
    // }

    // if (this.filteredRightChartStats[this.domain_id]?.length) {
    //   this.filteredRightIndicators[this.domain_id] = this.filterIndicators(this.filteredRightIndicators[this.domain_id], this.filteredRightChartStats[this.domain_id]);
    // }
    // if (this.filteredRightCenterChartStats[this.domain_id]?.length) {
    //   this.filteredRightCenterIndicators[this.domain_id] = this.filterIndicators(this.filteredRightCenterIndicators[this.domain_id], this.filteredRightCenterChartStats[this.domain_id]);
    // }

    // Map the indicators to the respective chart stats
    this.filteredLeftChartStats[this.domain_id] = this.mapIndicatorsToCharts(this.filteredLeftIndicators[this.domain_id]);
    this.filteredLeftCenterChartStats[this.domain_id] = this.mapIndicatorsToCharts(this.filteredLeftCenterIndicators[this.domain_id]);
    this.filteredRightChartStats[this.domain_id] = this.mapIndicatorsToCharts(this.filteredRightIndicators[this.domain_id]);
    this.filteredRightCenterChartStats[this.domain_id] = this.mapIndicatorsToCharts(this.filteredRightCenterIndicators[this.domain_id]);

    // take bacup from right indicators stats
    if (this.filteredRightChartStats[this.domain_id]?.length == this.filteredRightChartStatsBackup[this.domain_id]?.length ||
      !this.filteredRightChartStatsBackup[this.domain_id]?.length) {
      this.filteredRightChartStatsBackup[this.domain_id] = this.filteredRightChartStats[this.domain_id];
    }

    // take bacup from left indicators stats
    if (this.filteredLeftChartStats[this.domain_id]?.length == this.filteredLeftChartStatsBackup[this.domain_id]?.length ||
      !this.filteredLeftChartStatsBackup[this.domain_id]?.length) {
      this.filteredLeftChartStatsBackup[this.domain_id] = this.filteredLeftChartStats[this.domain_id];
    }

    // take bacup from left center indicators stats
    if (this.filteredLeftCenterChartStats[this.domain_id]?.length == this.filteredLeftCenterChartStatsBackup[this.domain_id]?.length ||
      !this.filteredLeftCenterChartStatsBackup[this.domain_id]?.length) {
      this.filteredLeftCenterChartStatsBackup[this.domain_id] = this.filteredLeftCenterChartStats[this.domain_id];
    }

    // take bacup from right center indicators stats
    if (this.filteredRightCenterChartStats[this.domain_id]?.length == this.filteredRightCenterChartStatsBackup[this.domain_id]?.length ||
      !this.filteredRightCenterChartStatsBackup[this.domain_id]?.length) {
      this.filteredRightCenterChartStatsBackup[this.domain_id] = this.filteredRightCenterChartStats[this.domain_id];
    }
    this.setFilters();
  }

  setFilters() {
    // Reset isHide for all right chart stats
    this.filteredRightChartStats[this.domain_id].forEach((element: { isHide?: boolean }) => {
      element.isHide = false;
    });

    const hideMap: { [key: string]: string } = {
      MARITAL_CODE: 'EDUCATION',
      ATTAINMENT_CODE: 'MARITAL'
    };

    Object.entries(hideMap).forEach(([filterKey, indicatorKey]) => {
      if (this.filterObject[filterKey]?.length > 0) {
        const indicator = this.filteredRightChartStats[this.domain_id].find(
          (x: { indicator: string }) => x.indicator === indicatorKey
        );
        if (indicator) {
          indicator.isHide = true;
        }
      }
    });

  }

  filterIndicators(indicators: any[] = [], chartStats: any[] = []): any[] {
    return indicators.filter(({ indicator }) => (chartStats.length ? chartStats : indicators).some(stat => stat.indicator === indicator)
    );
  }

  mapIndicatorsToCharts(indicators: any[]): any[] {
    return indicators
      .map((indicator: any) => ({
        indicator: indicator.indicator,
        domains: indicator.domains,
        charts: this.insightChartStats.filter(
          (stat: any) => stat.indicator === indicator.indicator
        )
      }))
      .filter(indicator => indicator.charts.length > 0); // Only keep indicators with charts
  }

  isObjectEmpty(obj: object): boolean {
    return Object.keys(obj).length === 0;
  }


  setCardAction(action: string) {
    this.isActionMode = action;
    if (this.isActionMode == geoMapKeys.customize) {
      this.leftIndicatorBackup = cloneDeep(this.filteredLeftChartStats[this.domain_id]);
      this.rightIndicatorBackup = cloneDeep(this.filteredRightChartStats[this.domain_id]);
      this.leftCenterIndicatorsBackup = cloneDeep(this.filteredRightCenterChartStats[this.domain_id]);
      this.rightCenterIndicatorsBackup = cloneDeep(this.filteredLeftCenterChartStats[this.domain_id]);
      this.backUpOpacity = cloneDeep(this.bgOpacity);
    }
  }


  drop(event: CdkDragDrop<string[]>) {
    
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
  }


  deleteSection(type: string) {
    if (!this.deletedCards[this.domain_id]) {
      this.deletedCards[this.domain_id] = {
        leftCard: false,
        rightCard: false,
        leftCardSecond: false,
        rightCardSecond: false
      };
    }
    if (type == 'left') {
      this.deletedCards[this.domain_id].leftCardSecond = cloneDeep(this.deletedCards?.[this.domain_id]?.leftCard);
      this.deletedCards[this.domain_id].leftCard = !this.deletedCards?.[this.domain_id]?.leftCard;
    } else {
      this.deletedCards[this.domain_id].rightCardSecond = cloneDeep(this.deletedCards?.[this.domain_id]?.rightCard);
      this.deletedCards[this.domain_id].rightCard = !this.deletedCards?.[this.domain_id].rightCard;
    }
  }



  scroll(type: string, side: string): void {
    console.log("this.leftIndicatorsDiv.nativeElement",)
    // if (this.isActionMode === geoMapKeys.customize && side != 'customOne' && side != 'customTwo') {
    //   return;
    // }
    let element: any;
    if (side == 'left') {
      element = this.leftScrollDiv.nativeElement;
    } else if (side == 'customOne') {
      element = this.customOneScroll.nativeElement;
    } else if (side == 'customTwo') {
      element = this.customTwoScroll.nativeElement;
    } else {
      element = this.rightScrollDiv.nativeElement;
    }

    //console.log("this.leftIndicatorsDiv.nativeElement.offsetHeight", this.leftIndicatorsDiv.nativeElement.offsetHeight)

    if (side == 'left') {
      if (element.scrollTop == 0) {
        this.canScrollLeftUp = false;
      }
    }

    if (side == 'right') {
      if (element.scrollTop == 0) {
        this.canScrollRightUp = false;
      } else {
        this.canScrollRightUp = true;
      }
    }

    element.scrollTop = type == 'up' ? element.scrollTop + 50 : element.scrollTop - 50;
  }

  setBgTransparent(opacity: number) {
    this.bgOpacity = opacity;
  }

  saveChanges() {
    this.geoConfig['opacity'] = this.bgOpacity;
    this.actionChange.emit({ event: 'save', config: this.geoConfig });
    this.isActionMode = 'save';
    const storData = {
      leftIndicators: this.filteredLeftChartStats,
      centerLeftIndicators: this.filteredRightCenterChartStats,
      centerRightIndicators: this.filteredLeftCenterChartStats,
      rightIndicators: this.filteredRightChartStats,
      opacity: this.bgOpacity
    };
    this.commonService.setLocalStorage(storData);
    this._themeService.geoConfig.set(this.geoConfig);
    this.saveUsed = true;
  }

  cancelCustomize() {
    // temporary fix, to be revised later 
    window.location.reload();
    // if (!this.deletedCards[this.domain_id]) {
    //   this.deletedCards[this.domain_id] = {
    //     leftCard: false,
    //     rightCard: false,
    //     leftCardSecond: false,
    //     rightCardSecond: false
    //   };
    // }
    // this.filteredLeftChartStats[this.domain_id] = cloneDeep(this.leftIndicatorBackup);
    // this.filteredRightChartStats[this.domain_id] = cloneDeep(this.rightIndicatorBackup);
    // this.filteredRightCenterChartStats[this.domain_id] = cloneDeep(this.rightCenterIndicatorsBackup);
    // this.filteredLeftCenterChartStats[this.domain_id] = cloneDeep(this.leftCenterIndicatorsBackup);
    // //this.rightCenterIndicatorsBackup = cloneDeep(this.rightCenterIndicatorsBackup);
    // this.bgOpacity = cloneDeep(this.backUpOpacity);
    // this.isActionMode = 'cancel';
    // this.actionChange.emit(this.isActionMode);
  }


  applyFilter(filter: any) {
    this.filterObject = filter;
    this.filterApplied = false;
    if (this.domain_id == 1) {
      this.loadPopulationSummary(this.filterObject);
    } else if (this.domain_id == 2) {
      this.loadLaberForceSummary(this.filterObject);
    } else if (this.domain_id == 3) {
      this.loadRealStateSummary(this.filterObject);
    }
    // Loop through filterObject and log each key-value pair
    for (const key in this.filterObject) {
      if (!Object.prototype.hasOwnProperty.call(this.filterObject, key)) continue;
      const defaultValue = this.defualtFilter[key as keyof typeof this.defualtFilter];
      const filterValue = this.filterObject[key];
      if (Array.isArray(defaultValue) && Array.isArray(filterValue)) {
        if (filterValue.length !== defaultValue.length || filterValue.some((v: any) => !defaultValue.includes(v))) {
          this.filterApplied = true;
          break;
        }
      }
    }
  }

  changeDomain(event: any) {
    this.defaultQueryParams.YEAR_CODE = [this.lastYear.toString()];
    // force Q3 as the base Quarters
    this.defaultQueryParams.QUARTER_CODE = this.lastQuarter == 'Q4' ? ['Q3'] : [this.lastQuarter];
    if (event == 1) {
      this.loadPopulationSummary(this.defaultQueryParams);
    } else if (event == 2) {
      this.loadLaberForceSummary(this.defaultQueryParams);
    } else if (event == 3) {
      this.loadRealStateSummary(this.defaultQueryParams);
    } else {
      this.router.navigate(['/404']);
    }
    this.domain_id = event;
  }

  onScroll(event: Event, position: string): void {
    const target = event.target as HTMLElement;
    const scrollHeight = target.scrollHeight;
    const scrollTop = target.scrollTop;
    const clientHeight = target.clientHeight + 2;
    const currentPos = Math.round(scrollTop + clientHeight);

    const atBottom = currentPos >= scrollHeight;
    const atTop = scrollTop === 0;

    if (position === 'left') {
      this.isLeftAtTop = atTop;
      this.isLeftAtBottom = atBottom;
    } else if (position === 'right') {
      this.isRightAtTop = atTop;
      this.isRightAtBottom = atBottom;
    }
  }

  regionChangeFromChart(event: any) {
    this.regionChangedFromChart.emit(event);
  }

  resetFilters(event: any) {
    this.triggerResetFilter.emit();
  }

}

