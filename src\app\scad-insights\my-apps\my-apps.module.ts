import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MyAppsRoutingModule } from './my-apps-routing.module';
import { MyAppsComponent } from 'src/app/scad-insights/my-apps/my-apps/my-apps.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCardComponent } from '../ifp-widgets/ifp-atoms/ifp-card/ifp-card.component';
import { IfpStepperComponent } from '../ifp-widgets/ifp-atoms/ifp-stepper/ifp-stepper.component';
import { DomainCardComponent } from '../ifp-widgets/ifp-atoms/domain-card/domain-card.component';
import { LandingPageComponent } from './landing-page/landing-page.component';
import { IfpBreadcrumbsComponent } from '../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { myAppReducer } from './store/myapp.state';
import { MyAppsListEffects } from './store/myappsList/myappsList.effects';
import { MyAppsAddEffects } from './store/AddMyApps/addMyApps.effects';
import { DraftEffects } from './store/draft-store/draft.effects';
import { LandingPageNewComponent } from './landing-page-new/landing-page-new.component';
import { MyAppsNewComponent } from './my-apps new/my-apps-new.component';
 
@NgModule({
  declarations: [
    MyAppsComponent,
    MyAppsNewComponent
  ],
  imports: [
    CommonModule,
    MyAppsRoutingModule,
    TranslateModule,
    IfpCardComponent,
    IfpStepperComponent,
    DomainCardComponent,
    LandingPageComponent,
    LandingPageNewComponent,
    IfpBreadcrumbsComponent,
    EffectsModule.forFeature([MyAppsListEffects, MyAppsAddEffects, DraftEffects]),
    StoreModule.forFeature('myapps', myAppReducer)
  ]
})
export class MyAppsModule { }
